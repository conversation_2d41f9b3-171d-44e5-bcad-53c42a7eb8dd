<?xml version="1.0"?>
<package format="2">
  <name>planner</name>
  <version>0.0.0</version>
  <description>The planner package</description>
  <buildtool_depend>catkin</buildtool_depend>

  <maintainer email="<EMAIL>"><PERSON>ang<PERSON><PERSON></maintainer>

  <license>TODO</license>

  <build_depend>roslib</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>tf</build_depend>
  <build_depend>tracking_controller</build_depend>
  <build_depend>map_manager</build_depend>
  <build_depend>onboard_detector</build_depend>
  <build_depend>dynamic_predictor</build_depend>
  <build_depend>global_planner</build_depend>
  <build_depend>trajectory_planner</build_depend>
  <build_depend>opt_sfc</build_depend>
  <build_depend>gcopter</build_depend>
  <build_export_depend>roslib</build_export_depend>
  <build_export_depend>roscpp</build_export_depend>
  <build_export_depend>tf</build_export_depend>
  <build_export_depend>tracking_controller</build_export_depend>
  <build_export_depend>map_manager</build_export_depend>
  <build_export_depend>onboard_detector</build_export_depend>
  <build_export_depend>dynamic_predictor</build_export_depend>
  <build_export_depend>global_planner</build_export_depend>
  <build_export_depend>trajectory_planner</build_export_depend>
  <exec_depend>roslib</exec_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>tf</exec_depend>
  <exec_depend>tracking_controller</exec_depend>
  <exec_depend>map_manager</exec_depend>
  <exec_depend>onboard_detector</exec_depend>
  <exec_depend>dynamic_predictor</exec_depend>
  <exec_depend>global_planner</exec_depend>
  <exec_depend>trajectory_planner</exec_depend>

  <export>
  </export>
</package>
