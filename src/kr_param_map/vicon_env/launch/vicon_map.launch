<launch>

  <arg name="map_frame_id" default="map"/>
  <arg name="map_size_x" default="20"/>
  <arg name="map_size_y" default="20"/>
  <arg name="map_size_z" default="5"/>

  <arg name="vicon_topic" default="/vicon/all_obs_odom"/>
  <arg name="semantic_path"  default="$(find vicon_env)/semantics/3dcase2.csv"  />


  <node pkg ="vicon_env" name ="vicon_map3d" type ="vicon_map3d" output = "screen">    

    <param name="map/x_size"     value="$(arg map_size_x)" />
    <param name="map/y_size"     value="$(arg map_size_y)" />
    <param name="map/z_size"     value="$(arg map_size_z)" />
    <param name="map/resolution" value="0.1"/>        
    <param name="map/frame_id"     value="$(arg map_frame_id)" />   

    <param name="sensing/radius" value="20.0"/>
    <param name="sensing/rate"   value="20.0"/>

    <remap from="~vicon_all_obs" to="$(arg vicon_topic)"/>
    <param name="semantic_path" value="$(arg semantic_path)"/>

  </node>

  <arg name="rviz_config" default="$(find vicon_env)/launch/vicon.rviz"/>
  <node pkg="rviz"
    type="rviz"
    name="rviz"
    args="-d $(arg rviz_config)"/>

</launch>
