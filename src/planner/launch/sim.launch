<launch>
    <node type="rviz" name="rviz" pkg="rviz" args="-d $(find planner)/rviz/rviz_v2.rviz" />
    <param name="trajectory_type" value="astar" /> <!-- astar, poly, sfc -->

    <arg name="map_file" default="map2.txt"/> 
    <arg name="enable_DOB" default="0"/> <!-- 用于记录数据，不调试时不用开启-->
    <arg name="enable_MPCC" default="1"/>
    <arg name="max_v" default="6.0"/>
    <arg name="max_a" default="3.0"/>
    <arg name="mu" default="1.0"/>
    <arg name="enable_dynamic_planning" default="0"/>
    <arg name="value_of_disturbance_force" default="0"/>
    <node 
        type="planner_px4" 
        name="planner_px4" 
        pkg="planner" 
        args="$(find planner)/maps/$(arg map_file) $(arg enable_DOB) $(arg enable_MPCC) $(arg max_v) $(arg max_a) $(arg mu)" 
        output="screen" 
        launch-prefix="gnome-terminal -- gdb -ex run --args"
    />
</launch>