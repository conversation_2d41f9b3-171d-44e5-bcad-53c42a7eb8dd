<launch>
    <node type="rviz" name="rviz" pkg="rviz" args="-d $(find planner)/rviz/rviz_v2.rviz" />

    <arg name="map_file" default="map3.txt"/>
    <arg name="enable_DOB" default="1"/>  <!-- 用于记录数据，不调试时不用开启-->
    <arg name="enable_MPCC" default="1"/>
    <arg name="max_v" default="6.0"/>
    <arg name="max_a" default="3.0"/>
    <arg name="mu" default="0.5"/>
    <arg name="enable_dynamic_planning" default="0"/>
    <arg name="value_of_disturbance_force" default="0"/>
    <node 
        type="planner_px4_circle" 
        name="planner_px4_circle" 
        pkg="planner" 
        args="$(find planner)/maps/$(arg map_file) $(arg enable_DOB) $(arg enable_MPCC) $(arg max_v) $(arg max_a) $(arg mu)" 
        output="screen"
        launch-prefix="gnome-terminal -- gdb -ex run --args"
    />

</launch>