cmake_minimum_required(VERSION 3.0)
project(planner)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_BUILD_TYPE Release)
set(CMAKE_VERBOSE_MAKEFILE OFF)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mavx -mfma -ffast-math -march=native")
add_definitions(-DLINUX)
# add_definitions(-DUSE_SFC_TRAJ)

find_package(Python3 COMPONENTS Development NumPy)
find_package(Eigen3 REQUIRED)
# catkin_make在编译过程中会自动从/opt/ros/noetic/include/和/opt/ros/noetic/lib这两个目录下寻找头文件和库文件,因此并不需要find_packege寻找所有依赖的包
# ${catkin_INCLUDE_DIRS} 会包含/opt/ros/noetic/include和find_packege中依赖包的头文件目录
# ${catkin_LIBRARIES} 会包含/opt/ros/noetic/lib和find_packege中依赖包的库文件目录
find_package(catkin REQUIRED COMPONENTS roslib roscpp tf tracking_controller dynamic_predictor trajectory_planner map_manager global_planner opt_sfc gcopter)    

# 指明头文件路径方式：includePath  include_directories
include_directories(./third/ 
                    ${EIGEN3_INCLUDE_DIR} 
                    /usr/lib/ ${catkin_INCLUDE_DIRS}
                    src/ modules/
                    /usr/include/pcl-1.10/
                    ../kr_opt_sfc/opt_sfc/include/)    # 添加额外的头文件路径，包括SFC
link_directories(/usr/local/lib)  # 添加额外的库路径(Python3)

message("--------------------------------------------------------------------------------------------------------------------------------------------------------------------")
message(${catkin_INCLUDE_DIRS})
message("--------------------------------------------------------------------------------------------------------------------------------------------------------------------")
message(${catkin_LIBRARIES})
message("--------------------------------------------------------------------------------------------------------------------------------------------------------------------")

catkin_package()

add_executable(planner_px4
    src/planner_px4_main.cpp
    modules/mpcc/nominal_mpcc.cpp
    modules/map/map.cpp
    modules/ros_interface/ros_interface.cpp
    modules/kinodynamic_astar/kinodynamic_astar.cpp
    modules/bspline_opt/bspline_optimizer.cpp
    modules/px4_interface/px4_interface.cpp
    modules/MRPT/mrptmodule.cpp
    modules/MRPT/Mrpt.h
    modules/parabolic_airdrop/parabolic_airdrop.cpp)
target_link_libraries(planner_px4
    Python3::Python Python3::NumPy nlopt ${catkin_LIBRARIES}  
)

add_executable(planner_px4_circle
    src/planner_px4_circle_main.cpp
    modules/mpcc/nominal_mpcc.cpp
    modules/map/map.cpp
    modules/ros_interface/ros_interface.cpp
    modules/kinodynamic_astar/kinodynamic_astar.cpp
    modules/bspline_opt/bspline_optimizer.cpp
    modules/px4_interface/px4_interface.cpp
    modules/MRPT/mrptmodule.cpp
    modules/MRPT/Mrpt.h
    modules/parabolic_airdrop/parabolic_airdrop.cpp)
target_link_libraries(planner_px4_circle
    Python3::Python Python3::NumPy nlopt ${catkin_LIBRARIES}  
)

add_executable(map_viewer
    src/map_viewer.cpp
    modules/map/map.cpp
    modules/ros_interface/ros_interface.cpp)
target_link_libraries(map_viewer
    ${catkin_LIBRARIES}
)

add_executable(map_generator
    src/map_generator.cpp
    modules/map/map.cpp
    modules/ros_interface/ros_interface.cpp)
target_link_libraries(map_generator
    ${catkin_LIBRARIES}
)

add_executable(Mrpt_test
    src/Mrpt_test.cpp  
    modules/MRPT/mrptmodule.cpp
    modules/MRPT/Mrpt.h)
target_link_libraries(Mrpt_test
    Python3::Python Python3::NumPy ${catkin_LIBRARIES}  
)

add_executable(parameter_fitting
    src/parameter_fitting.cpp)
target_link_libraries(parameter_fitting
    -lstdc++ -lm
)


