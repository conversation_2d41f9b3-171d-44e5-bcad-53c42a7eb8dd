#include "numeric"
#include <ros/package.h>
#include "map/map.hpp"
#include "map/sdf.hpp"
#include "ros_interface/ros_interface.hpp"
#include "kinodynamic_astar/kinodynamic_astar.hpp"
#include "bspline/uniform_bspline.hpp"
#include "bspline_opt/bspline_optimizer.hpp"
#include "px4_interface/px4_interface.hpp"
#include "mpcc/nominal_mpcc.hpp"
#include "matplotlib/matplotlibcpp.hpp"
// #include "quadrotor_dynamics/quad_simulator.hpp"
#include "common/rotation_math.hpp"
#include "disturbance_observer/gpiobserver.hpp"
#include "logger/logger.hpp"
#include "tracker/pid_tracker.hpp"
#include "MRPT/Mrpt.h"
#include "parabolic_airdrop/parabolic_airdrop.hpp"
#include <geometry_msgs/PoseStamped.h>
#include <map_manager/dynamicMap.h>
#include <trajectory_planner/polyTrajOccMap.h>
#include <global_planner/rrtOccMap.h>
#include <opt_sfc/TrajectoryTarget.h>
#include <nav_msgs/Path.h>

namespace plt = matplotlibcpp;
constexpr int n_step = NominalMpcc::_n_step;
// 真机要改的数据 hover_rate, dsafe, pid参数

// SFC轨迹接收相关变量
vector<Vector3d> received_sfc_trajectory;
bool sfc_trajectory_received = false;

// SFC轨迹回调函数
void sfcTrajectoryCallback(const nav_msgs::Path::ConstPtr& msg) {
    received_sfc_trajectory.clear();

    for (const auto& pose : msg->poses) {
        Vector3d point(
            pose.pose.position.x,
            pose.pose.position.y,
            pose.pose.position.z
        );
        received_sfc_trajectory.push_back(point);
    }

    sfc_trajectory_received = true;
    ROS_INFO("Received SFC trajectory with %zu points", received_sfc_trajectory.size());
}

// 发布轨迹目标给SFC节点
void publishTrajectoryTarget(ros::Publisher& target_pub,
                           const Vector3d& start_pos, const Vector3d& start_vel, const Vector3d& start_acc,
                           const Vector3d& goal_pos, const Vector3d& goal_vel, const Vector3d& goal_acc,
                           bool enable_visualization = true) {
    opt_sfc::TrajectoryTarget target_msg;

    target_msg.header.stamp = ros::Time::now();
    target_msg.header.frame_id = "odom";

    // 设置起点信息
    target_msg.start_position.x = start_pos.x();
    target_msg.start_position.y = start_pos.y();
    target_msg.start_position.z = start_pos.z();

    target_msg.start_velocity.x = start_vel.x();
    target_msg.start_velocity.y = start_vel.y();
    target_msg.start_velocity.z = start_vel.z();

    target_msg.start_acceleration.x = start_acc.x();
    target_msg.start_acceleration.y = start_acc.y();
    target_msg.start_acceleration.z = start_acc.z();

    // 设置终点信息
    target_msg.goal_position.x = goal_pos.x();
    target_msg.goal_position.y = goal_pos.y();
    target_msg.goal_position.z = goal_pos.z();

    target_msg.goal_velocity.x = goal_vel.x();
    target_msg.goal_velocity.y = goal_vel.y();
    target_msg.goal_velocity.z = goal_vel.z();

    target_msg.goal_acceleration.x = goal_acc.x();
    target_msg.goal_acceleration.y = goal_acc.y();
    target_msg.goal_acceleration.z = goal_acc.z();

    // 设置可视化标志
    target_msg.enable_visualization = enable_visualization;

    target_pub.publish(target_msg);

    ROS_INFO("Published trajectory target: start(%.2f,%.2f,%.2f) -> goal(%.2f,%.2f,%.2f)",
             start_pos.x(), start_pos.y(), start_pos.z(),
             goal_pos.x(), goal_pos.y(), goal_pos.z());
}
// 等待SFC轨迹生成完成
bool waitForSFCTrajectory(ros::NodeHandle& nh, double timeout_seconds = 10.0) {
    ros::Time start_time = ros::Time::now();
    ros::Rate rate(10); // 10Hz检查频率

    while (ros::ok() && (ros::Time::now() - start_time).toSec() < timeout_seconds) {
        ros::spinOnce();
        if (sfc_trajectory_received) {
            return true;
        }
        rate.sleep();
    }

    ROS_WARN("Timeout waiting for SFC trajectory after %.1f seconds", timeout_seconds);
    return false;
}

vector<Vector3d> generatePolyTrajectory(
    shared_ptr<trajPlanner::polyTrajOccMap> polyTraj,
    shared_ptr<globalPlanner::rrtOccMap<3>> rrtPlanner,
    const Vector3d& first_pos,
    const Vector3d& first_vel,
    const Vector3d& first_acc,
    const Vector3d& second_pos,
    const Vector3d& second_vel,
    const Vector3d& second_acc,
    const Vector3d& offset,
    double t_sample) {
    
    // 设置起始和结束条件
    vector<Vector3d> startEndConditions;
    startEndConditions.push_back(first_vel); 
    startEndConditions.push_back(second_vel); 
    startEndConditions.push_back(first_acc); 
    startEndConditions.push_back(second_acc); 

    Vector3d first_pos_ = first_pos - offset;
    Vector3d second_pos_ = second_pos - offset;

    rrtPlanner->updateStart(first_pos_);
    rrtPlanner->updateGoal(second_pos_);
    nav_msgs::Path rrtPathMsgTemp;
    rrtPlanner->makePlan(rrtPathMsgTemp);
    polyTraj->updatePath(rrtPathMsgTemp, startEndConditions);
    polyTraj->makePlan(true); // 生成轨迹，带走廊约束	
    nav_msgs::Path inputPolyTraj = polyTraj->getTrajectory(t_sample);

    // 提取轨迹位置点到vector容器中
    vector<Vector3d> polyTrajPoints;
    polyTrajPoints.reserve(inputPolyTraj.poses.size()); // 预分配内存提高效率
    
    for (const auto& pose : inputPolyTraj.poses) {
        Vector3d point(
            pose.pose.position.x + offset(0),
            pose.pose.position.y + offset(1), 
            pose.pose.position.z + offset(2)    
        );
        polyTrajPoints.push_back(point);
    }
    
    return polyTrajPoints;
}

int main(int argc, char **argv) {
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(5, &mask);
    pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask);

    bool enable_dob = (argv[2][0] == '1');
    cout << (enable_dob == true ? "Enable DOB" : "Disable DOB") << endl;

    bool enable_mpcc = (argv[3][0] == '1');
    cout << (enable_mpcc == true ? "Enable MPCC" : "Disable MPCC") << endl;

    double MAX_VEL = atof(argv[4]);
    double MAX_ACC = atof(argv[5]);
    cout << "MAX_VEL: " << MAX_VEL << " MAX_ACC: " << MAX_ACC << endl;
    double mu = atof(argv[6]); // 倍速，越大无人机飞的越快
    cout << "mu: " << mu << endl;

    string log_path = ros::package::getPath("planner") + "/logs";
    Logger logger(string(enable_dob == true ? "enable-DOB" : "disable-DOB") + "-"
        + string(enable_mpcc == true ? "enable-MPCC" : "disable-MPCC") + "-test", log_path);


    ros::init(argc, argv, "planner_px4");
    ros::NodeHandle nh; // 创建 ROS 节点句柄

    // 从ROS参数服务器获取路径规划算法类型
    std::string trajectory_type;
    nh.param<std::string>("trajectory_type", trajectory_type, "astar"); // 默认使用astar
    bool use_astar_traj = (trajectory_type == "astar");
    bool use_poly_traj = (trajectory_type == "poly");
    bool use_sfc_traj = (trajectory_type == "sfc");
    ROS_INFO("Using trajectory type: %s", trajectory_type.c_str());

    // SFC相关的发布器和订阅器
    ros::Publisher sfc_target_pub;
    ros::Subscriber sfc_trajectory_sub;

    if (use_sfc_traj) {
        sfc_target_pub = nh.advertise<opt_sfc::TrajectoryTarget>("/sfc/trajectory_target", 1);
        sfc_trajectory_sub = nh.subscribe("/sfc/optimized_trajectory", 1, sfcTrajectoryCallback);
        ROS_INFO("SFC publishers and subscribers initialized");
    }

    //construct map
    GridMap gridmap(argv[1]);
    gridmap.update_grid_map();
    double size_x = gridmap.size()[0];
    double size_y = gridmap.size()[1];
    //PX4 interface
    Px4Interface px4(size_x, size_y);
    // QuadSimulator px4(0.309);

    //ros interface
    RosInterface ros_inte(gridmap.size());
    sleep(1);
    ros_inte.publish_grid_map(gridmap);
    
    //construct SDF map
    SdfMap sdfmap(gridmap.resolution() / 1.0, gridmap);
    ros_inte.publish_sdf_map(sdfmap);

    Vector3d offset = Vector3d(size_x / 2, size_y / 2, 0.0);
    Vector3d start_pos = Vector3d(-8.0, 0.0, 2.0) + offset; 
    Vector3d goal_pos = Vector3d(6.0, 0.0, 2.0) + offset;
    // Vector3d target_pos = px4.target_pos();
    Vector3d target_pos =  Vector3d(-1.0, 2.0, 0.1) + offset; 
    Vector3d target_pos_above =  target_pos + Vector3d(0.0, 0.0, 2.0);
    Vector3d throw_pos = Vector3d(0.0, 0.0, 0.0);
    Vector3d throw_vel = Vector3d(0.0, 0.0, 0.0);
    double t_sample = 0.05;
    double payload_offset = 0.055;
    double risk_dis = 0.45;

    shared_ptr<mapManager::dynamicMap> map;
    map.reset(new mapManager::dynamicMap(nh, false));
    shared_ptr<trajPlanner::polyTrajOccMap> polyTraj;
    shared_ptr<globalPlanner::rrtOccMap<3>> rrtPlanner;
    KinodynamicAstar kino_astar(gridmap);

    // Astar
    pair<vector<Vector3d>, vector<Vector3d>> Kino_path;
    pair<vector<Vector3d>, vector<Vector3d>> kino_path_first, kino_path_second;
    // Poly
    vector<Vector3d> polyTrajPoints;
    vector<Vector3d> polyTraj_first_, polyTraj_second_;
    // SFC
    vector<Vector3d> sfcTrajPoints;

    if (use_astar_traj) {
        kino_astar.set_param(
            10., //w_time 2.5
            MAX_VEL,  //max_vel
            MAX_ACC,  //max_acc
            1.0 + 1.0 / 10000, //tie_breaker
            1 / 2.0,  //acc_resolution
            1 / 2.0,      //time_resolution
            0.6,      //max_duration
            1 / 50.,   //safety_check_res
            10.0);     //lamda_heu
        Kino_path.first.clear();
        vector<Vector3d> waypoints = {
            start_pos,
            target_pos_above,
            goal_pos
        };
        for (int i = 0; i < waypoints.size() - 1; i++) {
            kino_astar.search(
                waypoints[i],
                {0, 0, 0},
                waypoints[i+1],
                {0, 0, 0});
            auto kino_path_ = kino_astar.get_sample_path(t_sample);
            Kino_path.first.insert(Kino_path.first.end(), kino_path_.first.begin(), kino_path_.first.end());
        }
        Kino_path.first.push_back(goal_pos);
    }
    
    if (use_poly_traj) {
        rrtPlanner.reset(new globalPlanner::rrtOccMap<3> (nh));
		rrtPlanner->setMap(map);

        polyTraj.reset(new trajPlanner::polyTrajOccMap(nh));
        polyTraj->setMap(map); 
        polyTraj->updateDesiredVel(MAX_VEL / 3.0);
        polyTraj->updateDesiredAcc(MAX_ACC / 3.0); 

        polyTrajPoints.clear();

        vector<Vector3d> polyTraj_first = generatePolyTrajectory(polyTraj, rrtPlanner,
                                            start_pos, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0), 
                                            target_pos_above, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                                            offset, t_sample);
        
        vector<Vector3d> polyTraj_second = generatePolyTrajectory(polyTraj, rrtPlanner,
                                            target_pos_above, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0), 
                                            goal_pos, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                                            offset, t_sample);
        polyTrajPoints = polyTraj_first;
        polyTrajPoints.insert(polyTrajPoints.end(), polyTraj_second.begin(), polyTraj_second.end());
    }

    if (use_sfc_traj) {
        // 发布第一段轨迹目标给SFC节点（不可视化）
        publishTrajectoryTarget(sfc_target_pub,
                              start_pos - offset, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                              target_pos_above - offset, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                              false);

        // 等待SFC轨迹生成完成
        if (waitForSFCTrajectory(nh, 10.0)) {
            vector<Vector3d> sfcTraj_first = received_sfc_trajectory;
            // 将轨迹坐标转换回全局坐标系
            for (auto& point : sfcTraj_first) {
                point += offset;
            }

            // 重置接收标志
            sfc_trajectory_received = false;

            // 发布第二段轨迹目标给SFC节点（不可视化）
            publishTrajectoryTarget(sfc_target_pub,
                                  target_pos_above - offset, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                                  goal_pos - offset, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                                  false);

            // 等待第二段SFC轨迹生成完成
            if (waitForSFCTrajectory(nh, 10.0)) {
                vector<Vector3d> sfcTraj_second = received_sfc_trajectory;
                // 将轨迹坐标转换回全局坐标系
                for (auto& point : sfcTraj_second) {
                    point += offset;
                }

                sfc_trajectory_received = false;

                // 合并两段轨迹
                sfcTrajPoints = sfcTraj_first;
                sfcTrajPoints.insert(sfcTrajPoints.end(), sfcTraj_second.begin(), sfcTraj_second.end());

                std::cout << "Successfully received SFC trajectory with " << sfcTrajPoints.size() << " points" << std::endl;
            } else {
                std::cout << "Failed to receive second SFC trajectory!" << std::endl;
            }
        } else {
            std::cout << "Failed to receive first SFC trajectory!" << std::endl;
        }
    }
    
    vector<Vector3d> Vels, Accs;
    Vels.push_back(Vector3d::Zero());
    Vels.push_back(Vector3d::Zero());
    Accs.push_back(Vector3d::Zero());
    Accs.push_back(Vector3d::Zero());
    MatrixXd Ctrl_pts; 

    if (use_astar_traj) {
        UniformBspline::parameter2Bspline(t_sample, Kino_path.first, Vels, Accs, Ctrl_pts);
    } else if (use_poly_traj) {
        UniformBspline::parameter2Bspline(t_sample, polyTrajPoints, Vels, Accs, Ctrl_pts);
    } else if (use_sfc_traj) {
        UniformBspline::parameter2Bspline(t_sample, sfcTrajPoints, Vels, Accs, Ctrl_pts);
    }

    BsplineOptimizer optimizer;
    vector<tuple<double, double, double, double, double, double, double, double>> Cost_history;
    if (enable_mpcc) {
        optimizer.optimize(Ctrl_pts, start_pos,
            Vector3d(0, 0, 0), goal_pos, 
            Vector3d(0, 0, 0), &sdfmap, t_sample,
            100.0,  //lambda_s
            100.0,   //lambda_c
            0.0,    //lambda_v
            0.0,    //lambda_a
            0.0,    //lambda_l
            0.1,      //lambda_dl 0.1
            100,   //lambda_ep
            100,   //lambda_ev 100
            0,   //lambda_ea 10
            risk_dis,   //risk_dis
            MAX_VEL,    //vel_max
            MAX_ACC,    //acc_max 
            Cost_history);
    } else {
        t_sample *= 1.0;
        optimizer.optimize(Ctrl_pts, start_pos,
            Vector3d(0, 0, 0), goal_pos, 
            Vector3d(0, 0, 0), &sdfmap, t_sample, 
            100.0,  //lambda_s
            100.0,   //lambda_c
            0.1,    //lambda_v
            0.1,    //lambda_a
            0.0,    //lambda_l
            0.0,    //lambda_dl 
            100,   //lambda_ep
            100,   //lambda_ev 100
            10,   //lambda_ea 10
            risk_dis,   //risk_dis
            MAX_VEL,    //vel_max
            MAX_ACC,    //acc_max 
            Cost_history);
    }
    auto V_ctrl_pts = UniformBspline::getDerivativeCtrlPts(Ctrl_pts, t_sample);
    vector<Vector3d> Opt_bsp_p, Opt_bsp_v;
    for (double t = 3 * t_sample; t < Ctrl_pts.rows() * t_sample + 1e-6; t += t_sample * 1) {
        auto p = UniformBspline::getBsplineValue(t_sample, Ctrl_pts, t, 3);
        auto v = UniformBspline::getBsplineValue(t_sample, V_ctrl_pts, t - t_sample, 2);
        Opt_bsp_p.push_back(p);
        Opt_bsp_v.push_back(v);
    }

    ParabolicAirdrop airdrop(sdfmap, target_pos, Opt_bsp_p, Opt_bsp_v, MAX_VEL, 100, 100, payload_offset);
    if(airdrop.getoptimalpoint()){
        throw_pos = airdrop.best_pos;
        throw_vel = airdrop.best_vel;
        cerr << "throw_pos: " << throw_pos[0] << " " << throw_pos[1] <<  " " << throw_pos[2] << endl;
        cerr << "throw_vel: " << throw_vel[0] << " " << throw_vel[1] <<  " " << throw_vel[2] << endl;
    }else{
        cerr << "Failed to retrieve release point position and velocity. " << endl;
    };

    // 两段轨迹
    vector<Vector3d> pos_first = {
        start_pos,
        throw_pos
    };
    vector<Vector3d> vel_first = {
        {0, 0, 0}, 
        throw_vel
    };
    vector<Vector3d> pos_second = {
        throw_pos,
        goal_pos
    };
    vector<Vector3d> vel_second = {
        throw_vel, 
        {0, 0, 0}
    };

    if (use_astar_traj) {
        kino_path_first.first.clear();
        kino_path_first.second.clear();
        for (int i = 0; i < pos_first.size() - 1; i++) {
            kino_astar.search(
                pos_first[i],
                vel_first[i],
                pos_first[i+1],
                vel_first[i+1]);
            auto kino_path_ = kino_astar.get_sample_path(t_sample);
            kino_path_first.first.insert(kino_path_first.first.end(), kino_path_.first.begin(), kino_path_.first.end());
        }
        kino_path_first.first.push_back(throw_pos);
        for (int i = 0; i < pos_second.size() - 1; i++) {
            kino_astar.search(
                pos_second[i],
                vel_second[i],
                pos_second[i+1],
                vel_second[i+1]);
            auto kino_path_ = kino_astar.get_sample_path(t_sample);
            kino_path_second.first.insert(kino_path_second.first.end(), kino_path_.first.begin(), kino_path_.first.end());
        }
        kino_path_second.first.push_back(goal_pos);

        vector<Vector3d> kino_path = kino_path_first.first;
        kino_path.insert(kino_path.end(), kino_path_second.first.begin(), kino_path_second.first.end());
        ros_inte.publish_kino_traj(kino_path);
    }

    if (use_poly_traj) {
        polyTraj_first_ = generatePolyTrajectory(polyTraj, rrtPlanner,
                                                start_pos, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0), 
                                                throw_pos, throw_vel, Vector3d(0.0, 0.0, 0.0),
                                                offset, t_sample);
        
        polyTraj_second_ = generatePolyTrajectory(polyTraj, rrtPlanner,
                                                throw_pos, throw_vel, Vector3d(0.0, 0.0, 0.0), 
                                                goal_pos, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                                                offset, t_sample);
        vector<Vector3d> poly_path = polyTraj_first_;
        poly_path.insert(poly_path.end(), polyTraj_second_.begin(), polyTraj_second_.end());
        ros_inte.publish_kino_traj(poly_path);
    }

    vector<Vector3d> sfcTraj_first_, sfcTraj_second_;
    if (use_sfc_traj) {
        // 发布第一段轨迹目标给SFC节点（起点到抛投点，启用可视化）
        publishTrajectoryTarget(sfc_target_pub,
                              start_pos - offset, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                              throw_pos - offset, throw_vel, Vector3d(0.0, 0.0, 0.0),
                              true);

        // 等待第一段SFC轨迹生成完成
        if (waitForSFCTrajectory(nh, 10.0)) {
            sfcTraj_first_ = received_sfc_trajectory;
            // 将轨迹坐标转换回全局坐标系
            for (auto& point : sfcTraj_first_) {
                point += offset;
            }

            // 重置接收标志
            sfc_trajectory_received = false;

            // 发布第二段轨迹目标给SFC节点（抛投点到终点，启用可视化）
            publishTrajectoryTarget(sfc_target_pub,
                                  throw_pos - offset, throw_vel, Vector3d(0.0, 0.0, 0.0),
                                  goal_pos - offset, Vector3d(0.0, 0.0, 0.0), Vector3d(0.0, 0.0, 0.0),
                                  true);

            // 等待第二段SFC轨迹生成完成
            if (waitForSFCTrajectory(nh, 10.0)) {
                sfcTraj_second_ = received_sfc_trajectory;
                // 将轨迹坐标转换回全局坐标系
                for (auto& point : sfcTraj_second_) {
                    point += offset;
                }

                sfc_trajectory_received = false;

                // 合并并发布轨迹
                // vector<Vector3d> sfcTraj_ = sfcTraj_first_;
                // sfcTraj_.insert(sfcTraj_.end(), sfcTraj_second_.begin(), sfcTraj_second_.end());
                // ros_inte.publish_kino_traj(sfcTraj_);

                std::cout << "Successfully received and published SFC trajectory for throw mission" << std::endl;
            } else {
                std::cout << "Failed to receive second SFC trajectory for throw mission!" << std::endl;
            }
        } else {
            std::cout << "Failed to receive first SFC trajectory for throw mission!" << std::endl;
        }
    }

    MatrixXd ctrl_pts_first, ctrl_pts_second;
    //trajectory parameterize
    vector<Vector3d> vels_first = {
        {0, 0, 0},
        throw_vel
    };
    vector<Vector3d> accs_first = {
        {0, 0, 0},
        {0, 0, 0}
    };
    vector<Vector3d> vels_second = {
        throw_vel,
        {0, 0, 0}
    };
    vector<Vector3d> accs_second = {
        {0, 0, 0},
        {0, 0, 0}
    };

    if (use_astar_traj) {
        UniformBspline::parameter2Bspline(t_sample, kino_path_first.first, vels_first, accs_first, ctrl_pts_first);
        UniformBspline::parameter2Bspline(t_sample, kino_path_second.first, vels_second, accs_second, ctrl_pts_second);
    } else if (use_poly_traj) {
        UniformBspline::parameter2Bspline(t_sample, polyTraj_first_, vels_first, accs_first, ctrl_pts_first);
        UniformBspline::parameter2Bspline(t_sample, polyTraj_second_, vels_second, accs_second, ctrl_pts_second);
    } else if (use_sfc_traj) {
        UniformBspline::parameter2Bspline(t_sample, sfcTraj_first_, vels_first, accs_first, ctrl_pts_first);
        UniformBspline::parameter2Bspline(t_sample, sfcTraj_second_, vels_second, accs_second, ctrl_pts_second);
    }

    // B-spline trajectory optimization
    vector<tuple<double, double, double, double, double, double, double, double>> cost_history1, cost_history2;
    if (enable_mpcc) {
        optimizer.optimize(ctrl_pts_first, start_pos,
            Vector3d(0, 0, 0), throw_pos, 
            throw_vel, &sdfmap, t_sample, 
            100.0,  // lambda_s
            100.0,   // lambda_c
            0.0,    // lambda_v
            0.0,    // lambda_a
            0.0,    // lambda_l
            0.1,      //lambda_dl 0.1
            100,   // lambda_ep
            100,   // lambda_ev 100
            0,   // lambda_ea 10
            risk_dis,   // risk_dis
            MAX_VEL,    // vel_max
            MAX_ACC,    // acc_max 
            cost_history1);
        optimizer.optimize(ctrl_pts_second, throw_pos,
            throw_vel, goal_pos, 
            Vector3d(0, 0, 0), &sdfmap, t_sample, 
            100.0,  //lambda_s
            100.0,   //lambda_c
            0.0,    //lambda_v
            0.0,    //lambda_a
            0.0,    //lambda_l
            0.1,      //lambda_dl 0.1
            100,   //lambda_ep
            100,   //lambda_ev 100
            0,   //lambda_ea 10
            risk_dis,   //risk_dis
            MAX_VEL,    //vel_max
            MAX_ACC,    //acc_max 
            cost_history2);
    } else {
        t_sample *= 1.0;
        optimizer.optimize(ctrl_pts_first, start_pos,
            Vector3d(0, 0, 0), throw_pos, 
            throw_vel, &sdfmap, t_sample, 
            100.0,  //lambda_s
            100.0,   //lambda_c
            0.1,    //lambda_v
            0.1,    //lambda_a
            0.0,    //lambda_l
            0.0,      //lambda_dl 0.1
            100,   //lambda_ev 100
            100,   //lambda_ep
            10.0,   //lambda_ea 10
            risk_dis,   //risk_dis
            MAX_VEL,    //vel_max
            MAX_ACC,    //acc_max 
            cost_history1);
        optimizer.optimize(ctrl_pts_second, throw_pos,
            throw_vel, goal_pos, 
            Vector3d(0, 0, 0), &sdfmap, t_sample, 
            100.0,  //lambda_s
            100.0,   //lambda_c
            0.1,    //lambda_v
            0.1,    //lambda_a
            0.0,    //lambda_l
            0.0,      //lambda_dl 0.1
            100,   //lambda_ep
            100,   //lambda_ev 100
            10.0,   //lambda_ea 10
            risk_dis,   //risk_dis
            MAX_VEL,    //vel_max
            MAX_ACC,    //acc_max 
            cost_history2);
    }

    auto v_ctrl_pts_first = UniformBspline::getDerivativeCtrlPts(ctrl_pts_first, t_sample);
    auto a_ctrl_pts_first = UniformBspline::getDerivativeCtrlPts(v_ctrl_pts_first, t_sample);

    auto v_ctrl_pts_second = UniformBspline::getDerivativeCtrlPts(ctrl_pts_second, t_sample);
    auto a_ctrl_pts_second = UniformBspline::getDerivativeCtrlPts(v_ctrl_pts_second, t_sample);

    vector<Vector3d> opt_bsp_p1, opt_bsp_p2;
    for (double t = 3 * t_sample; t < ctrl_pts_first.rows() * t_sample + 1e-6; t += t_sample * 1) {
        auto p = UniformBspline::getBsplineValue(t_sample, ctrl_pts_first, t, 3);
        opt_bsp_p1.push_back(p);
    }
    
    for (double t = 3 * t_sample; t < ctrl_pts_second.rows() * t_sample + 1e-6; t += t_sample * 1) {
        auto p = UniformBspline::getBsplineValue(t_sample, ctrl_pts_second, t, 3);
        opt_bsp_p2.push_back(p);
    }
    vector<Vector3d> opt_bsp = opt_bsp_p1;
    opt_bsp.insert(opt_bsp.end(), opt_bsp_p2.begin(), opt_bsp_p2.end());
    ros_inte.publish_bspline_traj(opt_bsp);
    ros_inte.publish_parabola(airdrop.parabola_); 

    cout << "B-spline duration: " << ctrl_pts_first.rows() * t_sample - 3 * t_sample + ctrl_pts_second.rows() * t_sample - 3 * t_sample<< " s" << endl;

    vector<double> arc_lengths, arc_t;
    auto start_t = chrono::steady_clock::now();
    double total_len1, total_len2= 0.0;
    double dt = 0.001;
    for (double t = 2 * t_sample; t < v_ctrl_pts_first.rows() * t_sample; t += dt) {
        total_len1 += UniformBspline::getBsplineValueFast<Vector3d>(t_sample, v_ctrl_pts_first, t, 2).norm() * dt;
        arc_lengths.push_back(total_len1);
        arc_t.push_back(t - 2 * t_sample);
    }
    for (double t = 2 * t_sample; t < v_ctrl_pts_second.rows() * t_sample; t += dt) {
        total_len2 += UniformBspline::getBsplineValueFast<Vector3d>(t_sample, v_ctrl_pts_second, t, 2).norm() * dt;
        arc_lengths.push_back(total_len2 + total_len1);
        arc_t.push_back(t - 2 * t_sample + v_ctrl_pts_first.rows() * t_sample - 2 * t_sample);
    }
    cout << "Total len: " << total_len1 + total_len2 << " m" << endl;
    auto spend = chrono::duration<double>(chrono::steady_clock::now() - start_t).count();

    logger.length_ = total_len1 + total_len2;

    SdfMap sdfmap2(gridmap.resolution() / 1.0, gridmap);
    ros_inte.publish_sdf_map(sdfmap2);

    ros::Rate rate(100); 
    auto p_start = opt_bsp_p1.front();
    auto p_end = opt_bsp_p2.back();
    
    while(ros::ok() && !(px4.current_state.connected && px4.origin_received_)) {
        ros::spinOnce();
        rate.sleep();
    }
    
    geometry_msgs::PoseStamped ps;
    ps.header.frame_id = "FCU";
    ps.pose.position.x = 0;
    ps.pose.position.y = 0;
    ps.pose.position.z = p_start[2] - px4.origin_position_[2];
    ps.pose.orientation.x = 0;
    ps.pose.orientation.y = 0;
    ps.pose.orientation.z = 0;
    ps.pose.orientation.w = 1;

    // send a few setpoints before starting
    // 在切换到offboard模式之前，你必须先发送一些期望点信息到飞控中。不然飞控会拒绝切换到offboard模式。
    for(int i = 100; ros::ok() && i > 0; --i){
        ps.header.stamp = ros::Time::now();
        px4.pos_target_pub_.publish(ps);
        ros::spinOnce();
        rate.sleep();
    }

    mavros_msgs::SetMode offb_set_mode;
    offb_set_mode.request.custom_mode = "OFFBOARD";

    mavros_msgs::CommandBool arm_cmd;
    arm_cmd.request.value = true;

    ros::Time last_request = ros::Time::now();
    bool is_offboard = false;
    bool is_armed = false;

    while(ros::ok() && (!is_offboard || !is_armed)) {
        ps.header.stamp = ros::Time::now();
        px4.pos_target_pub_.publish(ps);
        if(px4.current_state.mode != "OFFBOARD" &&
            (ros::Time::now() - last_request > ros::Duration(5.0))){
            if(px4.px4_mode_srv_.call(offb_set_mode) &&
                offb_set_mode.response.mode_sent){
                ROS_INFO("Offboard enabled");
                is_offboard = true;
            }
            last_request = ros::Time::now();
        } else {
            if(!px4.current_state.armed &&
                (ros::Time::now() - last_request > ros::Duration(5.0))){
                if(px4.px4_arm_srv_.call(arm_cmd) &&
                    arm_cmd.response.success){
                    ROS_INFO("Vehicle armed");
                    is_armed = true;
                }
                last_request = ros::Time::now();
            }
        }
        ros::spinOnce();
        rate.sleep();
    }

    double avg_avg_vel = 0.;
    double avg_max_vel = 0.;
    double avg_min_dist = 0.;
    int fail_cnt = 0;

    // double fan_ang = 0.;
    auto start_time = ros::Time::now();
    while((ros::Time::now() - start_time).toSec() < 6.0 && ros::ok()) {
        Vector3d pos = px4.global_pos();  
        Vector4d quat = px4.global_quat();
        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        px4.set_pos(0, 0, p_start[2] - px4.origin_position_[2], Eigen::Quaterniond::Identity()); // 本地坐标系
        rate.sleep();  
        // 风扇位置
        // ros_inte.publish_fanmesh(Vector3d(8.6, 4.0, 1.5), Vector3d(0, fan_ang, M_PI / 180.0 * 90.));
        // fan_ang += M_PI / 180.0 * 10;
        ros::spinOnce();
    }
    
    while (ros::ok() && !ros_inte.start){
        Vector3d pos = px4.global_pos();  
        Vector4d quat = px4.global_quat();
        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        tracking_controller::Target target;
        target.position.x = p_start[0] - size_x / 2.0;
        target.position.y = p_start[1] - size_y / 2.0;
        target.position.z = p_start[2];
        target.velocity.x = 0.0;
        target.velocity.y = 0.0;
        target.velocity.z = 0.0;
        target.acceleration.x = 0.0;
        target.acceleration.y = 0.0;
        target.acceleration.z = 0.0;
        target.yaw = 0.0;
        px4.state_pub_.publish(target);
        // px4.set_pos(p_start[0] - px4.origin_position_[0] , p_start[1] - px4.origin_position_[1], p_start[2] - px4.origin_position_[2] , Eigen::Quaterniond::Identity());
        rate.sleep();     // 按频率休眠
        ros::spinOnce();  // 处理回调函数
    }
    
    double hover_ratio1 = 0.751;  // 仿真数据，真机需要实际测量得出。
    double hover_ratio2 = 0.738;
    bool parabolic_airdrop = false;

    double omega = 14;
    double l1 = 3 * omega - 1;
    double l2 = 3 * omega * omega - 1;
    double l3 = omega * omega * omega;
    double omega_z = 20;
    double l1_z = 3 * omega_z - 1;
    double l2_z = 3 * omega_z * omega_z - 1;
    double l3_z = omega_z * omega_z * omega_z;
    GPIObserver dob(Vector3d(l1, l1, l1_z), Vector3d(l2, l2, l2_z), Vector3d(l3, l3, l3_z));
    PidTracker pidtracker1(Vector3d(1.5, 1.5, 1.5), Vector3d(1.5, 1.5, 1.5), hover_ratio1);
    PidTracker pidtracker2(Vector3d(1.5, 1.5, 1.5), Vector3d(1.5, 1.5, 1.5), hover_ratio2);
    NominalMpcc nominal_mpcc1(hover_ratio1, "LD_AUGLAG", 300), nominal_mpcc2(hover_ratio2, "LD_AUGLAG", 300);
    Matrix<double, 3 + NominalMpcc::u_dim_ + 1 + NominalMpcc::u_dim_ + 1 + 1 + 1, 1> cost_w1, cost_w2;
    // 调参只能在这些参数附近微调
    // cost_w1 << 1.0, 50.0,                                                       // weight of contouring error and lag error
    //     mu * total_len1 / (ctrl_pts_first.rows() * t_sample - 3 * t_sample),    // weight of progress
    //     0.0, 0.0, 0.005, 0.0,                                                   // weight of control input
    //     0.0,                                                                    // weight of the cost of violating distance constraints
    //     0.1, 0.1, 0.1, 100.0,                                                   // weight of the difference of control input
    //     75,                                                                     // weight of the cost of violating CBF constraints
    //     (throw_vel.isZero()) ? 0.0 : 1.0 / throw_vel.squaredNorm();             // weight of the cost of violating terminal speed constraint 
    //     0.0;                                                                    // weight of the cost of violating terminal acceleration constraint     
    // cost_w2 << 1.0, 50.0,                                             
    //     mu * total_len2 / (ctrl_pts_second.rows() * t_sample - 3 * t_sample), 
    //     0.0, 0.0, 0.005, 0.0,                                   
    //     0.0,                                                           
    //     0.1, 0.1, 0.1, 100.0,                                         
    //     75,                                                         
    //     0.0;                
    //     0.0;    
    // 动态避障    
    cost_w1 << 0.5, 50.0,                                                       // weight of contouring error and lag error
        mu * total_len1 / (ctrl_pts_first.rows() * t_sample - 3 * t_sample),    // weight of progress
        0.0, 0.0, 0.005, 0.1,                                                   // weight of control input
        0.0,                                                                    // weight of the cost of violating distance constraints
        0.2, 0.2, 0.2, 200.0,                                                   // weight of the difference of control input
        75,                                                                     // weight of the cost of violating CBF constraints
        (throw_vel.isZero()) ? 0.0 : 1.0 / throw_vel.squaredNorm();             // weight of the cost of violating terminal speed constraint 
        0.0;                                                                    // weight of the cost of violating terminal acceleration constraint   
    cost_w2 << 0.5, 50.0,                                             
        mu * total_len2 / (ctrl_pts_second.rows() * t_sample - 3 * t_sample), 
        0.0, 0.0, 0.005, 0.0,                                   
        0.0,                                                           
        0.2, 0.2, 0.2, 200.0,                                          
        75,                                                         
        0.0;                
        0.0;                                                                                                             
    nominal_mpcc1.set_w(cost_w1);
    nominal_mpcc2.set_w(cost_w2);
    Matrix<double, NominalMpcc::x_dim_, 1> state;
    Matrix<double, n_step, NominalMpcc::x_dim_> x_predict;
    Matrix<double, n_step, NominalMpcc::u_dim_> u1, u2;
    Matrix<double, n_step + 1, 1> t_index1, t_index2;
    t_index1[0] = t_sample * 3;
    t_index2[0] = t_sample * 3;
    for (int k = 0; k < n_step; k++)
    {
        t_index1[k + 1] = t_index1[k] + 0.05;
        t_index2[k + 1] = t_index2[k] + 0.05;
        u1.row(k) << 0., 0., 0., hover_ratio1;
        u2.row(k) << 0., 0., 0., hover_ratio2;
    }

    vector<double> solve_times;
    vector<double> vel_norm, sim_vel_norm, acc_list, sim_acc_list, t_list, r_list, p_list, y_list, tilt_list, rr_list, pr_list, yr_list, thr_list;
    vector<double> acc_x_list, acc_y_list, acc_z_list, sim_acc_x_list, sim_acc_y_list, sim_acc_z_list;
    vector<double> real_rr_list, real_pr_list, real_yr_list;
    vector<double> acc_x_err_list, acc_y_err_list, acc_z_err_list, vel_x_list, vel_y_list, vel_z_list;
    vector<double> sim_vel_x_list, sim_vel_y_list, sim_vel_z_list;
    vector<double> dob_vx_list, dob_vy_list, dob_vz_list, dob_dx_list, dob_dy_list, dob_dz_list;
    vector<double> nominal_acc_x_err_list, nominal_acc_y_err_list, nominal_acc_z_err_list;
    vector<Vector3d> collision_pos;
    double mean_acc_norm_err = 0.0, mean_nominal_acc_norm_err = 0.0;
    double mean_v_norm_err = 0.0, mean_nominal_v_norm_err = 0.0;
    double mean_p_err = 0.0, mean_nominal_p_err = 0.0;
    vector<Vector3d> mpcc_traj, ball_traj;
    vector<double> dis_to_obs, dis_to_obs_sim1, dis_to_obs_sim2;
    int reach_cnt = 1 / 0.02;

#if USE_EXTENDED_DYNAMICS
    ExtendedQuadDynamic quaddynamic1(hover_ratio1), quaddynamic2(hover_ratio2);
#else
    NominalQuadDynamic quaddynamic1(hover_ratio1), quaddynamic2(hover_ratio2);
#endif
    Eigen::Matrix<double, NominalMpcc::x_dim_, 1> sim_xdot, sim_state1, sim_state2, nominal_sim_xdot, nominal_sim_state1;
    sim_xdot.setZero();
    sim_state1.setZero();
    sim_state2.setZero();
    nominal_sim_xdot.setZero();
    nominal_sim_state1.setZero();
    start_time = ros::Time::now();
    VectorXd u_opt1(4), u_opt2(4);;
    u_opt1.setZero();
    u_opt2.setZero();
    u_opt1(3) = hover_ratio1;
    u_opt2(3) = hover_ratio2;
    double solve_t1, solve_t2;
    int loop_cnt = 0;

    // MatrixXd ctrl_pts= MatrixXd(ctrl_pts_first.rows() + ctrl_pts_second.rows(), ctrl_pts_first.cols());
    // ctrl_pts << ctrl_pts_first, ctrl_pts_second;
    // auto v_ctrl_pts = UniformBspline::getDerivativeCtrlPts(ctrl_pts, t_sample);
    // auto a_ctrl_pts = UniformBspline::getDerivativeCtrlPts(v_ctrl_pts, t_sample);

    ros::Time past_vel_stamp = ros::Time::now();
    while (ros::ok()) {
        Vector3d pos = px4.global_pos();
        Vector3d ball_pos = px4.ball_pos();
        Vector4d quat = px4.global_quat();
        Vector3d vel = px4.global_vel();
        Vector3d acc_b = px4.acc(); // 推力产生的加速度
        Vector3d ang_rate = px4.rate();
        ros::Time vel_stamp = px4.vel_stamp();
        vector<vector<vector<Eigen::Vector3d>>> predPos = px4.get_pos_pred();
        vector<vector<vector<Eigen::Vector3d>>> predSize = px4.get_size_pred();
		vector<Eigen::VectorXd> intentProb = px4.get_intent_prob();
#if USE_EXTENDED_DYNAMICS
    if(t_index1[0] - ctrl_pts_first.rows() * t_sample < -1e-4 && parabolic_airdrop == false){ 
        state << pos, vel, quat.w(), quat.x(), quat.y(), quat.z(), u_opt1(3);
    }else{
        state << pos, vel, quat.w(), quat.x(), quat.y(), quat.z(), u_opt2(3);
    }
#else
        state << pos, vel, quat.w(), quat.x(), quat.y(), quat.z();
#endif

        Vector3d ang = quaternion_to_rpy(Quaterniond(quat.w(), quat.x(), quat.y(), quat.z()));
        auto b_e_matrix = Quaterniond(quat.w(), quat.x(), quat.y(), quat.z()).toRotationMatrix();
        Vector3d acc = b_e_matrix * acc_b + Vector3d(0, 0, -9.79362); // 合加速度

        if (sdfmap.get_dist_with_grad_trilinear(pos).first < 0.3) {
            collision_pos.push_back(pos);
        }

        mpcc_traj.push_back(pos);
        ball_traj.push_back(ball_pos);
        vel_norm.push_back(vel.norm());
        sim_vel_norm.push_back(sim_state1.block(3, 0, 3, 1).norm());
        mean_v_norm_err += fabs((vel - sim_state1.block(3, 0, 3, 1)).norm());
        mean_nominal_v_norm_err += fabs((vel - nominal_sim_state1.block(3, 0, 3, 1)).norm());
        mean_p_err += fabs((pos - sim_state1.block(0, 0, 3, 1)).norm());
        mean_nominal_p_err += fabs((pos - nominal_sim_state1.block(0, 0, 3, 1)).norm());
        acc_list.push_back(acc.norm());
        acc_x_list.push_back(acc.x());
        acc_y_list.push_back(acc.y());
        acc_z_list.push_back(acc.z());
        Vector3d sim_acc = sim_xdot.block(3, 0, 3, 1);
        Vector3d nominal_sim_acc = nominal_sim_xdot.block(3, 0, 3, 1);
        sim_acc_list.push_back(sim_acc.norm());
        sim_acc_x_list.push_back(sim_acc.x());
        sim_acc_y_list.push_back(sim_acc.y());
        sim_acc_z_list.push_back(sim_acc.z());
        mean_acc_norm_err += fabs((acc - sim_acc).norm());
        mean_nominal_acc_norm_err += fabs((acc - nominal_sim_acc).norm());
        acc_x_err_list.push_back((acc - sim_acc).x());
        acc_y_err_list.push_back((acc - sim_acc).y());
        acc_z_err_list.push_back((acc - sim_acc).z());
        nominal_acc_x_err_list.push_back((acc - nominal_sim_acc).x());
        nominal_acc_y_err_list.push_back((acc - nominal_sim_acc).y());
        nominal_acc_z_err_list.push_back((acc - nominal_sim_acc).z());
        vel_x_list.push_back(vel.x());
        vel_y_list.push_back(vel.y());
        vel_z_list.push_back(vel.z());
        t_list.push_back((ros::Time::now() - start_time).toSec());
        r_list.push_back(ang.x() * 180.0 / M_PI);
        p_list.push_back(ang.y() * 180.0 / M_PI);
        y_list.push_back(ang.z() * 180.0 / M_PI);
        tilt_list.push_back(acos(cos(ang.x())*cos(ang.y())) * 180.0 / M_PI);
        real_rr_list.push_back(ang_rate(0) * 180.0 / M_PI);
        real_pr_list.push_back(ang_rate(1) * 180.0 / M_PI);
        real_yr_list.push_back(ang_rate(2) * 180.0 / M_PI);

        logger.pos_ = pos;
        logger.vel_ = vel;
        logger.acc_ = acc;
        logger.quat_ = quat;
        logger.att_ = ang;
        logger.rate_ = ang_rate;
        logger.tilt_ = acos(cos(ang.x())*cos(ang.y())) * 180.0 / M_PI;
        logger.disturbance_ = dob.get_dhat();
        logger.dis_to_obs_ = sdfmap.get_dist_with_grad_trilinear(pos).first;
        logger.solution_time_ = solve_t1 + solve_t2;
        logger.time_ = (ros::Time::now() - start_time).toSec();

        
        if (enable_mpcc) {
            // MPCC, t_index[0]即控制开始时间
            if(t_index1[0] - ctrl_pts_first.rows() * t_sample < -1e-4 && parabolic_airdrop == false){  // 可以提前一点执行抛投（把-1e-6设置小一点即可），因为会有延迟。
                dob.update(vel, b_e_matrix, u_opt1[3] * 9.79362 / hover_ratio1/*1.084e-5 * pow(u_opt[3] * 1000 + 100, 2) * 4 / 0.74*/, (vel_stamp - past_vel_stamp).toSec());
                dob_vx_list.push_back(dob.get_vhat().x());
                dob_vy_list.push_back(dob.get_vhat().y());
                dob_vz_list.push_back(dob.get_vhat().z());
                dob_dx_list.push_back(dob.get_dhat().x());
                dob_dy_list.push_back(dob.get_dhat().y());
                dob_dz_list.push_back(dob.get_dhat().z());

                rr_list.push_back(u_opt1(0) * 180.0 / M_PI);
                pr_list.push_back(u_opt1(1) * 180.0 / M_PI);
                yr_list.push_back(u_opt1(2) * 180.0 / M_PI);
                thr_list.push_back(u_opt1(3));
                logger.u_ = u_opt1;

                auto ret = nominal_mpcc1.solve(state,
                sdfmap2, 
                ctrl_pts_first,
                v_ctrl_pts_first,
                a_ctrl_pts_first,
                predPos,
                predSize,
                intentProb,
                t_sample,
                total_len1,
                u_opt1,
                enable_dob ? dob.get_dhat() : Vector3d(0., 0., 0.),
                u1, x_predict,
                t_index1, solve_t1);
                
                vector<Vector3d> predict_traj;
                predict_traj.push_back(pos);
                for (int i = 0; i < n_step; i++) {
                    predict_traj.push_back(x_predict.block(i, 0, 1, 3).transpose());
                }
                ros_inte.publish_predict_traj(predict_traj);

                u_opt1 = u1.row(0);
#if USE_EXTENDED_DYNAMICS
                u_opt1(3) = x_predict(0, 10);
#endif

                px4.set_rate_with_trust(u_opt1(0), u_opt1(1), u_opt1(2), u_opt1(3));

                for (int i = 1; i < t_index1.size(); i++) 
                    t_index1[i] += 0.02;

                solve_times.push_back(solve_t1);

                // 有扰动作用
                quaddynamic1.xdot_func(state, u_opt1, dob.get_dhat(), sim_xdot);
                quaddynamic1.rk4_func(state, u_opt1, dob.get_dhat(), 0.02, sim_state1);
                quaddynamic1.rk4_func(state, u_opt1, dob.get_dhat(), 0.1, sim_state2);
                // 无扰动作用
                quaddynamic1.xdot_func(state, u_opt1, Vector3d(0, 0, 0), nominal_sim_xdot);
                quaddynamic1.rk4_func(state, u_opt1, Vector3d(0, 0, 0), 0.02, nominal_sim_state1);

                dis_to_obs.push_back(sdfmap.get_dist_with_grad_trilinear(pos).first);
                dis_to_obs_sim1.push_back(sdfmap.get_dist_with_grad_trilinear(Vector3d(sim_state1.block(0, 0, 3, 1))).first);
                dis_to_obs_sim2.push_back(sdfmap.get_dist_with_grad_trilinear(Vector3d(sim_state2.block(0, 0, 3, 1))).first);
            }else{
                dob.update(vel, b_e_matrix, u_opt2[3] * 9.79362 / hover_ratio2/*1.084e-5 * pow(u_opt[3] * 1000 + 100, 2) * 4 / 0.74*/, (vel_stamp - past_vel_stamp).toSec());
                dob_vx_list.push_back(dob.get_vhat().x());
                dob_vy_list.push_back(dob.get_vhat().y());
                dob_vz_list.push_back(dob.get_vhat().z());
                dob_dx_list.push_back(dob.get_dhat().x());
                dob_dy_list.push_back(dob.get_dhat().y());
                dob_dz_list.push_back(dob.get_dhat().z());

                rr_list.push_back(u_opt2(0) * 180.0 / M_PI);
                pr_list.push_back(u_opt2(1) * 180.0 / M_PI);
                yr_list.push_back(u_opt2(2) * 180.0 / M_PI);
                thr_list.push_back(u_opt2(3));
                logger.u_ = u_opt2;


                parabolic_airdrop = true;
                ros_inte.publish_magnet(parabolic_airdrop);

                // cerr << pos[0] << " " << pos[1] <<  " " << pos[2] << endl;
                // cerr << vel[0] << " " << vel[1] <<  " " << vel[2] << endl;
                // cerr << throw_pos[0] << " " << throw_pos[1] <<  " " << throw_pos[2] << endl;
                // cerr << throw_vel[0] << " " << throw_vel[1] <<  " " << throw_vel[2] << endl; 
                // while(1);
                auto ret = nominal_mpcc2.solve(state,
                sdfmap2, 
                ctrl_pts_second,
                v_ctrl_pts_second,
                a_ctrl_pts_second,
                predPos,
                predSize,
                intentProb,
                t_sample,
                total_len2,
                u_opt2,
                enable_dob ? dob.get_dhat() : Vector3d(0., 0., 0.),
                u2, x_predict,
                t_index2, solve_t2);
            
                vector<Vector3d> predict_traj;
                predict_traj.push_back(pos); // 自身位置
                for (int i = 0; i < n_step; i++) {
                    predict_traj.push_back(x_predict.block(i, 0, 1, 3).transpose()); // 预测位置
                }
                ros_inte.publish_predict_traj(predict_traj);

                u_opt2 = u2.row(0);
#if USE_EXTENDED_DYNAMICS
                u_opt2(3) = x_predict(0, 10);
#endif

                px4.set_rate_with_trust(u_opt2(0), u_opt2(1), u_opt2(2), u_opt2(3));

                for (int i = 1; i < t_index2.size(); i++) 
                    t_index2[i] += 0.02;

                solve_times.push_back(solve_t2);

                // 有扰动作用
                quaddynamic2.xdot_func(state, u_opt2, dob.get_dhat(), sim_xdot);
                quaddynamic2.rk4_func(state, u_opt2, dob.get_dhat(), 0.02, sim_state1);
                quaddynamic2.rk4_func(state, u_opt2, dob.get_dhat(), 0.1, sim_state2);
                // 无扰动作用
                quaddynamic2.xdot_func(state, u_opt2, Vector3d(0, 0, 0), nominal_sim_xdot);
                quaddynamic2.rk4_func(state, u_opt2, Vector3d(0, 0, 0), 0.02, nominal_sim_state1);

                dis_to_obs.push_back(sdfmap.get_dist_with_grad_trilinear(pos).first);
                dis_to_obs_sim1.push_back(sdfmap.get_dist_with_grad_trilinear(Vector3d(sim_state1.block(0, 0, 3, 1))).first);
                dis_to_obs_sim2.push_back(sdfmap.get_dist_with_grad_trilinear(Vector3d(sim_state2.block(0, 0, 3, 1))).first);
            }
        } else {
            if(loop_cnt * 0.02 < t_sample * (ctrl_pts_first.rows() - 3) &&  parabolic_airdrop == false){
                double t = loop_cnt * 0.02;
                Vector3d pd = UniformBspline::getBsplineValueFast<Vector3d>(t_sample, ctrl_pts_first, t + t_sample * 3, 3);
                Vector3d vd = UniformBspline::getBsplineValueFast<Vector3d>(t_sample, v_ctrl_pts_first, t + t_sample * 2, 2);
                Vector3d ad = UniformBspline::getBsplineValueFast<Vector3d>(t_sample, a_ctrl_pts_first, t + t_sample * 1, 1);
                auto ret = pidtracker1.calculate_control(
                    pd, vd, ad, pos, vel, Quaterniond(quat.w(), quat.x(), quat.y(), quat.z())  
                );
                px4.set_attitude_with_trust(ret.first, ret.second);
                pidtracker1.estimateThrustModel(acc);
                rate.sleep();       // 每0.02s(即ros::Rate rate(50))执行一次循环，pid控制一定要加上，保证控制频率。
            } else {
                if(parabolic_airdrop == false) loop_cnt = 0;
                double t = loop_cnt * 0.02;
                parabolic_airdrop = true;
                ros_inte.publish_magnet(parabolic_airdrop);
                if (t > t_sample * (ctrl_pts_second.rows() - 3)) {
                    t = t_sample * (ctrl_pts_second.rows() - 3);
                    if (reach_cnt-- < 0)  // 执行50次，每次循环0.02s，相当于等待1s。
                        break;
                }
                Vector3d pd = UniformBspline::getBsplineValueFast<Vector3d>(t_sample, ctrl_pts_second, t + t_sample * 3, 3);
                Vector3d vd = UniformBspline::getBsplineValueFast<Vector3d>(t_sample, v_ctrl_pts_second, t + t_sample * 2, 2);
                Vector3d ad = UniformBspline::getBsplineValueFast<Vector3d>(t_sample, a_ctrl_pts_second, t + t_sample * 1, 1);
                auto ret = pidtracker2.calculate_control(
                    pd, vd, ad, pos, vel, Quaterniond(quat.w(), quat.x(), quat.y(), quat.z())
                );
                px4.set_attitude_with_trust(ret.first, ret.second);
                pidtracker2.estimateThrustModel(acc);
                rate.sleep(); 
            }
        }
        
        // rate.sleep();
        ros::spinOnce();

        logger.update();
        past_vel_stamp = vel_stamp;
        loop_cnt++;

        // 也即当优化后得到的控制起始时间t_index2[0] 比B样条总时间 ctrl_pts_second.rows() * t_sample 要大时，说明控制结束。
        if (t_index2[0] - ctrl_pts_second.rows() * t_sample > -1e-4) { 
            if (reach_cnt-- < 0)
                break;
        }

        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        ros_inte.publish_mpcc_traj(mpcc_traj);
        ros_inte.publish_ball_traj(ball_traj);
        // ros_inte.publish_collision(collision_pos);
    }
    
    mean_acc_norm_err /= loop_cnt;
    mean_nominal_acc_norm_err /= loop_cnt;
    mean_v_norm_err /= loop_cnt;
    mean_nominal_v_norm_err /= loop_cnt;
    mean_p_err /= loop_cnt;
    mean_nominal_p_err /= loop_cnt;

    if (*min_element(dis_to_obs.begin(), dis_to_obs.end()) < 0.2) {
        fail_cnt = 1;
    } else {
        avg_avg_vel += accumulate(vel_norm.begin(), vel_norm.end(), 0.0) / vel_norm.size();
        avg_max_vel += *max_element(vel_norm.begin(), vel_norm.end());
        avg_min_dist += *min_element(dis_to_obs.begin(), dis_to_obs.end()) > 0 ? *min_element(dis_to_obs.begin(), dis_to_obs.end()) : 0;
    }

    // cout << "Mean solution time: " << fixed << setprecision(3)
    //     << accumulate(solve_times.begin(), solve_times.end(), 0.0) / solve_times.size() * 1e3
    //     << " ms" << endl;
    // cout << "mean_acc_norm_err: " << mean_acc_norm_err << " m/s^2" << endl;
    // cout << "mean_nominal_acc_norm_err: " << mean_nominal_acc_norm_err << " m/s^2" << endl;
    // cout << "mean_v_norm_err: " << mean_v_norm_err << " m/s" << endl;
    // cout << "mean_nominal_v_norm_err: " << mean_nominal_v_norm_err << " m/s" << endl;
    // cout << fixed << setprecision(6) << "mean_p_err: " << mean_p_err << " m" << endl;
    // cout << fixed << setprecision(6) << "mean_nominal_p_err: " << mean_nominal_p_err << " m" << endl;

    // cout << "mean velocity related to reference traj: " << total_len / t_list[t_list.size() - 1] << " m/s" << endl;
    // cout << "mean velocity related to real traj: " << accumulate(vel_norm.begin(), vel_norm.end(), 0.0) / vel_norm.size() << " m/s" << endl;
    // cout << "minimum distance to nearest obstacle: " << *min_element(dis_to_obs.begin(), dis_to_obs.end()) << " m" << endl;

    start_time = ros::Time::now();
    while((ros::Time::now() - start_time).toSec() < 8.0 && ros::ok()) {
        Vector3d pos = px4.global_pos();
        Vector4d quat = px4.global_quat();
        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        px4.set_pos(p_end[0] - px4.origin_position_[0] , p_end[1] - px4.origin_position_[1], 0, Eigen::Quaterniond::Identity());
        rate.sleep();
        // ros_inte.publish_fanmesh(Vector3d(8.6, 4.0, 1.5), Vector3d(0, fan_ang, M_PI / 180.0 * 90.));
        // fan_ang += M_PI / 180.0 * 10;
        ros::spinOnce();
    }
    cout << " fail_cnt: " << fail_cnt << endl;

    // px4.set_px4_mode("POSCTL");

    cout << "avg_avg_vel: " << avg_avg_vel << endl;
    cout << "avg_max_vel: " << avg_max_vel << endl;
    cout << "avg_min_dist: " << avg_min_dist << endl;
    cout << "success rate: " << 1 - (double)fail_cnt << endl;
    
#if 1

    plt::figure(2);
    plt::plot(arc_t, arc_lengths, {{"color", "blue"}, {"linestyle", "-"}, {"label", "equal t"}});
    plt::xlabel("t(s)");
    plt::ylabel("length(m)");
    plt::legend({{"fontsize", "8"}, {"loc", "upper left"}});

    plt::figure(3);
    plt::plot(t_list, vel_norm, {{"color", "gold"}, {"linestyle", "-"}, {"label", "real_norm"}});
    plt::plot(t_list, sim_vel_norm, {{"color", "gold"}, {"linestyle", "--"}, {"label", "sim_norm"}});
    plt::plot(t_list, vel_x_list, {{"color", "green"}, {"linestyle", "-"}, {"label", "x"}});
    plt::plot(t_list, vel_y_list, {{"color", "red"}, {"linestyle", "-"}, {"label", "y"}});
    plt::plot(t_list, vel_z_list, {{"color", "blue"}, {"linestyle", "-"}, {"label", "z"}});
    plt::xlabel("t(s)");
    plt::ylabel("velocity(m/s)");
    plt::legend({{"fontsize", "8"}, {"loc", "upper left"}});

    plt::figure(4);
    plt::plot(t_list, r_list, {{"color", "green"}, {"linestyle", "-"}, {"label", "roll"}});
    plt::plot(t_list, p_list, {{"color", "red"}, {"linestyle", "-"}, {"label", "pitch"}});
    plt::plot(t_list, y_list, {{"color", "blue"}, {"linestyle", "-"}, {"label", "yaw"}});
    plt::plot(t_list, tilt_list, {{"color", "gold"}, {"linestyle", "-"}, {"label", "tilt"}});
    plt::xlabel("t(s)");
    plt::ylabel("angle(degree)");
    plt::ylim(-180, 180);
    plt::legend({{"fontsize", "8"}, {"loc", "upper left"}});

    plt::figure(5);
    plt::plot(t_list, rr_list, {{"color", "green"}, {"linestyle", "-"}, {"label", "roll"}});
    plt::plot(t_list, pr_list, {{"color", "red"}, {"linestyle", "-"}, {"label", "pitch"}});
    plt::plot(t_list, yr_list, {{"color", "blue"}, {"linestyle", "-"}, {"label", "yaw"}});
    plt::plot(t_list, real_rr_list, {{"color", "green"}, {"linestyle", "--"}, {"label", "real roll"}});
    plt::plot(t_list, real_pr_list, {{"color", "red"}, {"linestyle", "--"}, {"label", "real pitch"}});
    plt::plot(t_list, real_yr_list, {{"color", "blue"}, {"linestyle", "--"}, {"label", "real yaw"}});
    // vector<double> pi_list, npi_list;
    plt::xlabel("t(s)");
    plt::ylabel("rate(degree/s)");
    plt::ylim(-180 * 2, 180 * 2);
    plt::legend({{"fontsize", "8"}, {"loc", "upper left"}});

    plt::figure(6);
    plt::plot(t_list, dis_to_obs, {{"color", "green"}, {"linestyle", "-"}, {"label", "real"}});
    plt::plot(t_list, dis_to_obs_sim1, {{"color", "red"}, {"linestyle", "-"}, {"label", "sim_dt=0.02"}});
    plt::plot(t_list, dis_to_obs_sim2, {{"color", "blue"}, {"linestyle", "-"}, {"label", "sim_dt=0.1"}});
    plt::xlabel("t(s)");
    plt::ylabel("distance to nearest obstacle(m)");
    plt::ylim(0.0, 1.2); // 根据实际修改

    plt::figure(7);
    plt::plot(t_list, thr_list, {{"color", "green"}, {"linestyle", "-"}});
    plt::xlabel("t(s)");
    plt::ylabel("thrust(%)");
    plt::ylim(0, 1);

    plt::figure(8);
    plt::plot(t_list, acc_list, {{"color", "gold"}, {"linestyle", "-"}, {"label", "real_norm"}});
    plt::plot(t_list, sim_acc_list, {{"color", "gold"}, {"linestyle", "--"}, {"label", "sim_norm"}});
    plt::plot(t_list, acc_x_list, {{"color", "green"}, {"linestyle", "-"}, {"label", "real_x"}});
    plt::plot(t_list, sim_acc_x_list, {{"color", "green"}, {"linestyle", "--"}, {"label", "sim_x"}});
    plt::plot(t_list, acc_y_list, {{"color", "red"}, {"linestyle", "-"}, {"label", "real_y"}});
    plt::plot(t_list, sim_acc_y_list, {{"color", "red"}, {"linestyle", "--"}, {"label", "sim_y"}});
    plt::plot(t_list, acc_z_list, {{"color", "blue"}, {"linestyle", "-"}, {"label", "real_z"}});
    plt::plot(t_list, sim_acc_z_list, {{"color", "blue"}, {"linestyle", "--"}, {"label", "sim_z"}});
    plt::xlabel("t(s)");
    plt::ylabel("acceleration(m/s^2)");
    plt::legend();
    plt::ylim(-9.79362 * 2, 9.79362 * 2);

    plt::figure(9);
    plt::plot(t_list, solve_times, {{"color", "green"}, {"linestyle", "-"}});
    plt::xlabel("t(s)");
    plt::ylabel("solution time(s)");
    plt::ylim(0.0, 0.015);

    plt::figure(10);
    plt::scatter(vel_x_list, acc_x_err_list, 1.0, {{"color", "green"}, {"marker", "x"}, {"label", "x"}});
    plt::scatter(vel_y_list, acc_y_err_list, 1.0, {{"color", "red"}, {"marker", "x"}, {"label", "y"}});
    plt::scatter(vel_z_list, acc_z_err_list, 1.0, {{"color", "blue"}, {"marker", "x"}, {"label", "z"}});
    plt::xlabel("velocity(m/s)");
    plt::ylabel("acceleration(m/s^2)");
    plt::legend();

    plt::figure(11);
    plt::plot(t_list, nominal_acc_x_err_list, {{"color", "green"}, {"linestyle", "-"}, {"label", "x"}});
    plt::plot(t_list, nominal_acc_y_err_list, {{"color", "red"}, {"linestyle", "-"}, {"label", "y"}});
    plt::plot(t_list, nominal_acc_z_err_list, {{"color", "blue"}, {"linestyle", "-"}, {"label", "z"}});
    plt::plot(t_list, dob_dx_list, {{"color", "green"}, {"linestyle", "--"}, {"label", "x_hat"}});
    plt::plot(t_list, dob_dy_list, {{"color", "red"}, {"linestyle", "--"}, {"label", "y_hat"}});
    plt::plot(t_list, dob_dz_list, {{"color", "blue"}, {"linestyle", "--"}, {"label", "z_hat"}});
    plt::xlabel("t(s)");
    plt::ylabel("disturbance acceleration(m/s^2)");
    plt::ylim(-10, 10);
    plt::legend();

    plt::figure(12);
    plt::plot(t_list, vel_x_list, {{"color", "green"}, {"linestyle", "-"}, {"label", "x"}});
    plt::plot(t_list, vel_y_list, {{"color", "red"}, {"linestyle", "-"}, {"label", "y"}});
    plt::plot(t_list, vel_z_list, {{"color", "blue"}, {"linestyle", "-"}, {"label", "z"}});
    plt::plot(t_list, dob_vx_list, {{"color", "green"}, {"linestyle", "--"}, {"label", "x_hat"}});
    plt::plot(t_list, dob_vy_list, {{"color", "red"}, {"linestyle", "--"}, {"label", "y_hat"}});
    plt::plot(t_list, dob_vz_list, {{"color", "blue"}, {"linestyle", "--"}, {"label", "z_hat"}});
    plt::xlabel("t(s)");
    plt::ylabel("velocity(m/s^2)");
    plt::ylim(-10, 10);
    plt::legend();

    vector<double> v_x, v_y, v_z, v_norm, a_x, a_y, a_z, a_norm;
    t_list.clear();
    for (double t = 3 * t_sample; t < ctrl_pts_first.rows() * t_sample + 1e-6; t += t_sample * 0.1) {
        auto p = UniformBspline::getBsplineValue(t_sample, ctrl_pts_first, t, 3);
        auto v = UniformBspline::getBsplineValue(t_sample, v_ctrl_pts_first, t - t_sample, 2);
        auto a = UniformBspline::getBsplineValue(t_sample, a_ctrl_pts_first, t - t_sample - t_sample, 1);
        t_list.push_back(t - 3 * t_sample);
        v_x.push_back(v.x());
        v_y.push_back(v.y());
        v_z.push_back(v.z());
        v_norm.push_back(v.norm());
        a_x.push_back(a.x());
        a_y.push_back(a.y());
        a_z.push_back(a.z());
        a_norm.push_back(a.norm());
    }
    for (double t = 3 * t_sample; t < ctrl_pts_second.rows() * t_sample + 1e-6; t += t_sample * 0.1) {
        auto p = UniformBspline::getBsplineValue(t_sample, ctrl_pts_second, t, 3);
        auto v = UniformBspline::getBsplineValue(t_sample, v_ctrl_pts_second, t - t_sample, 2);
        auto a = UniformBspline::getBsplineValue(t_sample, a_ctrl_pts_second, t - t_sample - t_sample, 1);
        t_list.push_back(t - 3 * t_sample + ctrl_pts_first.rows() * t_sample - 3 * t_sample + t_sample * 0.1);
        v_x.push_back(v.x());
        v_y.push_back(v.y());
        v_z.push_back(v.z());
        v_norm.push_back(v.norm());
        a_x.push_back(a.x());
        a_y.push_back(a.y());
        a_z.push_back(a.z());
        a_norm.push_back(a.norm());
    }

    plt::figure(13);
    plt::plot(t_list, v_x, {{"color", "green"}, {"linestyle", "-"}, {"label", "x"}});
    plt::plot(t_list, v_y, {{"color", "red"}, {"linestyle", "-"}, {"label", "y"}});
    plt::plot(t_list, v_z, {{"color", "blue"}, {"linestyle", "-"}, {"label", "z"}});
    plt::plot(t_list, v_norm, {{"color", "gold"}, {"linestyle", "-"}, {"label", "norm"}});
    plt::xlabel("t(s)");
    plt::ylabel("velocity(m/s)");
    plt::ylim(-10, 10);
    plt::legend();

    plt::figure(14);
    plt::plot(t_list, a_x, {{"color", "green"}, {"linestyle", "-"}, {"label", "x"}});
    plt::plot(t_list, a_y, {{"color", "red"}, {"linestyle", "-"}, {"label", "y"}});
    plt::plot(t_list, a_z, {{"color", "blue"}, {"linestyle", "-"}, {"label", "z"}});
    plt::plot(t_list, a_norm, {{"color", "gold"}, {"linestyle", "-"}, {"label", "norm"}});
    plt::xlabel("t(s)");
    plt::ylabel("acceleration(m/s^2)");
    plt::ylim(-20, 20);
    plt::legend();

    plt::show();
#endif

    return 0;
}


