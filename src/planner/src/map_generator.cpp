#include <random>
#include <fstream>

#include "map/map.hpp"
#include "ros_interface/ros_interface.hpp"

using namespace std;

int main(int argc, char **argv) {
    ros::init(argc, argv, "map_generator");

    if (argc != 2) {
        return 0;
    }

    const Vector3d map_size(50.0, 10.0, 3.0);
    int obs_num = 100;
    double max_length = 0.4;
    double max_width = 0.4;
    double min_length = 0.3;
    double min_width = 0.3;

    GridMap gridmap(0.05, map_size);

    random_device rd;
    default_random_engine eng(rd());
    auto rand_x = uniform_real_distribution<double>(0, map_size(0));
    auto rand_y = uniform_real_distribution<double>(0, map_size(1));
    auto rand_yaw = uniform_real_distribution<double>(0, M_PI * 2);
    auto rand_l = uniform_real_distribution<double>(min_length, max_length);
    auto rand_w = uniform_real_distribution<double>(min_width, max_width);

    vector<ObsRectangle> rectangles;
    for(int i = 0; i < obs_num; i++) {
        while (true) {
            double x = rand_x(eng);
            double y = rand_y(eng);
            double l = rand_l(eng);
            double w = rand_w(eng);
            double yaw = rand_yaw(eng);
            double diagonal = sqrt(l/2 * l/2 +w/2 * w/2);

            if (x + diagonal > map_size(0) - 2 || x - diagonal < 2) {
                continue;
            }
            if (y + diagonal > map_size(1) - 0.9 || y - diagonal < 0.9) {
                continue;
            }
            bool flag = false;
            for (auto &c : rectangles) {
                double dis = (Vector3d(x, y, 0) - c.pos_).norm() - diagonal - sqrt(c.length_/2 * c.length_/2 +c.width_/2 * c.width_/2);
                if (dis < 0.8) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                continue;
            }

            rectangles.push_back(ObsRectangle(Vector3d(x, y, 0), map_size(2), l, w, yaw));
            break;
        }
    }
    for (auto &c : rectangles) {
        gridmap.push_obs(c);
    }

    gridmap.update_grid_map();

    SdfMap sdfmap(gridmap.resolution() / 1.0, gridmap);

    RosInterface ros_inte(gridmap.size());
    sleep(1);
    ros_inte.publish_grid_map(gridmap);
    ros_inte.publish_sdf_map(sdfmap);
    sleep(1);

    ofstream map_file(argv[1]);
    map_file << "map " << 0.05 << " " << map_size(0) << " " << map_size(1) << " " << map_size(2) << endl;
    for (auto &c : rectangles) {
        map_file << "ObsRectangle " << c.pos_(0) << " " << c.pos_(1) << " " << c.pos_(2) << " " << c.high_ << " " << c.length_ << " " << c.width_ << " " << c.yaw_ << endl;
    }
    map_file.close();
    
    return 0;
}