#include "numeric"
#include <ros/package.h>
#include "map/map.hpp"
#include "map/sdf.hpp"
#include "ros_interface/ros_interface.hpp"
#include "kinodynamic_astar/kinodynamic_astar.hpp"
#include "bspline/uniform_bspline.hpp"
#include "bspline_opt/bspline_optimizer.hpp"
#include "px4_interface/px4_interface.hpp"
#include "mpcc/nominal_mpcc.hpp"
#include "matplotlib/matplotlibcpp.hpp"
// #include "quadrotor_dynamics/quad_simulator.hpp"
#include "common/rotation_math.hpp"
#include "disturbance_observer/gpiobserver.hpp"
#include "logger/logger.hpp"
#include "tracker/pid_tracker.hpp"
#include "MRPT/Mrpt.h"
#include "parabolic_airdrop/parabolic_airdrop.hpp"

namespace plt = matplotlibcpp;

constexpr int n_step = NominalMpcc::_n_step;
#define FLIGHT_ALTITUDE 2.0f // 飞行高度
#define RATE 50  // 频率 hz
#define RADIUS 5   // 绕八运动的半径大小 m
#define CYCLE_S 15 // 完成一次绕八运动所花费的时间
#define STEPS (CYCLE_S * RATE) // 每一圈的路径点个数
#define n_loop 20 // 飞行的圈数

using Vector3d = Eigen::Vector3d;  // 使用 Eigen 库中的 Vector3d


int main(int argc, char **argv) {
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(5, &mask);
    pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask);
    

    bool enable_dob = (argv[2][0] == '1');
    cout << (enable_dob == true ? "Enable DOB" : "Disable DOB") << endl;

    bool enable_mpcc = (argv[3][0] == '1');
    cout << (enable_mpcc == true ? "Enable MPCC" : "Disable MPCC") << endl;

    double MAX_VEL = atof(argv[4]);
    double MAX_ACC = atof(argv[5]);
    cout << "MAX_VEL: " << MAX_VEL << " MAX_ACC: " << MAX_ACC << endl;
    double mu = atof(argv[6]); // 倍速，越大无人机飞的越快
    cout << "mu: " << mu << endl;

    string log_path = ros::package::getPath("planner") + "/logs";
    Logger logger(string(enable_dob == true ? "enable-DOB" : "disable-DOB") + "-"
        + string(enable_mpcc == true ? "enable-MPCC" : "disable-MPCC") + "-test", log_path);

    bool useFakeDetector_ = atof(argv[7]);
    cout << "useFakeDetector: " << useFakeDetector_ << endl; 

    ros::init(argc, argv, "planner_px4_circle");
    ros::NodeHandle nh_;

    //construct map
    GridMap gridmap(argv[1]);
    double size_x = gridmap.size()[0];
    double size_y = gridmap.size()[1];

    //PX4 interface
    Px4Interface px4(size_x, size_y);
    // QuadSimulator px4(0.309);

    //ros interface
    RosInterface ros_inte(gridmap.size());
    sleep(1);
    ros_inte.publish_grid_map(gridmap);
    
    //construct SDF map
    SdfMap sdfmap(gridmap.resolution() / 1.0, gridmap);
    ros_inte.publish_sdf_map(sdfmap);

    double t_sample = 0.05;

    vector<Vector3d> path(STEPS);
    const double dadt = (2.0 * M_PI) / CYCLE_S;   // 角度相对于时间的一阶导数
    const double r = RADIUS;
    for (int i = 0; i < STEPS; i++) {
        double a = (-M_PI / 2.0) + i * (2.0 * M_PI / STEPS);
        double c = cos(a);
        double s = sin(a);
        double sspo = (s * s) + 1.0;

        path[i](0) = (r * c) / sspo + size_x / 2.0;  // x 坐标
        path[i](1) = -(r * c * s) / sspo + size_y / 2.0;  // y 坐标
        path[i](2) = FLIGHT_ALTITUDE;  // z 坐标
    }
    //trajectory parameterize
    vector<Vector3d> vels, accs;
    vels.push_back(Vector3d::Zero());
    vels.push_back(Vector3d::Zero());
    accs.push_back(Vector3d::Zero());
    accs.push_back(Vector3d::Zero());
    MatrixXd ctrl_pts;
    UniformBspline::parameter2Bspline(t_sample, path, vels, accs, ctrl_pts);
    ctrl_pts = ctrl_pts.replicate(n_loop, 1);


    //B-spline trajectory optimization
    // BsplineOptimizer optimizer;
    // vector<tuple<double, double, double, double, double, double, double, double>> cost_history;
    // optimizer.optimize(ctrl_pts, path[0],
    //     Vector3d(0, 0, 0), path[1], 
    //     Vector3d(0, 0, 0), &sdfmap, t_sample,
    //     100.0,  //lambda_s
    //     100.0,   //lambda_c
    //     0.0,    //lambda_v
    //     0.0,    //lambda_a
    //     0.0,    //lambda_l
    //     0.1,      //lambda_dl 0.1
    //     100,   //lambda_ep
    //     100,   //lambda_ev 100
    //     0,   //lambda_ea 10
    //     0.6,   //risk_dis
    //     MAX_VEL,    //vel_max
    //     MAX_ACC,    //acc_max 
    //     cost_history);

    auto v_ctrl_pts = UniformBspline::getDerivativeCtrlPts(ctrl_pts, t_sample);
    auto a_ctrl_pts = UniformBspline::getDerivativeCtrlPts(v_ctrl_pts, t_sample);
   

    vector<Vector3d> opt_bsp_p, opt_bsp_v, opt_bsp_a;
    vector<double> opt_t_list;
    for (double t = 3 * t_sample; t < ctrl_pts.rows() * t_sample + 1e-6; t += t_sample * 1) {
        auto p = UniformBspline::getBsplineValue(t_sample, ctrl_pts, t, 3);
        auto v = UniformBspline::getBsplineValue(t_sample, v_ctrl_pts, t - t_sample, 2);
        auto a = UniformBspline::getBsplineValue(t_sample, a_ctrl_pts, t - t_sample - t_sample, 1);
        opt_bsp_p.push_back(p);
        opt_bsp_v.push_back(v);
        opt_bsp_a.push_back(a);
        opt_t_list.push_back(t);
    }

    ros_inte.publish_bspline_traj(opt_bsp_p);
    cout << "B-spline duration: " << ctrl_pts.rows() * t_sample - 3 * t_sample << " s" << endl;

    plt::figure(1);
    vector<double> arc_lengths, arc_t;
    auto start_t = chrono::steady_clock::now();
    double total_len = 0.0;
    double dt = 0.001;
    for (double t = 2 * t_sample; t < v_ctrl_pts.rows() * t_sample; t += dt) {
        total_len += UniformBspline::getBsplineValueFast<Vector3d>(t_sample, v_ctrl_pts, t, 2).norm() * dt;
        arc_lengths.push_back(total_len);
        arc_t.push_back(t - 2 * t_sample);
    }
    cout << "Total len: " << total_len << " m" << endl;
    auto spend = chrono::duration<double>(chrono::steady_clock::now() - start_t).count();
    plt::plot(arc_t, arc_lengths, {{"color", "blue"}, {"linestyle", "-"}, {"label", "equal t"}});
    plt::xlabel("t(s)");
    plt::ylabel("length(m)");
    plt::legend({{"fontsize", "8"}, {"loc", "upper left"}});
    // plt::show();  // 显示图像
    // plt::save("xxx.png");  // 保存图像

    logger.length_ = total_len;

    SdfMap sdfmap2(gridmap.resolution() / 1.0, gridmap);
    ros_inte.publish_sdf_map(sdfmap2);


    ros::Rate rate(100);
    auto p_start = opt_bsp_p.front();
    auto p_end = opt_bsp_p.back();
    while(ros::ok() && !(px4.current_state.connected && px4.origin_received_)) {
        ros::spinOnce();
        rate.sleep();
    }
    geometry_msgs::PoseStamped ps;
    ps.header.frame_id = "FCU";
    ps.pose.position.x = 0;
    ps.pose.position.y = 0;
    ps.pose.position.z = p_start[2] - px4.origin_position_[2];
    ps.pose.orientation.x = 0;
    ps.pose.orientation.y = 0;
    ps.pose.orientation.z = 0;
    ps.pose.orientation.w = 1;

    // send a few setpoints before starting
    // 在切换到offboard模式之前，你必须先发送一些期望点信息到飞控中。不然飞控会拒绝切换到offboard模式。
    for(int i = 100; ros::ok() && i > 0; --i){
        ps.header.stamp = ros::Time::now();
        px4.pos_target_pub_.publish(ps);
        ros::spinOnce();
        rate.sleep();
    }

    mavros_msgs::SetMode offb_set_mode;
    offb_set_mode.request.custom_mode = "OFFBOARD";

    mavros_msgs::CommandBool arm_cmd;
    arm_cmd.request.value = true;

    ros::Time last_request = ros::Time::now();
    bool is_offboard = false;
    bool is_armed = false;

    while(ros::ok() && (!is_offboard || !is_armed)) {
        ps.header.stamp = ros::Time::now();
        px4.pos_target_pub_.publish(ps);
        if(px4.current_state.mode != "OFFBOARD" &&
            (ros::Time::now() - last_request > ros::Duration(5.0))){
            if(px4.px4_mode_srv_.call(offb_set_mode) &&
                offb_set_mode.response.mode_sent){
                ROS_INFO("Offboard enabled");
                is_offboard = true;
            }
            last_request = ros::Time::now();
        } else {
            if(!px4.current_state.armed &&
                (ros::Time::now() - last_request > ros::Duration(5.0))){
                if(px4.px4_arm_srv_.call(arm_cmd) &&
                    arm_cmd.response.success){
                    ROS_INFO("Vehicle armed");
                    is_armed = true;
                }
                last_request = ros::Time::now();
            }
        }
        ros::spinOnce();
        rate.sleep();
    }

    double avg_avg_vel = 0.;
    double avg_max_vel = 0.;
    double avg_min_dist = 0.;
    int fail_cnt = 0;

    // double fan_ang = 0.;
    auto start_time = ros::Time::now();
    while((ros::Time::now() - start_time).toSec() < 6.0 && ros::ok()) {
        Vector3d pos = px4.global_pos();  
        Vector4d quat = px4.global_quat();
        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        px4.set_pos(0, 0, p_start[2] - px4.origin_position_[2], Eigen::Quaterniond::Identity()); // 本地坐标系
        rate.sleep();  
        // 风扇位置
        // ros_inte.publish_fanmesh(Vector3d(8.6, 4.0, 1.5), Vector3d(0, fan_ang, M_PI / 180.0 * 90.));
        // fan_ang += M_PI / 180.0 * 10;
        ros::spinOnce();
    }
    
    while (ros::ok() && !ros_inte.start){
        Vector3d pos = px4.global_pos();  
        Vector4d quat = px4.global_quat();
        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        tracking_controller::Target target;
        target.position.x = p_start[0] - size_x / 2.0;
        target.position.y = p_start[1] - size_y / 2.0;
        target.position.z = p_start[2];
        target.velocity.x = 0.0;
        target.velocity.y = 0.0;
        target.velocity.z = 0.0;
        target.acceleration.x = 0.0;
        target.acceleration.y = 0.0;
        target.acceleration.z = 0.0;
        target.yaw = 0.0;
        px4.state_pub_.publish(target);
        // px4.set_pos(p_start[0] - px4.origin_position_[0] , p_start[1] - px4.origin_position_[1], p_start[2] - px4.origin_position_[2] , Eigen::Quaterniond::Identity());
        rate.sleep();     // 按频率休眠
        ros::spinOnce();  // 处理回调函数
    }
    
    double hover_ratio = 0.738;
    double omega = 14;
    double l1 = 3 * omega - 1;
    double l2 = 3 * omega * omega - 1;
    double l3 = omega * omega * omega;
    double omega_z = 20;
    double l1_z = 3 * omega_z - 1;
    double l2_z = 3 * omega_z * omega_z - 1;
    double l3_z = omega_z * omega_z * omega_z;
    GPIObserver dob(Vector3d(l1, l1, l1_z), Vector3d(l2, l2, l2_z), Vector3d(l3, l3, l3_z));
    NominalMpcc nominal_mpcc(hover_ratio, "LD_AUGLAG", 300);
    Matrix<double, 3 + NominalMpcc::u_dim_ + 1 + NominalMpcc::u_dim_ + 1 + 1 + 1, 1> cost_w;
    // 调参只能在这些参数附近微调
    cost_w << 1.0, 50.0,                                                        // weight of contouring error and lag error
        mu * total_len / (ctrl_pts.rows() * t_sample - 3 * t_sample),           // weight of progress
        0.0, 0.0, 0.005, 0.0,                                                   // weight of control input
        0.0,                                                                    // weight of the cost of violating distance constraints
        0.1, 0.1, 0.1, 100.0,                                                   // weight of the difference of control input
        75,                                                                     // weight of the cost of violating CBF constraints
        0.0,                                                                    // weight of the cost of violating terminal speed constraint    
        0.0;                                                                    // weight of the cost of violating terminal acceleration constraint     
    nominal_mpcc.set_w(cost_w);
    Matrix<double, NominalMpcc::x_dim_, 1> state;
    Matrix<double, n_step, NominalMpcc::x_dim_> x_predict;
    Matrix<double, n_step, NominalMpcc::u_dim_> u;
    Matrix<double, n_step + 1, 1> t_index;
    t_index[0] = t_sample * 3;
    for (int k = 0; k < n_step; k++)
    {
        t_index[k + 1] = t_index[k] + 0.05;
        u.row(k) << 0., 0., 0., hover_ratio;
    }

    vector<double> solve_times;
    vector<double> vel_norm, sim_vel_norm, acc_list, sim_acc_list, t_list, r_list, p_list, y_list, tilt_list, rr_list, pr_list, yr_list, thr_list;
    vector<double> acc_x_list, acc_y_list, acc_z_list, sim_acc_x_list, sim_acc_y_list, sim_acc_z_list;
    vector<double> real_rr_list, real_pr_list, real_yr_list;
    vector<double> acc_x_err_list, acc_y_err_list, acc_z_err_list, vel_x_list, vel_y_list, vel_z_list;
    vector<double> sim_vel_x_list, sim_vel_y_list, sim_vel_z_list;
    vector<double> dob_vx_list, dob_vy_list, dob_vz_list, dob_dx_list, dob_dy_list, dob_dz_list;
    vector<double> nominal_acc_x_err_list, nominal_acc_y_err_list, nominal_acc_z_err_list;
    vector<Vector3d> collision_pos;
    double mean_acc_norm_err = 0.0, mean_nominal_acc_norm_err = 0.0;
    double mean_v_norm_err = 0.0, mean_nominal_v_norm_err = 0.0;
    double mean_p_err = 0.0, mean_nominal_p_err = 0.0;
    vector<Vector3d> mpcc_traj, ball_traj;
    vector<double> dis_to_obs, dis_to_obs_sim1, dis_to_obs_sim2;
    int reach_cnt = 1 / 0.02;

#if USE_EXTENDED_DYNAMICS
    ExtendedQuadDynamic quaddynamic(hover_ratio);
#else
    NominalQuadDynamic quaddynamic(hover_ratio);
#endif
    Eigen::Matrix<double, NominalMpcc::x_dim_, 1> sim_xdot, sim_state1, sim_state2, nominal_sim_xdot, nominal_sim_state1;
    sim_xdot.setZero();
    sim_state1.setZero();
    sim_state2.setZero();
    nominal_sim_xdot.setZero();
    nominal_sim_state1.setZero();
    start_time = ros::Time::now();
    VectorXd u_opt(4);;
    u_opt.setZero();
    u_opt(3) = hover_ratio;
    double solve_t;
    int loop_cnt = 0;

    // MatrixXd ctrl_pts= MatrixXd(ctrl_pts_first.rows() + ctrl_pts_second.rows(), ctrl_pts_first.cols());
    // ctrl_pts << ctrl_pts_first, ctrl_pts_second;
    // auto v_ctrl_pts = UniformBspline::getDerivativeCtrlPts(ctrl_pts, t_sample);
    // auto a_ctrl_pts = UniformBspline::getDerivativeCtrlPts(v_ctrl_pts, t_sample);

    ros::Time past_vel_stamp = ros::Time::now();
    while (ros::ok()) {
        Vector3d pos = px4.global_pos();
        Vector4d quat = px4.global_quat();
        Vector3d vel = px4.global_vel();
        Vector3d acc_b = px4.acc();
        Vector3d ang_rate = px4.rate();
        ros::Time vel_stamp = px4.vel_stamp();
#if USE_EXTENDED_DYNAMICS
        state << pos, vel, quat.w(), quat.x(), quat.y(), quat.z(), u_opt(3);
#else
        state << pos, vel, quat.w(), quat.x(), quat.y(), quat.z();
#endif

        Vector3d ang = quaternion_to_rpy(Quaterniond(quat.w(), quat.x(), quat.y(), quat.z()));
        auto b_e_matrix = Quaterniond(quat.w(), quat.x(), quat.y(), quat.z()).toRotationMatrix();
        // Vector3d acc = b_e_matrix * acc_b + Vector3d(0, 0, -9.79362); // 动力学
        Vector3d acc = px4.global_acc();  // 运动学

        dob.update(vel, b_e_matrix, u_opt[3] * 9.79362 / hover_ratio/*1.084e-5 * pow(u_opt[3] * 1000 + 100, 2) * 4 / 0.74*/, (vel_stamp - past_vel_stamp).toSec());
        dob_vx_list.push_back(dob.get_vhat().x());
        dob_vy_list.push_back(dob.get_vhat().y());
        dob_vz_list.push_back(dob.get_vhat().z());
        dob_dx_list.push_back(dob.get_dhat().x());
        dob_dy_list.push_back(dob.get_dhat().y());
        dob_dz_list.push_back(dob.get_dhat().z());

        dis_to_obs.push_back(sdfmap.get_dist_with_grad_trilinear(pos).first);
        dis_to_obs_sim1.push_back(sdfmap.get_dist_with_grad_trilinear(Vector3d(sim_state1.block(0, 0, 3, 1))).first);
        dis_to_obs_sim2.push_back(sdfmap.get_dist_with_grad_trilinear(Vector3d(sim_state2.block(0, 0, 3, 1))).first);
        if (sdfmap.get_dist_with_grad_trilinear(pos).first < 0.3) {
            collision_pos.push_back(pos);
        }

        mpcc_traj.push_back(pos);
        vel_norm.push_back(vel.norm());
        sim_vel_norm.push_back(sim_state1.block(3, 0, 3, 1).norm());
        mean_v_norm_err += fabs((vel - sim_state1.block(3, 0, 3, 1)).norm());
        mean_nominal_v_norm_err += fabs((vel - nominal_sim_state1.block(3, 0, 3, 1)).norm());
        mean_p_err += fabs((pos - sim_state1.block(0, 0, 3, 1)).norm());
        mean_nominal_p_err += fabs((pos - nominal_sim_state1.block(0, 0, 3, 1)).norm());
        acc_list.push_back(acc.norm());
        acc_x_list.push_back(acc.x());
        acc_y_list.push_back(acc.y());
        acc_z_list.push_back(acc.z());
        Vector3d sim_acc = sim_xdot.block(3, 0, 3, 1);
        Vector3d nominal_sim_acc = nominal_sim_xdot.block(3, 0, 3, 1);
        sim_acc_list.push_back(sim_acc.norm());
        sim_acc_x_list.push_back(sim_acc.x());
        sim_acc_y_list.push_back(sim_acc.y());
        sim_acc_z_list.push_back(sim_acc.z());
        mean_acc_norm_err += fabs((acc - sim_acc).norm());
        mean_nominal_acc_norm_err += fabs((acc - nominal_sim_acc).norm());
        acc_x_err_list.push_back((acc - sim_acc).x());
        acc_y_err_list.push_back((acc - sim_acc).y());
        acc_z_err_list.push_back((acc - sim_acc).z());
        nominal_acc_x_err_list.push_back((acc - nominal_sim_acc).x());
        nominal_acc_y_err_list.push_back((acc - nominal_sim_acc).y());
        nominal_acc_z_err_list.push_back((acc - nominal_sim_acc).z());
        vel_x_list.push_back(vel.x());
        vel_y_list.push_back(vel.y());
        vel_z_list.push_back(vel.z());
        t_list.push_back((ros::Time::now() - start_time).toSec());
        r_list.push_back(ang.x() * 180.0 / M_PI);
        p_list.push_back(ang.y() * 180.0 / M_PI);
        y_list.push_back(ang.z() * 180.0 / M_PI);
        tilt_list.push_back(acos(cos(ang.x())*cos(ang.y())) * 180.0 / M_PI);
        rr_list.push_back(u_opt(0) * 180.0 / M_PI);
        pr_list.push_back(u_opt(1) * 180.0 / M_PI);
        yr_list.push_back(u_opt(2) * 180.0 / M_PI);
        thr_list.push_back(u_opt(3));
        real_rr_list.push_back(ang_rate(0) * 180.0 / M_PI);
        real_pr_list.push_back(ang_rate(1) * 180.0 / M_PI);
        real_yr_list.push_back(ang_rate(2) * 180.0 / M_PI);

        

        logger.pos_ = pos;
        logger.vel_ = vel;
        logger.acc_ = acc;
        logger.quat_ = quat;
        logger.att_ = ang;
        logger.rate_ = ang_rate;
        logger.tilt_ = acos(cos(ang.x())*cos(ang.y())) * 180.0 / M_PI;
        logger.disturbance_ = dob.get_dhat();
        logger.u_ = u_opt;
        logger.dis_to_obs_ = sdfmap.get_dist_with_grad_trilinear(pos).first;
        logger.solution_time_ = solve_t;
        logger.time_ = (ros::Time::now() - start_time).toSec();

        std::vector<std::vector<std::vector<Eigen::Vector3d>>> predPos, predSize; // 定义预测位置和尺寸
		std::vector<Eigen::VectorXd> intentProb; // 定义意图概率

        if (enable_mpcc) {
            // MPCC, t_index[0]即控制开始时间
            auto ret = nominal_mpcc.solve(state,
                sdfmap2,
                ctrl_pts,
                v_ctrl_pts,
                a_ctrl_pts,
                predPos,
                predSize,
                intentProb,
                t_sample,
                total_len,
                u_opt,
                enable_dob ? dob.get_dhat() : Vector3d(0., 0., 0.),
                u, x_predict,
                t_index, solve_t);
            
            vector<Vector3d> predict_traj;
            predict_traj.push_back(pos);
            for (int i = 0; i < n_step; i++) {
                predict_traj.push_back(x_predict.block(i, 0, 1, 3).transpose());
            }
            ros_inte.publish_predict_traj(predict_traj);

            u_opt = u.row(0);
#if USE_EXTENDED_DYNAMICS
            u_opt(3) = x_predict(0, 10);
#endif

            px4.set_rate_with_trust(u_opt(0), u_opt(1), u_opt(2), u_opt(3));

            for (int i = 1; i < t_index.size(); i++) 
                t_index[i] += 0.02;

            solve_times.push_back(solve_t);
        } 
        // 有扰动作用
        quaddynamic.xdot_func(state, u_opt, dob.get_dhat(), sim_xdot);
        quaddynamic.rk4_func(state, u_opt, dob.get_dhat(), 0.02, sim_state1);
        quaddynamic.rk4_func(state, u_opt, dob.get_dhat(), 0.1, sim_state2);
        // 无扰动作用
        quaddynamic.xdot_func(state, u_opt, Vector3d(0, 0, 0), nominal_sim_xdot);
        quaddynamic.rk4_func(state, u_opt, Vector3d(0, 0, 0), 0.02, nominal_sim_state1);
        
        // rate.sleep();
        ros::spinOnce();

        logger.update();
        past_vel_stamp = vel_stamp;
        loop_cnt++;

        // 也即当优化后得到的控制起始时间t_index[0] 比B样条总时间 ctrl_pts_second.rows() * t_sample 要大时，说明控制结束。
        if (t_index[0] - ctrl_pts.rows() * t_sample > -1e-4) { 
            if (reach_cnt-- < 0)
                break;
        }

        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        ros_inte.publish_mpcc_traj(mpcc_traj);
        ros_inte.publish_ball_traj(ball_traj);
        // ros_inte.publish_collision(collision_pos);
    }
    
    mean_acc_norm_err /= loop_cnt;
    mean_nominal_acc_norm_err /= loop_cnt;
    mean_v_norm_err /= loop_cnt;
    mean_nominal_v_norm_err /= loop_cnt;
    mean_p_err /= loop_cnt;
    mean_nominal_p_err /= loop_cnt;

    if (*min_element(dis_to_obs.begin(), dis_to_obs.end()) < 0.2) {
        fail_cnt = 1;
    } else {
        avg_avg_vel += accumulate(vel_norm.begin(), vel_norm.end(), 0.0) / vel_norm.size();
        avg_max_vel += *max_element(vel_norm.begin(), vel_norm.end());
        avg_min_dist += *min_element(dis_to_obs.begin(), dis_to_obs.end()) > 0 ? *min_element(dis_to_obs.begin(), dis_to_obs.end()) : 0;
    }

    // cout << "Mean solution time: " << fixed << setprecision(3)
    //     << accumulate(solve_times.begin(), solve_times.end(), 0.0) / solve_times.size() * 1e3
    //     << " ms" << endl;
    // cout << "mean_acc_norm_err: " << mean_acc_norm_err << " m/s^2" << endl;
    // cout << "mean_nominal_acc_norm_err: " << mean_nominal_acc_norm_err << " m/s^2" << endl;
    // cout << "mean_v_norm_err: " << mean_v_norm_err << " m/s" << endl;
    // cout << "mean_nominal_v_norm_err: " << mean_nominal_v_norm_err << " m/s" << endl;
    // cout << fixed << setprecision(6) << "mean_p_err: " << mean_p_err << " m" << endl;
    // cout << fixed << setprecision(6) << "mean_nominal_p_err: " << mean_nominal_p_err << " m" << endl;

    // cout << "mean velocity related to reference traj: " << total_len / t_list[t_list.size() - 1] << " m/s" << endl;
    // cout << "mean velocity related to real traj: " << accumulate(vel_norm.begin(), vel_norm.end(), 0.0) / vel_norm.size() << " m/s" << endl;
    // cout << "minimum distance to nearest obstacle: " << *min_element(dis_to_obs.begin(), dis_to_obs.end()) << " m" << endl;

    start_time = ros::Time::now();
    while((ros::Time::now() - start_time).toSec() < 8.0 && ros::ok()) {
        Vector3d pos = px4.global_pos();
        Vector4d quat = px4.global_quat();
        ros_inte.publish_pose(pos, quat);
        ros_inte.publish_quadmesh(pos, quat);
        px4.set_pos(p_end[0] - px4.origin_position_[0] , p_end[1] - px4.origin_position_[1], 0, Eigen::Quaterniond::Identity());
        rate.sleep();
        // ros_inte.publish_fanmesh(Vector3d(8.6, 4.0, 1.5), Vector3d(0, fan_ang, M_PI / 180.0 * 90.));
        // fan_ang += M_PI / 180.0 * 10;
        ros::spinOnce();
    }
    cout << " fail_cnt: " << fail_cnt << endl;

    // px4.set_px4_mode("POSCTL");

    cout << "avg_avg_vel: " << avg_avg_vel << endl;
    cout << "avg_max_vel: " << avg_max_vel << endl;
    cout << "avg_min_dist: " << avg_min_dist << endl;
    cout << "success rate: " << 1 - (double)fail_cnt << endl;

    return 0;
}

