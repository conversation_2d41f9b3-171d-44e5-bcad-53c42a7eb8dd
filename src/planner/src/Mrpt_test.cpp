#include <iostream>
#include "Eigen/Dense"
#include "MRPT/Mrpt.h"

int main() 
{
  int n = 100, d = 3, k = 1;
  double target_recall = 0.9;
  Eigen::MatrixXf X = Eigen::MatrixXf::Random(d, n);
  Eigen::MatrixXf q = Eigen::VectorXf::Random(d);

  Eigen::VectorXi indices(k), indices_exact(1);

  Mrpt::exact_knn(q, X, 1, indices_exact.data());
  std::cerr << indices_exact(0) << std::endl;

  // Mrpt mrpt(X);
  // mrpt.grow_autotune(target_recall, k);

  // mrpt.query(q, indices.data());
  // std::cerr << indices.transpose() << std::endl;
}