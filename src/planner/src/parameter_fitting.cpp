#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <Eigen/Dense>
#include <Eigen/Core>

using namespace std;
using namespace Eigen;

struct LogData {
    Vector3d vel;          // velocity (index 3-5)
    Vector3d acc;          // acceleration (index 6-8)
    Quaterniond quat;      // quaternion (index 9-12: w,x,y,z)
    double thrust_cmd;     // thrust command (index 26, u[3])
};

class ParameterFitter {
public:
    ParameterFitter() : g_(9.79362) {}
    
    bool loadData(const string& filename) {
        ifstream file(filename);
        if (!file.is_open()) {
            cerr << "Cannot open file: " << filename << endl;
            return false;
        }
        
        string line;
        data_.clear();
        
        while (getline(file, line)) {
            istringstream iss(line);
            vector<double> values;
            double value;
            
            // 读取每行的31个数值
            while (iss >> value) {
                values.push_back(value);
            }
            
            if (values.size() != 31) {
                continue; // 跳过格式不正确的行
            }
            
            LogData logData;
            // 只保留需要的数据
            logData.vel << values[3], values[4], values[5];          // 速度
            logData.acc << values[6], values[7], values[8];          // 加速度
            logData.quat = Quaterniond(values[9], values[10], values[11], values[12]); // 四元数 w,x,y,z
            logData.thrust_cmd = values[26];                         // 推力指令 u[3]
            
            data_.push_back(logData);
        }
        
        file.close();
        cout << "Loaded " << data_.size() << " data points" << endl;
        return true;
    }
    
    Vector4d fitParameters(double hover_thrust) {
        if (data_.empty()) {
            cerr << "No data for fitting" << endl;
            return Vector4d::Zero();
        }
        
        int N = data_.size();
        MatrixXd A(3 * N, 4);  // 每个数据点产生3个方程（x,y,z分量）
        VectorXd b(3 * N);
        
        Vector3d z_W(0, 0, 1);  // 世界坐标系z轴
        
        for (int i = 0; i < N; i++) {
            const LogData& d = data_[i];
            
            // 从四元数获取旋转矩阵
            Matrix3d R = d.quat.toRotationMatrix();
            Vector3d x_B = R.col(0);  // 机体坐标系x轴
            Vector3d y_B = R.col(1);  // 机体坐标系y轴
            Vector3d z_B = R.col(2);  // 机体坐标系z轴
            
            // 计算水平速度 v_h = v^T(x_B + y_B)
            double v_h = d.vel.dot(x_B + y_B);
            
            // 推力指令
            double c_cmd = d.thrust_cmd * (g_ / hover_thrust);
            
            // 构建线性系统
            // 动力学方程: acc + RDR^T*v = c*z_B - g*z_W
            // 其中 c = c_cmd + k_h * v_h^2
            // RDR^T*v = d_x*(x_B^T*v)*x_B + d_y*(y_B^T*v)*y_B + d_z*(z_B^T*v)*z_B
            
            // 重新排列为: d_x*(x_B^T*v)*x_B + d_y*(y_B^T*v)*y_B + d_z*(z_B^T*v)*z_B - k_h*v_h^2*z_B = c_cmd*z_B - g*z_W - acc
            
            double v_x_B = d.vel.dot(x_B);  // x_B^T * v
            double v_y_B = d.vel.dot(y_B);  // y_B^T * v  
            double v_z_B = d.vel.dot(z_B);  // z_B^T * v
            
            // 对于每个空间分量（x, y, z）
            for (int j = 0; j < 3; j++) {
                int row = i * 3 + j;
                
                // 系数矩阵A
                A(row, 0) = v_x_B * x_B(j);              // d_x 的系数
                A(row, 1) = v_y_B * y_B(j);              // d_y 的系数
                A(row, 2) = v_z_B * z_B(j);              // d_z 的系数
                A(row, 3) = -v_h * v_h * z_B(j);         // k_h 的系数
                
                // 目标向量b
                b(row) = c_cmd * z_B(j) - g_ * z_W(j) - d.acc(j);
            }
        }
        
        // 求解最小二乘问题 A * theta = b
        // theta = [d_x, d_y, d_z, k_h]^T
        Vector4d theta = A.colPivHouseholderQr().solve(b);
        
        return theta;
    }
    
    void analyzeResults(const Vector4d& params, double hover_thrust) {
        if (data_.empty()) return;
        
        cout << "Mass-normalized rotor drag coefficients:" << endl;
        cout << "  d_x = " << params(0) << endl;
        cout << "  d_y = " << params(1) << endl;
        cout << "  d_z = " << params(2) << endl;
        cout << "Thrust model coefficient:" << endl;
        cout << "  k_h = " << params(3) << endl;
        
        // 计算拟合误差
        double total_error_air = 0.0;
        double total_error_ = 0.0;
        int N = data_.size();
        Vector3d z_W(0, 0, 1);
        
        for (int i = 0; i < N; i++) {
            const LogData& d = data_[i];
            Matrix3d R = d.quat.toRotationMatrix();
            Vector3d x_B = R.col(0);
            Vector3d y_B = R.col(1);
            Vector3d z_B = R.col(2);
            
            double v_h = d.vel.dot(x_B + y_B);
            double c_cmd = d.thrust_cmd * (g_ / hover_thrust);
            
            // 计算预测的加速度
            Matrix3d D = params.head<3>().asDiagonal();
            Vector3d acc_air = (c_cmd + params(3) * v_h * v_h) * z_B - g_ * z_W - R * D * R.transpose() * d.vel;
            Vector3d acc_ = c_cmd * z_B - g_ * z_W;
            
            // 计算误差
            Vector3d error_air = d.acc - acc_air;
            Vector3d error_ = d.acc - acc_;
            total_error_air += error_air.norm();
            total_error_ += error_.norm();
        }
        
        cout << "\n========== Fitting Quality ==========" << endl;
        cout << "Number of data points: " << N << endl;
        cout << "Average error_air: " << total_error_air / N << " m/s²" << endl;
        cout << "Average error: " << total_error_ / N << " m/s²" << endl;
        
        // analyze parameter validity
        cout << "\n========== Parameter Analysis ==========" << endl;
        if (params(0) > 0 && params(1) > 0 && params(2) > 0) {
            cout << "All drag coefficients are positive" << endl;
        } else {
            cout << "Warning: negative drag coefficients found, check data or model" << endl;
        }
    }
    
private:
    vector<LogData> data_;
    double g_;  // 重力加速度
};

int main(int argc, char** argv) {
    // ROS launch会传递额外的参数，我们只需要前两个用户参数
    // argv[0] = 程序名
    // argv[1] = 数据文件路径
    // argv[2] = hover_ratio
    // argv[3+] = ROS内部参数 (__name:=..., __log:=..., 等)
    
    if (argc < 3) {
        return -1;
    }
    
    string filename = argv[1];
    double hover_ratio = atof(argv[2]);
    
    cout << "Parameter Fitting Tool" << endl;
    cout << "Data file: " << filename << endl;
    cout << "Hover ratio: " << hover_ratio << endl;
    cout << "Arguments received: " << argc << endl;
    
    ParameterFitter fitter;
    
    // 加载数据
    if (!fitter.loadData(filename)) {
        cerr << "Failed to load data from: " << filename << endl;
        return -1;
    }
    
    // fit parameters
    cout << "\nStarting parameter fitting..." << endl;
    Vector4d params = fitter.fitParameters(hover_ratio);
    
    // 分析结果
    fitter.analyzeResults(params, hover_ratio);
    
    return 0;
} 