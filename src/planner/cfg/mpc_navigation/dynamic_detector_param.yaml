localization_mode: 1 # 0: pose (default) 1: odom

depth_image_topic: /no_topic
aligned_depth_image_topic: /no_topic
odom_topic: /iris_0/mavros/vision_odom/odom

# Camera Parameters
# depth_intrinsics: [386.22674560546875, 386.22674560546875, 317.3930969238281, 239.78431701660156] # fx, fy, cx, cy real   rostopic echo  /camera/depth/camera_info获取
depth_intrinsics: [319.9348449707031, 319.9348449707031, 320.0, 240.0] # fx,  fy, cx, cy simulation  
# color_intrinsics: [608.08740234375, 608.08740234375, 317.48284912109375, 234.11557006835938] # fx, fy, cx, cy  real rostopic echo /camera/color/camera_info获取
color_intrinsics: [462.266357421875, 462.266357421875, 320.0, 240.0] # fx,  fy, cx, cy simulation 
# depth_scale_factor: 1000 # 1000 for Intel Realsense Camera
depth_scale_factor: 10 # simulation
depth_min_value: 0.5
depth_max_value: 5.0
depth_filter_margin: 10 # filter
depth_skip_pixel: 2  # filter
image_cols: 640
image_rows: 480
body_to_camera: [0.0,  0.0,  1.0,  0.03,  # 1.0 -1.0 -1.0 代表姿态的旋转，0.03 0.0 0.06 代表无人机坐标系下x,y z轴上的偏移量 最后一行默认为 0.0 0.0 0.0 1.0
                 -1.0,  0.0,  0.0,  0.0 ,   
                 0.0, -1.0,  0.0,  0.06,
                 0.0,  0.0,  0.0,  1.0]      # 与setupTF.launch中的body_to_camera_tf一致，根据那里的数据来调整即可。
body_to_camera_color: [0.0,  0.0,  1.0,  0.03,
                -1.0,  0.0,  0.0,  0.0 ,   
                 0.0, -1.0,  0.0,  0.06,
                 0.0,  0.0,  0.0,  1.0]



# Raycasting (max depth)
raycast_max_length: 5.0

# time difference
time_difference: 0.033

# sensor data processing
voxel_occupied_thresh: 15 # min num of points for a voxel to be occupied in voxel filter

# dbscan
ground_height: 0.1 # height of ground to remove ground points
dbscan_min_points_cluster: 20 # 20: 4.0m range; 30: 3.5m range 40: 3.0m range
dbscan_search_range_epsilon: 0.1 # searching range radius

# bounding box filtering
filtering_BBox_IOU_threshold: 0.05
yolo_overwrite_distance: 3

# tracking and data association
history_size: 100 # size of tracking history. history[0] is current detection
prediction_size: 20 # size of prediction
similarity_threshold: 0.02 # similiary threshold for data association matching comparison
retrack_similarity_threshold: 0.015  # similiary threshold for retracking
fix_size_history_threshold: 10 # History threshold (num of frames) to fix box size
fix_size_dimension_threshold: 0.4 # dimension threshold (size of proportional) to fix box size

e_p: 0.25
e_q_pos: 0.01
e_q_vel: 0.05
e_q_acc: 0.05
e_r_pos: 0.04
e_r_vel: 0.3
e_r_acc: 0.6

kalman_filter_averaging_frames: 10

# classification
frame_skip: 5 # num of frames skiped when comparing 2 point clouds
dynamic_velocity_threshold: 0.15
dynamic_voting_threshold: 0.8
maximum_skip_ratio: 0.5 # the upper limit of points that are skipped(out of previous FOV) to be classfified as dynamic
frames_force_dynamic: 10 # Range of searching dynamic obstacles in box history
frames_force_dynamic_check_range: 30 # threshold for forcing dynamic obstacles
dynamic_consistency_threshold: 5 # obstacles being voted as dynamic for continuous k frames are eligible to be classified as dynamic

# constrain size
constrain_size: true
target_object_size: [0.5, 0.5, 1.7]