sensor_input_mode: 0 # 0 camera 1: lidar
localization_mode: 1 # 0: pose (default) 1: odom
# depth_image_topic: /camera/depth/image_raw # 用于实时建图
depth_image_topic: /no_topic
odom_topic: /iris_0/mavros/vision_odom/odom

# robot size
robot_size: [0.4, 0.4, 0.3] # 长(x)宽（y）高(z)

# Camera Parameters
depth_intrinsics: [554.254691191187, 554.254691191187, 320.5, 240.5] # fx,  fy, cx, cy
depth_scale_factor: 10 # 1000 for Intel Realsense Camera
depth_min_value: 0.5
depth_max_value: 5.0
depth_filter_margin: 2 # filter
depth_skip_pixel: 2 # filter
image_cols: 640
image_rows: 480
body_to_camera: [0.0,  0.0,  1.0,  0.03,
                -1.0,  0.0,  0.0,  0.0 ,   
                 0.0, -1.0,  0.0,  0.06,
                 0.0,  0.0,  0.0,  1.0]

# Raycasting
raycast_max_length: 5.0
p_hit: 0.70
p_miss: 0.35
p_min: 0.12
p_max: 0.97
p_occ: 0.80


# Map
map_resolution: 0.05
ground_height: -0.1 # m
map_size: [18, 9, 3] # meter. in x y z direction (reserved size) 这个范围必须要大于等于实际地图的大小
local_update_range: [5, 5, 5]
local_bound_inflation: 3.0 # inflate local bound in meter
clean_local_map: false

# visualziation
local_map_size: [19, 10, 3] # meter. in x y z direction (only for visualization)
max_height_visualization: 2.5 # m
visualize_global_map: true
verbose: false

# 预创建的静态地图点云数据文件
prebuilt_map_directory: "/cfg/saved_map/sim_map.pcd" 
sdf_save_directory: "/cfg/saved_map/sdf_map.sdf"