#pragma once

#include <iostream>
#include <Eigen/Eigen>

using namespace std;
using namespace Eigen;

inline Vector3d quaternion_to_rpy(const Quaterniond &q) {
    const double &qw = q.w();
    const double &qx = q.x();
    const double &qy = q.y();
    const double &qz = q.z();

    Vector3d rpy;
    /* roll  */ rpy.x() = atan2(2. * (qw*qx + qy*qz), 1. - 2. * (qx*qx + qy*qy));
    double sin_pitch = 2. * (qw*qy - qz*qx);
    // sin_pitch = sin_pitch >  1.0 ?  1.0 : sin_pitch;
    // sin_pitch = sin_pitch < -1.0 ? -1.0 : sin_pitch;
    /* pitch */ rpy.y() = asin(sin_pitch);
    /* yaw   */ rpy.z() = atan2(2. * (qw*qz + qx*qy), 1. - 2. * (qy*qy + qz*qz));
    return rpy;
}

inline Quaterniond rpy_to_quaternion(Vector3d &rpy) // yaw (Z), pitch (Y), roll (X)
{
    // Abbreviations for the various angular functions
    double cy = cos(rpy[2] * 0.5);
    double sy = sin(rpy[2] * 0.5);
    double cp = cos(rpy[1] * 0.5);
    double sp = sin(rpy[1] * 0.5);
    double cr = cos(rpy[0] * 0.5);
    double sr = sin(rpy[0] * 0.5);
 
    Quaterniond q;
    q.w() = cy * cp * cr + sy * sp * sr;
    q.x() = cy * cp * sr - sy * sp * cr;
    q.y() = sy * cp * sr + cy * sp * cr;
    q.z() = sy * cp * cr - cy * sp * sr;
 
    return q;
}

inline Matrix<double, 3, 3> quaternion_to_matrix(const Quaterniond &q) {
    auto &x = q.x();
    auto &y = q.y();
    auto &z = q.z();
    auto &w = q.w();
    Matrix<double, 3, 3> m;
    m << 1 - 2 * (y * y + z * z), 2 * (x * y - w * z), 2 * (x * z + w * y),
        2 * (x * y + w * z), 1 - 2 * (x * x + z * z), 2 * (y * z - w * x),
        2 * (x * z - w * y), 2 * (y * z + w * x), 1 - 2 * (x * x + y * y);
    return m;
}


inline Quaterniond matrix_to_quaternion(const Matrix<double, 3, 3> &R , Matrix<double, 4, 9> *grad)
{
  Quaterniond q;
  double  tr = R(0,0) + R(1,1) + R(2,2);
  if (tr > 0) 
  { 
    double S = sqrt(tr + 1.0) * 2; 
    q.w() = 0.25 * S;
    q.x() = (R(2,1) - R(1,2)) / S;
    q.y() = (R(0,2) - R(2,0)) / S; 
    q.z() = (R(1,0) - R(0,1)) / S; 

    if(grad){
      (*grad)(0, 0) = 0.25 / (S / 2);
      (*grad)(0, 1) = 0;
      (*grad)(0, 2) = 0;
      (*grad)(0, 3) = 0;
      (*grad)(0, 4) = 0.25 / (S / 2);
      (*grad)(0, 5) = 0;
      (*grad)(0, 6) = 0;
      (*grad)(0, 7) = 0;
      (*grad)(0, 8) = 0.25 / (S / 2);

      (*grad)(1, 0) = (R(1,2) - R(2,1)) / (4 * pow(S / 2, 3));
      (*grad)(1, 1) = 0;
      (*grad)(1, 2) = 0;
      (*grad)(1, 3) = 0;
      (*grad)(1, 4) = (R(1,2) - R(2,1)) / (4 * pow(S / 2, 3));
      (*grad)(1, 5) = -1 / S;
      (*grad)(1, 6) = 0;
      (*grad)(1, 7) = 1 / S;
      (*grad)(1, 8) = (R(1,2) - R(2,1)) / (4 * pow(S / 2, 3));

      (*grad)(2, 0) = (R(2,0) - R(0,2)) / (4 * pow(S / 2, 3));
      (*grad)(2, 1) = 0;
      (*grad)(2, 2) = 1 / S;
      (*grad)(2, 3) = 0;
      (*grad)(2, 4) = (R(2,0) - R(0,2)) / (4 * pow(S / 2, 3));
      (*grad)(2, 5) = 0;
      (*grad)(2, 6) = -1 / S;
      (*grad)(2, 7) = 0;
      (*grad)(2, 8) = (R(2,0) - R(0,2)) / (4 * pow(S / 2, 3));

      (*grad)(3, 0) = (R(0,1) - R(1,0)) / (4 * pow(S / 2, 3));
      (*grad)(3, 1) = -1 / S;
      (*grad)(3, 2) = 0;
      (*grad)(3, 3) = 1 / S;
      (*grad)(3, 4) = (R(0,1) - R(1,0)) / (4 * pow(S / 2, 3));
      (*grad)(3, 5) = 0;
      (*grad)(3, 6) = 0;
      (*grad)(3, 7) = 0;
      (*grad)(3, 8) = (R(0,1) - R(1,0)) / (4 * pow(S / 2, 3));
    }

  } 
  else if (R(0,0) > R(1,1) && R(0,0) > R(2,2)) 
  { 
    double S = sqrt(1.0 + R(0,0) - R(1,1) - R(2,2)) * 2; 
    q.w() = (R(2,1) - R(1,2)) / S;
    q.x() = 0.25 * S;
    q.y() = (R(0,1) + R(1,0)) / S; 
    q.z() = (R(0,2) + R(2,0)) / S; 

    if (grad) {
      (*grad)(0, 0) = (R(1,2) - R(2,1)) / (4 * pow(S / 2, 3));
      (*grad)(0, 1) = 0;
      (*grad)(0, 2) = 0;
      (*grad)(0, 3) = 0;
      (*grad)(0, 4) = (R(2,1) - R(1,2)) / (4 * pow(S / 2, 3));
      (*grad)(0, 5) = -1 / S;
      (*grad)(0, 6) = 0;
      (*grad)(0, 7) = 1 / S;
      (*grad)(0, 8) = (R(2,1) - R(1,2)) / (4 * pow(S / 2, 3));

      (*grad)(1, 0) = 0.25 / (S / 2);
      (*grad)(1, 1) = 0;
      (*grad)(1, 2) = 0;
      (*grad)(1, 3) = 0;
      (*grad)(1, 4) = -0.25 / (S / 2);
      (*grad)(1, 5) = 0;
      (*grad)(1, 6) = 0;
      (*grad)(1, 7) = 0;
      (*grad)(1, 8) = -0.25 / (S / 2);

      (*grad)(2, 0) = -(R(1,0) + R(0,1)) / (4 * pow(S / 2, 3));
      (*grad)(2, 1) = 1 / S;
      (*grad)(2, 2) = 0;
      (*grad)(2, 3) = 1 / S;
      (*grad)(2, 4) = (R(0,1) + R(1,0)) / (4 * pow(S / 2, 3));
      (*grad)(2, 5) = 0;
      (*grad)(2, 6) = 0;
      (*grad)(2, 7) = 0;
      (*grad)(2, 8) = (R(0,1) + R(1,0)) / (4 * pow(S / 2, 3));

      (*grad)(3, 0) = -(R(0,2) + R(2,0)) / (4 * pow(S / 2, 3));
      (*grad)(3, 1) = 0;
      (*grad)(3, 2) = 1 / S;
      (*grad)(3, 3) = 0;
      (*grad)(3, 4) = (R(0,2) + R(2,0)) / (4 * pow(S / 2, 3));
      (*grad)(3, 5) = 0;
      (*grad)(3, 6) = 1 / S;
      (*grad)(3, 7) = 0;
      (*grad)(3, 8) = (R(0,2) + R(2,0)) / (4 * pow(S / 2, 3));
    }
  } 
  else if (R(1,1) > R(2,2)) 
  { 
    double S = sqrt(1.0 + R(1,1) - R(0,0) - R(2,2)) * 2; 
    q.w() = (R(0,2) - R(2,0)) / S;
    q.x() = (R(0,1) + R(1,0)) / S; 
    q.y() = 0.25 * S;
    q.z() = (R(1,2) + R(2,1)) / S; 


    if (grad) {
      (*grad)(0, 0) = (R(0,2) - R(2,0)) / (4 * pow(S / 2, 3));
      (*grad)(0, 1) = 0;
      (*grad)(0, 2) = 1 / S;
      (*grad)(0, 3) = 0;
      (*grad)(0, 4) = (R(2,0) - R(0,2)) / (4 * pow(S / 2, 3));
      (*grad)(0, 5) = 0;
      (*grad)(0, 6) = -1 / S;
      (*grad)(0, 7) = 0;
      (*grad)(0, 8) = (R(0,2) - R(2,0)) / (4 * pow(S / 2, 3));

      (*grad)(1, 0) = (R(0,1) + R(1,0)) / (4 * pow(S / 2, 3));
      (*grad)(1, 1) = 1 / S;
      (*grad)(1, 2) = 0;
      (*grad)(1, 3) = 1 / S;
      (*grad)(1, 4) = -(R(0,1) + R(1,0)) / (4 * pow(S / 2, 3));
      (*grad)(1, 5) = 0;
      (*grad)(1, 6) = 0;
      (*grad)(1, 7) = 0;
      (*grad)(1, 8) = (R(0,1) + R(1,0)) / (4 * pow(S / 2, 3));

      (*grad)(2, 0) = -0.25 / (S / 2);
      (*grad)(2, 1) = 0;
      (*grad)(2, 2) = 0;
      (*grad)(2, 3) = 0;
      (*grad)(2, 4) = 0.25 / (S / 2);
      (*grad)(2, 5) = 0;
      (*grad)(2, 6) = 0;
      (*grad)(2, 7) = 0;
      (*grad)(2, 8) = -0.25 / (S / 2);

      (*grad)(3, 0) = (R(1,2) + R(2,1)) / (4 * pow(S / 2, 3));
      (*grad)(3, 1) = 0;
      (*grad)(3, 2) = 0;
      (*grad)(3, 3) = 0;
      (*grad)(3, 4) = -(R(1,2) + R(2,1)) / (4 * pow(S / 2, 3));
      (*grad)(3, 5) = 1 / S;
      (*grad)(3, 6) = 0;
      (*grad)(3, 7) = 1 / S;
      (*grad)(3, 8) = (R(1,2) + R(2,1)) / (4 * pow(S / 2, 3));
    }
  } 
  else 
  { 
    double S = sqrt(1.0 + R(2,2) - R(0,0) - R(1,1)) * 2; 
    q.w() = (R(1,0) - R(0,1)) / S;
    q.x() = (R(0,2) + R(2,0)) / S;
    q.y() = (R(1,2) + R(2,1)) / S;
    q.z() = 0.25 * S;

    if (grad) {
      (*grad)(0, 0) = (R(1,0) - R(0,1)) / (4 * pow(S / 2, 3));
      (*grad)(0, 1) = -1 / S;
      (*grad)(0, 2) = 0;
      (*grad)(0, 3) = 1 / S;
      (*grad)(0, 4) = (R(1,0) - R(0,1)) / (4 * pow(S / 2, 3));
      (*grad)(0, 5) = 0;
      (*grad)(0, 6) = 0;
      (*grad)(0, 7) = 0;
      (*grad)(0, 8) = (R(0,1) - R(1,0)) / (4 * pow(S / 2, 3));

      (*grad)(1, 0) = (R(0,2) + R(2,0)) / (4 * pow(S / 2, 3));
      (*grad)(1, 1) = 0;
      (*grad)(1, 2) = 1 / S;
      (*grad)(1, 3) = 0;
      (*grad)(1, 4) = (R(0,2) + R(2,0)) / (4 * pow(S / 2, 3));
      (*grad)(1, 5) = 0;
      (*grad)(1, 6) = 1 / S;
      (*grad)(1, 7) = 0;
      (*grad)(1, 8) = -(R(0,2) + R(2,0)) / (4 * pow(S / 2, 3));

      (*grad)(2, 0) = (R(1,2) + R(2,1)) / (4 * pow(S / 2, 3));
      (*grad)(2, 1) = 0;
      (*grad)(2, 2) = 0;
      (*grad)(2, 3) = 0;
      (*grad)(2, 4) = (R(1,2) + R(2,1)) / (4 * pow(S / 2, 3));
      (*grad)(2, 5) = 1 / S;
      (*grad)(2, 6) = 0;
      (*grad)(2, 7) = 1 / S;
      (*grad)(2, 8) = -(R(1,2) + R(2,1)) / (4 * pow(S / 2, 3));
      
      (*grad)(3, 0) = -0.25 / (S / 2);
      (*grad)(3, 1) = 0;
      (*grad)(3, 2) = 0;
      (*grad)(3, 3) = 0;
      (*grad)(3, 4) = -0.25 / (S / 2);
      (*grad)(3, 5) = 0;
      (*grad)(3, 6) = 0;
      (*grad)(3, 7) = 0;
      (*grad)(3, 8) = 0.25 / (S / 2);
    }
  }
  return q;
}

inline Matrix<double, 3, 3> rpy_to_matrix(const Vector3d &rpy)
{
  double c, s;
  Matrix<double, 3, 3> Rz = Matrix<double, 3, 3>::Zero();
  double y = rpy(2);
  c = cos(y);
  s = sin(y);
  Rz(0,0) =  c;
  Rz(1,0) =  s;
  Rz(0,1) = -s;
  Rz(1,1) =  c;
  Rz(2,2) =  1; 

  Matrix<double, 3, 3> Ry = Matrix<double, 3, 3>::Zero();
  double p = rpy(1);
  c = cos(p);
  s = sin(p);
  Ry(0,0) =  c;
  Ry(2,0) = -s;
  Ry(0,2) =  s;
  Ry(2,2) =  c;
  Ry(1,1) =  1; 

  Matrix<double, 3, 3> Rx = Matrix<double, 3, 3>::Zero();
  double r = rpy(0);
  c = cos(r);
  s = sin(r);
  Rx(1,1) =  c;
  Rx(2,1) =  s;
  Rx(1,2) = -s;
  Rx(2,2) =  c;
  Rx(0,0) =  1; 

  Matrix<double, 3, 3> R = Rz * Ry * Rx;  
  return R;
}

inline Vector3d matrix_to_rpy(const Matrix<double, 3, 3> &R)
{
  Vector3d n = R.col(0);
  Vector3d o = R.col(1);
  Vector3d a = R.col(2);

  Vector3d rpy;
  double y = atan2(n(1), n(0));
  double p = atan2(-n(2), n(0)*cos(y)+n(1)*sin(y));
  double r = atan2(a(0)*sin(y)-a(1)*cos(y), -o(0)*sin(y)+o(1)*cos(y));
  rpy(0) = r;
  rpy(1) = p;
  rpy(2) = y;

  return rpy;
}

inline Quaterniond quaternion_mul(const Quaterniond &q1, const Quaterniond &q2)
{
  double a1 = q1.w();
  double b1 = q1.x();
  double c1 = q1.y();
  double d1 = q1.z();
    
  double a2 = q2.w();
  double b2 = q2.x();
  double c2 = q2.y();
  double d2 = q2.z();
    
  Quaterniond q3;
  q3.w() = a1*a2 - b1*b2 - c1*c2 - d1*d2;
  q3.x() = a1*b2 + b1*a2 + c1*d2 - d1*c2;
  q3.y() = a1*c2 - b1*d2 + c1*a2 + d1*b2;
  q3.z() = a1*d2 + b1*c2 - c1*b2 + d1*a2;
  return q3;
}


inline Quaterniond quaternion_inv(const Quaterniond &q)
{
  Quaterniond q2;
  q2.w() =  q.w();
  q2.x() = -q.x();
  q2.y() = -q.y();
  q2.z() = -q.z();    
  return q2;
}

inline Quaterniond acc2quaternion(const Eigen::Vector3d &vector_acc, const double &yaw, Matrix<double, 4, 4> *grad) {
    Quaterniond q;
    Vector3d zb_des, yb_des, xb_des, proj_xb_des;
    Matrix3d rotmat;

    proj_xb_des << cos(yaw), sin(yaw), 0.0;

    zb_des = vector_acc / vector_acc.norm();
    yb_des = zb_des.cross(proj_xb_des) / (zb_des.cross(proj_xb_des)).norm();
    xb_des = yb_des.cross(zb_des) / (yb_des.cross(zb_des)).norm();

    rotmat << xb_des(0), yb_des(0), zb_des(0), xb_des(1), yb_des(1), zb_des(1), xb_des(2), yb_des(2), zb_des(2);

    Matrix<double, 4, 9> grad1;
    q = matrix_to_quaternion(rotmat, &grad1);

    if(grad){
      Matrix<double, 3, 4> proj_xb_des_g, zb_des_g, yb_des_g, xb_des_g;
      Matrix<double, 6, 4> combined_g1, combined_g2;
      Matrix<double, 3, 6> yb_z_p_g, xb_y_z_g;
      Matrix<double, 9, 4> grad2;
      double ax = vector_acc(0);
      double ay = vector_acc(0);
      double az = vector_acc(0);
      double p0 = proj_xb_des(0);
      double p1 = proj_xb_des(1);
      double p2 = proj_xb_des(2);
      double z0 = zb_des(0);
      double z1 = zb_des(1);
      double z2 = zb_des(2);
      double y0 = yb_des(0);
      double y1 = yb_des(1);
      double y2 = yb_des(2);
      zb_des_g(0, 0) = (ay * ay + az * az) / pow(vector_acc.norm(), 3);
      zb_des_g(0, 1) = -ax * ay / pow(vector_acc.norm(), 3);
      zb_des_g(0, 2) = -ax * az / pow(vector_acc.norm(), 3);
      zb_des_g(0, 3) = 0;

      zb_des_g(1, 0) = -ax * ay / pow(vector_acc.norm(), 3);
      zb_des_g(1, 1) = (ax * ax + az * az) / pow(vector_acc.norm(), 3);
      zb_des_g(1, 2) = -ay * az / pow(vector_acc.norm(), 3);
      zb_des_g(1, 3) = 0;

      zb_des_g(2, 0) = -ax * az / pow(vector_acc.norm(), 3);
      zb_des_g(2, 1) = -ay * az / pow(vector_acc.norm(), 3);
      zb_des_g(2, 2) = (ax * ax + ay * ay) / pow(vector_acc.norm(), 3);
      zb_des_g(2, 3) = 0;

      proj_xb_des_g(0, 0) = 0;
      proj_xb_des_g(0, 1) = 0;
      proj_xb_des_g(0, 2) = 0;
      proj_xb_des_g(0, 3) = -sin(yaw);

      proj_xb_des_g(1, 0) = 0;
      proj_xb_des_g(1, 1) = 0;
      proj_xb_des_g(1, 2) = 0;
      proj_xb_des_g(1, 3) = cos(yaw);

      proj_xb_des_g(2, 0) = 0;
      proj_xb_des_g(2, 1) = 0;
      proj_xb_des_g(2, 2) = 0;
      proj_xb_des_g(2, 3) = 0;

      combined_g1 << zb_des_g, proj_xb_des_g;


      double tmp_ = pow(p0*z1 - p1*z0, 2) + pow(p0*z2 - p2*z0, 2) + pow(p1*z2 - p2*z1, 2);
      double tmp = pow(tmp_, 1.5);
      yb_z_p_g(0, 0) = (p1*z2 - p2*z1)*(-p1*(p0*z1 - p1*z0) - p2*(p0*z2 - p2*z0))/tmp;
      yb_z_p_g(0, 1) = (p2*tmp_+ (p0*(p0*z1 - p1*z0) - p2*(p1*z2 - p2*z1))*(p1*z2 - p2*z1))/tmp;
      yb_z_p_g(0, 2) = (-p1*tmp_ + (p0*(p0*z2 - p2*z0) + p1*(p1*z2 - p2*z1))*(p1*z2 - p2*z1))/tmp;
      yb_z_p_g(0, 3) = (p1*z2 - p2*z1)*(z1*(p0*z1 - p1*z0) + z2*(p0*z2 - p2*z0))/tmp;
      yb_z_p_g(0, 4) = (-z2*tmp_ - (p1*z2 - p2*z1)*(z0*(p0*z1 - p1*z0) - z2*(p1*z2 - p2*z1)))/tmp;
      yb_z_p_g(0, 5) = (z1*tmp_ - (p1*z2 - p2*z1)*(z0*(p0*z2 - p2*z0) + z1*(p1*z2 - p2*z1)))/tmp;

      yb_z_p_g(1, 0) = (-p2*tmp_ + (p0*z2 - p2*z0)*(p1*(p0*z1 - p1*z0) + p2*(p0*z2 - p2*z0)))/tmp;
      yb_z_p_g(1, 1) = -(p0*z2 - p2*z0)*(p0*(p0*z1 - p1*z0) - p2*(p1*z2 - p2*z1))/tmp;
      yb_z_p_g(1, 2) = (p0*tmp_ - (p0*z2 - p2*z0)*(p0*(p0*z2 - p2*z0) + p1*(p1*z2 - p2*z1)))/tmp;
      yb_z_p_g(1, 2) = (z2*tmp_ - (p0*z2 - p2*z0)*(z1*(p0*z1 - p1*z0) + z2*(p0*z2 - p2*z0)))/tmp;
      yb_z_p_g(1, 4) = (p0*z2 - p2*z0)*(z0*(p0*z1 - p1*z0) - z2*(p1*z2 - p2*z1))/tmp;
      yb_z_p_g(1, 5) = (-z0*tmp_ + (p0*z2 - p2*z0)*(z0*(p0*z2 - p2*z0) + z1*(p1*z2 - p2*z1)))/tmp;

      yb_z_p_g(2, 0) = (p1*tmp_ - (p0*z1 - p1*z0)*(p1*(p0*z1 - p1*z0) + p2*(p0*z2 - p2*z0)))/tmp;
      yb_z_p_g(2, 1) = (-p0*tmp_ + (p0*z1 - p1*z0)*(p0*(p0*z1 - p1*z0) - p2*(p1*z2 - p2*z1)))/tmp;
      yb_z_p_g(2, 2) = (p0*z1 - p1*z0)*(p0*(p0*z2 - p2*z0) + p1*(p1*z2 - p2*z1))/tmp;
      yb_z_p_g(2, 3) = (-z1*tmp_ + (p0*z1 - p1*z0)*(z1*(p0*z1 - p1*z0) + z2*(p0*z2 - p2*z0)))/tmp;
      yb_z_p_g(2, 4) = (z0*tmp_ - (p0*z1 - p1*z0)*(z0*(p0*z1 - p1*z0) - z2*(p1*z2 - p2*z1)))/tmp;
      yb_z_p_g(2, 5) = -(p0*z1 - p1*z0)*(z0*(p0*z2 - p2*z0) + z1*(p1*z2 - p2*z1))/tmp;

      yb_des_g = yb_z_p_g * combined_g1;

      combined_g2 << yb_des_g, zb_des_g;

      double tmp1_ = pow(z0*y1 - z1*y0, 2) + pow(z0*y2 - z2*y0, 2) + pow(z1*y2 - z2*y1, 2);
      double tmp1 = pow(tmp1_, 1.5);
      xb_y_z_g(0, 0) = (z1*y2 - z2*y1)*(-z1*(z0*y1 - z1*y0) - z2*(z0*y2 - z2*y0))/tmp1;
      xb_y_z_g(0, 1) = (z2*tmp1_+ (z0*(z0*y1 - z1*y0) - z2*(z1*y2 - z2*y1))*(z1*y2 - z2*y1))/tmp1;
      xb_y_z_g(0, 2) = (-z1*tmp1_ + (z0*(z0*y2 - z2*y0) + z1*(z1*y2 - z2*y1))*(z1*y2 - z2*y1))/tmp1;
      xb_y_z_g(0, 3) = (z1*y2 - z2*y1)*(y1*(z0*y1 - z1*y0) + y2*(z0*y2 - z2*y0))/tmp1;
      xb_y_z_g(0, 4) = (-y2*tmp1_ - (z1*y2 - z2*y1)*(y0*(z0*y1 - z1*y0) - y2*(z1*y2 - z2*y1)))/tmp1;
      xb_y_z_g(0, 5) = (y1*tmp1_ - (z1*y2 - z2*y1)*(y0*(z0*y2 - z2*y0) + y1*(z1*y2 - z2*y1)))/tmp1;

      xb_y_z_g(1, 0) = (-z2*tmp1_ + (z0*y2 - z2*y0)*(z1*(z0*y1 - z1*y0) + z2*(z0*y2 - z2*y0)))/tmp1;
      xb_y_z_g(1, 1) = -(z0*y2 - z2*y0)*(z0*(z0*y1 - z1*y0) - z2*(z1*y2 - z2*y1))/tmp1;
      xb_y_z_g(1, 2) = (z0*tmp1_ - (z0*y2 - z2*y0)*(z0*(z0*y2 - z2*y0) + z1*(z1*y2 - z2*y1)))/tmp1;
      xb_y_z_g(1, 2) = (y2*tmp1_ - (z0*y2 - z2*y0)*(y1*(z0*y1 - z1*y0) + y2*(z0*y2 - z2*y0)))/tmp1;
      xb_y_z_g(1, 4) = (z0*y2 - z2*y0)*(y0*(z0*y1 - z1*y0) - y2*(z1*y2 - z2*y1))/tmp1;
      xb_y_z_g(1, 5) = (-y0*tmp1_ + (z0*y2 - z2*y0)*(y0*(z0*y2 - z2*y0) + y1*(z1*y2 - z2*y1)))/tmp1;

      xb_y_z_g(2, 0) = (z1*tmp1_ - (z0*y1 - z1*y0)*(z1*(z0*y1 - z1*y0) + z2*(z0*y2 - z2*y0)))/tmp1;
      xb_y_z_g(2, 1) = (-z0*tmp1_ + (z0*y1 - z1*y0)*(z0*(z0*y1 - z1*y0) - z2*(z1*y2 - z2*y1)))/tmp1;
      xb_y_z_g(2, 2) = (z0*y1 - z1*y0)*(z0*(z0*y2 - z2*y0) + z1*(z1*y2 - z2*y1))/tmp1;
      xb_y_z_g(2, 3) = (-y1*tmp1_ + (z0*y1 - z1*y0)*(y1*(z0*y1 - z1*y0) + y2*(z0*y2 - z2*y0)))/tmp1;
      xb_y_z_g(2, 4) = (y0*tmp1_ - (z0*y1 - z1*y0)*(y0*(z0*y1 - z1*y0) - y2*(z1*y2 - z2*y1)))/tmp1;
      xb_y_z_g(2, 5) = -(z0*y1 - z1*y0)*(y0*(z0*y2 - z2*y0) + y1*(z1*y2 - z2*y1))/tmp1;

      xb_des_g = xb_y_z_g * combined_g2;

      grad2.block(0, 0, 1, 4) = xb_des_g.block(0, 0, 1, 4);
      grad2.block(1, 0, 1, 4) = yb_des_g.block(0, 0, 1, 4);
      grad2.block(2, 0, 1, 4) = zb_des_g.block(0, 0, 1, 4);
      grad2.block(3, 0, 1, 4) = xb_des_g.block(1, 0, 1, 4);
      grad2.block(4, 0, 1, 4) = yb_des_g.block(1, 0, 1, 4);
      grad2.block(5, 0, 1, 4) = zb_des_g.block(1, 0, 1, 4);
      grad2.block(6, 0, 1, 4) = xb_des_g.block(2, 0, 1, 4);
      grad2.block(7, 0, 1, 4) = yb_des_g.block(2, 0, 1, 4);
      grad2.block(8, 0, 1, 4) = zb_des_g.block(2, 0, 1, 4);

      *grad = grad1 * grad2;
    }


    return q;
}