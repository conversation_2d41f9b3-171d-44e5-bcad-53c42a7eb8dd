#pragma once

#include <ros/ros.h>
#include <nav_msgs/Path.h>
#include <tf/transform_broadcaster.h>

#include "map/map.hpp"
#include "map/sdf.hpp"
#include "std_msgs/Int32.h" 
#include "std_msgs/Float32.h" 

class RosInterface {
private:
    ros::Publisher grid_map_pub_;
    ros::Publisher sdf_map_pub_;
    ros::Publisher quadmesh_pub_;
    ros::Publisher kino_traj_pub_;
    ros::Publisher bspline_traj_pub_;
    ros::Publisher mpcc_traj_pub_;
    ros::Publisher ball_traj_pub_;
    ros::Publisher predict_traj_pub_;
    ros::Publisher collision_pub_;
    ros::Publisher fanmesh_pub_;
    ros::Publisher dyn_obs_pub_;
    ros::Publisher pose_pub_;
    ros::Publisher parabola_pub_;
    ros::Publisher magnet_pub_;
    ros::Subscriber start_sub_;
    tf::TransformBroadcaster broadcaster;
    Vector3d map_size_;
public:
    bool start;
    RosInterface(Vector3d map_size);
    void publish_grid_map(GridMap &map);
    void publish_sdf_map(SdfMap &sdf);
    void publish_quadmesh(Vector3d pos, Vector4d quat);
    void publish_kino_traj(vector<Vector3d> &traj);
    void publish_bspline_traj(vector<Vector3d> &traj);
    void publish_mpcc_traj(vector<Vector3d> &traj);
    void publish_ball_traj(vector<Vector3d> &traj);
    void publish_predict_traj(vector<Vector3d> &traj);
    void publish_collision(vector<Vector3d> &pos);
    void publish_fanmesh(Vector3d pos, Vector3d ang);
    void publish_pose(Vector3d pos, Vector4d quat);
    void publish_parabola(MatrixXd path_);
    void publish_magnet(bool airdrop);
    nav_msgs::Path MatrixXdToNavMsgsPath(MatrixXd eigen_path);
    void startCallback(const std_msgs::Int32::ConstPtr& msg);
};