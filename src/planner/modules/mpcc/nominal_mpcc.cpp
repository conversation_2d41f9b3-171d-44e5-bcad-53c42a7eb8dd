#include "nominal_mpcc.hpp"

int cnt = 0;

VectorXd vector2Vector(const vector<double> &v) {
    VectorXd vv(v.size());
    for(int i = 0; i < v.size(); i++) {
        vv[i] = v[i];
    }
    return vv;
}

NominalMpcc::NominalMpcc(double hover_ratio, string algorithm, int maxeval) : 
#if USE_EXTENDED_DYNAMICS
    ExtendedQuadDynamic(hover_ratio),
#else
    NominalQuadDynamic(hover_ratio),
#endif
    dt_((double)_dt_num / _dt_den),
    opt_(algorithm.c_str(), u_dim_ * _n_step + _n_step + 1) {
    for (int k = 0; k < _n_step; k++) {
        state_[k].setZero();
        state_g_[k].setZero();
        acc_[k].setZero();
        acc_g_[k].setZero();
    }
    flag = false;
    opt_.set_min_objective((nlopt::vfunc)&NominalMpcc::cost_func, this);
    vector<double> tols(_n_step);
    for (int i = 0; i < tols.size(); i++) {
        tols[i] = 1e-8;
    }
    opt_.add_inequality_mconstraint((nlopt::mfunc)&NominalMpcc::v_b_constraint, this, tols);
    opt_.set_xtol_rel(1e-16);
    opt_.set_ftol_rel(1e-10);
    opt_.set_maxeval(maxeval);
    nlopt::opt local_opt("LD_LBFGS", u_dim_ * _n_step + _n_step + 1);
    local_opt.set_xtol_rel(1e-16);
    local_opt.set_ftol_rel(1e-10);
    local_opt.set_maxeval(maxeval);
    // local_opt.set_vector_storage(16);
    opt_.set_local_optimizer(local_opt);

    hover_ratio_ = hover_ratio;
}

//velocity constraints
void NominalMpcc::v_b_constraint(unsigned m, double *result, unsigned n, const double *u,
                            double *gradient, /* NULL if not needed */
                            NominalMpcc *instance) {
    Matrix<double, x_dim_, 1> *state = instance->state_;
    Matrix<double, x_dim_, _n_step * u_dim_> *state_g = instance->state_g_;
    const double &dt = instance->dt_;

    for (int k = 0; k < m; k++) {
        result[k] = pow(state[k][3], 2) + pow(state[k][4], 2) + pow(state[k][5], 2) - pow(20.0, 2);
        if (gradient) {
            for (int i = 0; i < _n_step * u_dim_; i++) {
                gradient[k * n + i] = 2 * state[k][3] * state_g[k](3, i) + 
                                        2 * state[k][4] * state_g[k](4, i) +
                                        2 * state[k][5] * state_g[k](5, i);
            }
            for (int i = _n_step * u_dim_; i < n; i++) {
                gradient[k * n + i] = 0;
            }
        }
    }
}

double NominalMpcc::cost_func(const vector<double> &u, vector<double> &grad, NominalMpcc *instance)
{
    Matrix<double, x_dim_, 1> *state = instance->state_;
    Matrix<double, x_dim_, _n_step * u_dim_> *state_g = instance->state_g_;
    Matrix<double, 3, 1> *acc = instance->acc_;
    Matrix<double, 3, _n_step * u_dim_> *acc_g = instance->acc_g_;
    const double &dt = instance->dt_;
    Matrix<double, _n_step, u_dim_> &ref_u = instance->ref_u_;
    auto &cost_w = instance->cost_w;

    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> g;
    g.setZero();
    Matrix<double, u_dim_, 1> uvec;

    //update preditive model using RK4 
    //Compared with Euler method, RK4 has better accuracy in solving the state equation
    uvec << u[0], u[1], u[2], u[3];
    Matrix<double, x_dim_, x_dim_> x1dotx0;
    Matrix<double, x_dim_, u_dim_> x1dotu;
    Matrix<double, 3, x_dim_> accdotx0;
    Matrix<double, 3, u_dim_> accdotu;
    instance->rk4_func(instance->init_state_, uvec, instance->disturbance_acc_, dt, state[0], x1dotx0, x1dotu, acc[0], accdotx0, accdotu);
    state_g[0].block(0, 0, x_dim_, u_dim_) = x1dotu;
    acc_g[0].block(0, 0, 3, u_dim_) = accdotu;
    for (int k = 1; k < _n_step; k++) {
        uvec << u[0 + k * u_dim_], u[1 + k * u_dim_], u[2 + k * u_dim_], u[3 + k * u_dim_];
        instance->rk4_func(state[k - 1], uvec, k < 2 ? instance->disturbance_acc_ : Vector3d(0, 0, 0), dt, state[k], x1dotx0, x1dotu, acc[k], accdotx0, accdotu);
        state_g[k].block(0, k * u_dim_, x_dim_, u_dim_) = x1dotu;
        state_g[k].block(0, 0, x_dim_, u_dim_ * k) = x1dotx0 * state_g[k - 1].block(0, 0, x_dim_, u_dim_ * k);
        acc_g[k].block(0, k * u_dim_, 3, u_dim_) = accdotu;
        acc_g[k].block(0, 0, 3, u_dim_ * k) = accdotx0 * state_g[k - 1].block(0, 0, x_dim_, u_dim_ * k);
    }

    //calculate reference state and tangent vector
    Matrix<double, 3, 1> ref_pos[_n_step + 1];
    Matrix<double, 3, _n_step + 1> ref_pos_g[_n_step + 1];
    Matrix<double, 3, 1> tangent[_n_step + 1];
    Matrix<double, 3, _n_step + 1> tangent_g[_n_step + 1];
    double t_sum = u[u_dim_ * _n_step + _n_step];
    double t_sum2 = u[u_dim_ * _n_step + _n_step];
    int count = 0;

    Matrix<double, 1, _n_step + 1> tp_g[_n_step + 1];
    double tp;
    double tp_k_g;
    double v_end_cost = 0.0;
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> v_end_cost_g;
    v_end_cost_g.setZero();

    Matrix<double, 3, 1> ref_acc[_n_step + 1];
    Matrix<double, 3, 1> ref_vel[_n_step + 1];
    Matrix<double, 3, _n_step + 1> ref_acc_g[_n_step + 1];
    Matrix<double, 3, _n_step + 1> ref_vel_g[_n_step + 1];
    Matrix<double, 4, _n_step + 1> grad_;
    Matrix<double, 1, 2> tmp_;
    Quaterniond q, qd, q_err;
    double q_err_w;
    Vector3d q_err_v, log_q_err;
    double qcost = 0.0;
    Matrix<double, 1, 4> qcost_err_g;
    Matrix<double, 4, 4> q_err_g1;
    Matrix<double, 4, 4> q_err_g2;
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> qcost_g; 
    qcost_g.setZero();
    Matrix<double, 4, 4> qd_grad;
    Matrix<double, 4, _n_step + 1> qd_grad_;

    for (int k = 0; k < _n_step + 1; k++) {
        bool over = false;
        if (t_sum >= instance->t_max_) {    // k = 0 时t_sum >= instance->t_max_不可能成立，因为限定了u[u_dim_ * _n_step + _n_step]的范围
            t_sum = instance->t_max_;
            over = true;
            count ++;
        }
        Matrix<double, 3, 1> ref_pos_k_g;
        Matrix<double, 3, 1> tmp2;
        Matrix<double, 3, 1> tmp = UniformBspline::getBsplineValueFast(instance->ts_, instance->v_ctrl_pts_, t_sum - instance->ts_, 2, &tmp2);   // 期望速度
        ref_pos[k] = UniformBspline::getBsplineValueFast(instance->ts_, instance->ctrl_pts_, t_sum, 3, &ref_pos_k_g);
        double snorm = tmp.squaredNorm();
        double norm = tmp.norm();
        tangent[k] = tmp / norm;     //  期望速度的单位向量
        ref_pos_g[k].setZero();
        tangent_g[k].setZero();

        Matrix<double, 3, 1> end_ref_vel_g;
        Matrix<double, 3, 1> end_ref_vel = UniformBspline::getBsplineValueFast(instance->ts_, instance->v_ctrl_pts_,  instance->t_max_ - instance->ts_, 2, &end_ref_vel_g); 
        tp = pow(t_sum / instance -> t_max_, 2);  // 要设为t_sum / instance -> t_max_，这样就与t_max_无关，范围是固定的。
        tp_k_g = 2.0 * (t_sum / instance -> t_max_) * (1 / instance -> t_max_);


        if(!over){
            ref_pos_g[k].block(0, _n_step, 3, 1) = ref_pos_k_g;
            tangent_g[k].block(0, _n_step, 3, 1) = (tmp2 * norm - tmp * 0.5 * (1 / norm) * 2 * tmp.dot(tmp2)) / snorm;
            for (int i = 0; i < k; i++){
                ref_pos_g[k].block(0, i, 3, 1) = ref_pos_k_g;
                tangent_g[k].block(0, i, 3, 1) = (tmp2 * norm - tmp * 0.5 * (1 / norm) * 2 * tmp.dot(tmp2)) / snorm;
            }


            if(k > 0){
                tp_g[k - 1].setZero();
                tp_g[k - 1].block(0, _n_step, 1, 1)(0, 0) = tp_k_g;
                for (int i = 0; i < k; i++){
                  tp_g[k - 1].block(0, i, 1, 1)(0, 0) = tp_k_g;
                }

                // 终点速度控制
                v_end_cost += tp * (pow(state[k - 1][3] - end_ref_vel(0), 2) + pow(state[k - 1][4] - end_ref_vel(1), 2) + pow(state[k - 1][5] - end_ref_vel(2), 2));
                v_end_cost_g.block(u_dim_ * _n_step, 0,  _n_step + 1, 1) +=  tp_g[k - 1].transpose() * (pow(state[k - 1][3] - end_ref_vel(0), 2) + pow(state[k - 1][4] - end_ref_vel(1), 2) + pow(state[k - 1][5] - end_ref_vel(2), 2));
                v_end_cost_g.block(0, 0, u_dim_ * _n_step, 1) += tp * (2 * (state[k - 1][3] - end_ref_vel(0)) * state_g[k - 1].block(3, 0, 1, u_dim_ * _n_step).transpose()
                                                                     + 2 * (state[k - 1][4] - end_ref_vel(1)) * state_g[k - 1].block(4, 0, 1, u_dim_ * _n_step).transpose()
                                                                     + 2 * (state[k - 1][5] - end_ref_vel(2)) * state_g[k - 1].block(5, 0, 1, u_dim_ * _n_step).transpose());
            }else{
                tp_g[0].setZero();
                tp_g[0].block(0, _n_step, 1, 1)(0, 0) = tp_k_g;
                v_end_cost += tp * (instance->init_state_.block(3, 0, 3, 1) - end_ref_vel).squaredNorm();
                v_end_cost_g.block(u_dim_ * _n_step, 0,  _n_step + 1, 1) += tp_g[0].transpose() * (instance->init_state_.block(3, 0, 3, 1) - end_ref_vel).squaredNorm();
            }
        }
        else{
            if(cost_w[13] && k > 0){   // 两者相互制约，缺一不可
                ref_pos[k] = ref_pos[k - 1] + tmp * u[u_dim_ * _n_step + k - 1]; 
                // ref_pos[k] = ref_pos[k - 1] + tmp * dt;
                ref_pos_g[k].block(0, _n_step, 3, 1) = ref_pos_g[k - 1].block(0, _n_step, 3, 1);
                tangent_g[k].block(0, _n_step, 3, 1) = tangent_g[k - 1].block(0, _n_step, 3, 1);
                for (int i = 0; i < k - count; i++) {
                    ref_pos_g[k].block(0, i, 3, 1) = ref_pos_g[k - 1].block(0, i, 3, 1);
                    tangent_g[k].block(0, i, 3, 1) = tangent_g[k - 1].block(0, i, 3, 1);
                }
                for (int i = k - count; i < k; i++) {
                    ref_pos_g[k].block(0, i, 3, 1) = tmp;
                }
                
                double tp2 = pow(t_sum2 / instance -> t_max_, 2); 
                double tp_k_g2 = 2.0 * (t_sum2 / instance -> t_max_) * (1 / instance -> t_max_);
                tp_g[k - 1].setZero();
                tp_g[k - 1].block(0, _n_step, 1, 1)(0, 0) = tp_k_g2;
                for (int i = 0; i < k; i++){
                  tp_g[k - 1].block(0, i, 1, 1)(0, 0) = tp_k_g2;
                }
                v_end_cost += tp2 * (pow(state[k - 1][3] - end_ref_vel(0), 2) + pow(state[k - 1][4] - end_ref_vel(1), 2) + pow(state[k - 1][5] - end_ref_vel(2), 2));
                v_end_cost_g.block(u_dim_ * _n_step, 0,  _n_step + 1, 1) +=  tp_g[k - 1].transpose() * (pow(state[k - 1][3] - end_ref_vel(0), 2) + pow(state[k - 1][4] - end_ref_vel(1), 2) + pow(state[k - 1][5] - end_ref_vel(2), 2));
                v_end_cost_g.block(0, 0, u_dim_ * _n_step, 1) += tp2 * (2 * (state[k - 1][3] - end_ref_vel(0)) * state_g[k - 1].block(3, 0, 1, u_dim_ * _n_step).transpose()
                                                                      + 2 * (state[k - 1][4] - end_ref_vel(1)) * state_g[k - 1].block(4, 0, 1, u_dim_ * _n_step).transpose()
                                                                      + 2 * (state[k - 1][5] - end_ref_vel(2)) * state_g[k - 1].block(5, 0, 1, u_dim_ * _n_step).transpose());
            }
        }
        if (k < _n_step) {
            t_sum += u[u_dim_ * _n_step + k];
            t_sum2 += u[u_dim_ * _n_step + k];
        }
    }


    //calculate error vector between the predicted state and the reference state
    Matrix<double, 3, 1> err[_n_step + 1];
    Matrix<double, 3, u_dim_ * _n_step + _n_step + 1> err_g[_n_step + 1];
    for (int k = 0; k < _n_step + 1; k++) {
        if (k > 0) {
            err[k] = state[k - 1].block(0, 0, 3, 1) - ref_pos[k];
            err_g[k].setZero();
            err_g[k].block(0, u_dim_ * _n_step, 3, _n_step + 1) = -ref_pos_g[k];
            err_g[k].block(0, 0, 3, u_dim_ * _n_step) = state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step);
        } else {
            err[k] = instance->init_state_.block(0, 0, 3, 1) - ref_pos[k];
            err_g[k].setZero();
            err_g[k].block(0, u_dim_ * _n_step, 3, _n_step + 1) = -ref_pos_g[k];
        }
    }

    // calculate lag error
    Matrix<double, 3, 1> lag_err[_n_step + 1];
    Matrix<double, 3, u_dim_ * _n_step + _n_step + 1> lag_err_g[_n_step + 1];
    double lag_e[_n_step + 1];
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> lag_e_g[_n_step + 1];
    for (int k = 0; k < _n_step + 1; k++) {
        lag_e[k] = err[k].dot(tangent[k]);
        lag_e_g[k] = (tangent[k].transpose() * err_g[k]).transpose();
        lag_e_g[k].block(u_dim_ * _n_step, 0, _n_step + 1, 1) += (err[k].transpose() * tangent_g[k]).transpose();
        lag_err[k] = lag_e[k] * tangent[k];
        lag_err_g[k] = tangent[k] * lag_e_g[k].transpose();
        lag_err_g[k].block(0, u_dim_ * _n_step, 3, _n_step + 1) += lag_e[k] * tangent_g[k];
        lag_e_g[k] = 2 * lag_e[k] * lag_e_g[k];
        lag_e[k] = pow(lag_e[k], 2);
    }
    
    //calculate contouring error
    Matrix<double, 3, 1> contour_err[_n_step + 1];
    Matrix<double, 3, u_dim_ * _n_step + _n_step + 1> contour_err_g[_n_step + 1];
    double contour_e[_n_step + 1];
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> contour_e_g[_n_step + 1];
    for (int k = 0; k < _n_step + 1; k++) {
        contour_err[k] = err[k] - lag_err[k];
        contour_err_g[k] = err_g[k] - lag_err_g[k];
        contour_e[k] = contour_err[k].squaredNorm();
        contour_e_g[k] = (2 * contour_err[k].transpose() * contour_err_g[k]).transpose();
    }

    //calculate the cost of tracking progress
    double t_cost = -u[u_dim_ * _n_step + _n_step];
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> t_cost_g;
    t_cost_g.setZero();
    t_cost_g[u_dim_ * _n_step + _n_step] = -1;
    for (int k = 0; k < _n_step; k++) {
        t_cost -= u[u_dim_ * _n_step + k];
        t_cost_g[u_dim_ * _n_step + k] = -1;
    }
    // t_cost -= u[u_dim_ * _n_step] * 0.5;
    // t_cost_g[u_dim_ * _n_step] = -1 * 0.5;

    //calculate the cost of violating distance constraints (set its weight to 0 when using CBF constraints)
    double c_cost = 0.0;
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> c_cost_g;
    c_cost_g.setZero();
    double dsafe = 0.45;  // 根据实际无人机的尺寸修改
    for (int k = 0; k < _n_step; k++) {
        auto dis = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k].block(0, 0, 3, 1));
        if (dis.first < dsafe) {
            double tmp = dis.first - dsafe;
            double tmp2 = pow(tmp, 2);
            c_cost += tmp2;
            c_cost_g.block(0, 0, u_dim_ * _n_step, 1) += (2 * tmp * dis.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)).transpose();
        }
    }
    // cout << endl;

    vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> oxyz_; 
    vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> osize_;
    oxyz_.clear();
    osize_.clear();
    int numDynamicOb  = 0;
    instance->updatePredObstacles(instance->predPos_, instance->predSize_, instance->intentProb_, oxyz_, osize_, numDynamicOb);


    //calculate the cost of violating CBF constraints
    double cbf_cost = 0.;
    double acbf_cost = -INFINITY;
    // double gamma = 0.7;
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> cbf_cost_g, acbf_cost_g;
    cbf_cost_g.setZero();
    acbf_cost_g.setZero();
    bool cbf_en = true;
    // if (!instance->flag) {
    for (int k = 0; k < _n_step; k++) {
#if 0
        {
            std::pair<double, Eigen::Vector3d> dis1;
            if (k == 0) {
                dis1 = instance->sdf_->get_dist_with_grad_trilinear<double>(instance->init_state_.block(0, 0, 3, 1));
            } else {
                dis1 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k - 1].block(0, 0, 3, 1));
            }
            auto dis2 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k].block(0, 0, 3, 1));
            double gamma = cbf_en ? 0.65 : 1.0;//0.2 * k / (_n_step - 1) + 0.8;
            double cbf = (1 - gamma) * (dis1.first - dsafe) - (dis2.first - dsafe);
            if (cbf > 0.0) {
                double tmp = cbf;//pow(cbf, 2);
                double scale = pow(0.9, k);
                cbf_cost += scale * tmp;
                if (k == 0) {
                    cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (dis2.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                } else {
                    cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) += scale * (((1 - gamma) * dis1.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)
                        - dis2.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step))).transpose();
                }
            }
        }
        {
            for (int i = 0; i < numDynamicOb; i++) {
                std::pair<double, Eigen::Vector3d> dis1, dis2;
                if (k == 0) {
                    instance->get_dis_ellipsoid(instance->init_state_.block(0, 0, 3, 1), oxyz_[k].row(i), osize_[k].row(i), &dis1.first, &dis1.second);
                } else {
                    instance->get_dis_ellipsoid(state[k - 1].block(0, 0, 3, 1), oxyz_[k].row(i), osize_[k].row(i), &dis1.first, &dis1.second);
                }
                instance->get_dis_ellipsoid(state[k].block(0, 0, 3, 1), oxyz_[k+1].row(i), osize_[k+1].row(i), &dis2.first, &dis2.second);
                double gamma = 0.65;//0.2 * k / (_n_step - 1) + 0.8;
                double cbf = (1 - gamma) * (dis1.first - dsafe) - (dis2.first - dsafe);
                if (cbf > 0.0) {
                    double tmp = cbf;//pow(cbf, 2);
                    double scale = pow(0.9, k);
                    cbf_cost += scale * tmp;
                    if (k == 0) {
                        cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (dis2.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                    } else {
                        cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) += scale * (((1 - gamma) * dis1.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)
                            - dis2.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step))).transpose();
                    }
                }
            }
        }
#else
        {
            double gamma1 = cbf_en ? 0.65 : 1.0;
            double gamma2 = cbf_en ? 0.9 : 1.0;
            double gamma3 = cbf_en ? 0.95 : 1.0;
            double scale = cbf_en ? pow(0.9, k) : 1.0;
            if (k == 0) {
                std::pair<double, Eigen::Vector3d> dis1 = instance->sdf_->get_dist_with_grad_trilinear<double>(instance->init_state_.block(0, 0, 3, 1));
                auto dis2 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k].block(0, 0, 3, 1));
                double cbf = (dis2.first - dsafe) + (gamma1 - 1) * (dis1.first - dsafe);
                if (cbf < 0.0) {
                    double tmp = -cbf;
                    cbf_cost += scale * tmp;
                    cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (dis2.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                }
            } else if (k == 1) {
                std::pair<double, Eigen::Vector3d> dis1 = instance->sdf_->get_dist_with_grad_trilinear<double>(instance->init_state_.block(0, 0, 3, 1));
                std::pair<double, Eigen::Vector3d> dis2 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k - 1].block(0, 0, 3, 1));
                auto dis3 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k].block(0, 0, 3, 1));
                double cbf = (dis3.first - dsafe) 
                            + (gamma1 + gamma2 - 2) * (dis2.first - dsafe) 
                            + (gamma1 - 1) * (gamma2 - 1) * (dis1.first - dsafe);
                if (cbf < 0.0) {
                    double tmp = -cbf;
                    cbf_cost += scale * tmp;
                    cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (dis3.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)
                                                                + (gamma1 + gamma2 - 2) * dis2.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                }
            } else if (k == 2) {
                std::pair<double, Eigen::Vector3d> dis1 = instance->sdf_->get_dist_with_grad_trilinear<double>(instance->init_state_.block(0, 0, 3, 1));
                std::pair<double, Eigen::Vector3d> dis2 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k - 2].block(0, 0, 3, 1));
                std::pair<double, Eigen::Vector3d> dis3 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k - 1].block(0, 0, 3, 1));
                auto dis4 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k].block(0, 0, 3, 1));
                double cbf = (dis4.first - dsafe) 
                            + (gamma1 + gamma2 + gamma3 - 3) * (dis3.first - dsafe) 
                            + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * (dis2.first - dsafe)
                            + (gamma1 - 1) * (gamma2 - 1) * (gamma3 - 1) * (dis1.first - dsafe);
                if (cbf < 0.0) {
                    double tmp = -cbf;
                    cbf_cost += scale * tmp;
                    cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (
                        dis4.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)
                        + (gamma1 + gamma2 + gamma3 - 3) * dis3.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)
                        + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * dis2.second.transpose() * state_g[k - 2].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                }
            } else {
                std::pair<double, Eigen::Vector3d> dis1 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k - 3].block(0, 0, 3, 1));
                std::pair<double, Eigen::Vector3d> dis2 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k - 2].block(0, 0, 3, 1));
                std::pair<double, Eigen::Vector3d> dis3 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k - 1].block(0, 0, 3, 1));
                auto dis4 = instance->sdf_->get_dist_with_grad_trilinear<double>(state[k].block(0, 0, 3, 1));
                double cbf = (dis4.first - dsafe) 
                            + (gamma1 + gamma2 + gamma3 - 3) * (dis3.first - dsafe) 
                            + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * (dis2.first - dsafe)
                            + (gamma1 - 1) * (gamma2 - 1) * (gamma3 - 1) * (dis1.first - dsafe);
                if (cbf < 0.0) {
                    double tmp = -cbf;
                    cbf_cost += scale * tmp;
                    cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (
                        dis4.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)
                        + (gamma1 + gamma2 + gamma3 - 3) * dis3.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)
                        + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * dis2.second.transpose() * state_g[k - 2].block(0, 0, 3, u_dim_ * _n_step)
                        + (gamma1 - 1) * (gamma2 - 1) * (gamma3 - 1) * dis1.second.transpose() * state_g[k - 3].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                }
            }
        }

        {
            for (int i = 0; i < numDynamicOb; i++) {
                double gamma1 = cbf_en ? 0.65 : 1.0;
                double gamma2 = cbf_en ? 0.9 : 1.0;
                double gamma3 = cbf_en ? 0.95 : 1.0;
                double scale = cbf_en ? pow(0.9, k) : 1.0;
                if (k == 0) {
                    std::pair<double, Eigen::Vector3d> dis1, dis2;
                    instance->get_dis_ellipsoid(instance->init_state_.block(0, 0, 3, 1), oxyz_[k].row(i), osize_[k].row(i), &dis1.first, &dis1.second);
                    instance->get_dis_ellipsoid(state[k].block(0, 0, 3, 1), oxyz_[k+1].row(i), osize_[k+1].row(i), &dis2.first, &dis2.second);
                    double cbf = (dis2.first - dsafe) + (gamma1 - 1) * (dis1.first - dsafe);
                    if (cbf < 0.0) {
                        double tmp = -cbf;
                        cbf_cost += scale * tmp;
                        cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (dis2.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                    }
                } else if (k == 1) {
                    std::pair<double, Eigen::Vector3d> dis1, dis2, dis3;
                    instance->get_dis_ellipsoid(instance->init_state_.block(0, 0, 3, 1), oxyz_[k-1].row(i), osize_[k-1].row(i), &dis1.first, &dis1.second);
                    instance->get_dis_ellipsoid(state[k - 1].block(0, 0, 3, 1), oxyz_[k].row(i), osize_[k].row(i), &dis2.first, &dis2.second);
                    instance->get_dis_ellipsoid(state[k].block(0, 0, 3, 1), oxyz_[k+1].row(i), osize_[k+1].row(i), &dis3.first, &dis3.second);
                    double cbf = (dis3.first - dsafe) 
                                + (gamma1 + gamma2 - 2) * (dis2.first - dsafe) 
                                + (gamma1 - 1) * (gamma2 - 1) * (dis1.first - dsafe);
                    if (cbf < 0.0) {
                        double tmp = -cbf;
                        cbf_cost += scale * tmp;
                        cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (dis3.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)
                                                                    + (gamma1 + gamma2 - 2) * dis2.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                    }
                } else if (k == 2) {
                    std::pair<double, Eigen::Vector3d> dis1, dis2, dis3, dis4;
                    instance->get_dis_ellipsoid(instance->init_state_.block(0, 0, 3, 1), oxyz_[k-2].row(i), osize_[k-2].row(i), &dis1.first, &dis1.second);
                    instance->get_dis_ellipsoid(state[k - 2].block(0, 0, 3, 1), oxyz_[k-1].row(i), osize_[k-1].row(i), &dis2.first, &dis2.second);
                    instance->get_dis_ellipsoid(state[k - 1].block(0, 0, 3, 1), oxyz_[k].row(i), osize_[k].row(i), &dis3.first, &dis3.second);
                    instance->get_dis_ellipsoid(state[k].block(0, 0, 3, 1), oxyz_[k+1].row(i), osize_[k+1].row(i), &dis4.first, &dis4.second);
                    double cbf = (dis4.first - dsafe) 
                                + (gamma1 + gamma2 + gamma3 - 3) * (dis3.first - dsafe) 
                                + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * (dis2.first - dsafe)
                                + (gamma1 - 1) * (gamma2 - 1) * (gamma3 - 1) * (dis1.first - dsafe);
                    if (cbf < 0.0) {
                        double tmp = -cbf;
                        cbf_cost += scale * tmp;
                        cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (
                            dis4.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)
                            + (gamma1 + gamma2 + gamma3 - 3) * dis3.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)
                            + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * dis2.second.transpose() * state_g[k - 2].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                    }
                } else {
                    std::pair<double, Eigen::Vector3d> dis1, dis2, dis3, dis4;
                    instance->get_dis_ellipsoid(state[k - 3].block(0, 0, 3, 1), oxyz_[k-2].row(i), osize_[k-2].row(i), &dis1.first, &dis1.second);
                    instance->get_dis_ellipsoid(state[k - 2].block(0, 0, 3, 1), oxyz_[k-1].row(i), osize_[k-1].row(i), &dis2.first, &dis2.second);
                    instance->get_dis_ellipsoid(state[k - 1].block(0, 0, 3, 1), oxyz_[k].row(i), osize_[k].row(i), &dis3.first, &dis3.second);
                    instance->get_dis_ellipsoid(state[k].block(0, 0, 3, 1), oxyz_[k+1].row(i), osize_[k+1].row(i), &dis4.first, &dis4.second);
                    double cbf = (dis4.first - dsafe) 
                                + (gamma1 + gamma2 + gamma3 - 3) * (dis3.first - dsafe) 
                                + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * (dis2.first - dsafe)
                                + (gamma1 - 1) * (gamma2 - 1) * (gamma3 - 1) * (dis1.first - dsafe);
                    if (cbf < 0.0) {
                        double tmp = -cbf;
                        cbf_cost += scale * tmp;
                        cbf_cost_g.block(0, 0, u_dim_ * _n_step, 1) -= scale * (
                            dis4.second.transpose() * state_g[k].block(0, 0, 3, u_dim_ * _n_step)
                            + (gamma1 + gamma2 + gamma3 - 3) * dis3.second.transpose() * state_g[k - 1].block(0, 0, 3, u_dim_ * _n_step)
                            + ((gamma1 - 1) * (gamma2 + gamma3 - 2) + (gamma2 - 1) * (gamma3 - 1)) * dis2.second.transpose() * state_g[k - 2].block(0, 0, 3, u_dim_ * _n_step)
                            + (gamma1 - 1) * (gamma2 - 1) * (gamma3 - 1) * dis1.second.transpose() * state_g[k - 3].block(0, 0, 3, u_dim_ * _n_step)).transpose();
                    }
                }
            }
        }
#endif
    }
    instance->cbf_cost_ = cbf_cost;

    //calculate heading angle cost
    double yawcost = 0.0;
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> yawcost_g;
    yawcost_g.setZero();
    for (int k = 0; k < _n_step; k++) {
        double &vx = state[k][3];
        double &vy = state[k][4];
        double &qw = state[k][6];
        double &qx = state[k][7];
        double &qy = state[k][8];
        double &qz = state[k][9];
        double tmpx = 1. - 2. * (qy * qy + qz * qz);
        double tmpy = 2. * (qw * qz + qx * qy);
        double tmpnorm = pow(tmpx, 2) + pow(tmpy, 2);
        double yaw = std::atan2(tmpy, tmpx);
        Vector2d yaw_g_tmp(-tmpy / tmpnorm, tmpx / tmpnorm);
        Vector4d yaw_g;
        yaw_g.setZero();
        yaw_g(0) = yaw_g_tmp.y() * 2 * qz;
        yaw_g(1) = yaw_g_tmp.y() * 2 * qy;
        yaw_g(2) = yaw_g_tmp.x() * (-4 * qy) + yaw_g_tmp.y() * 2 * qx;
        yaw_g(3) = yaw_g_tmp.x() * (-4 * qz) + yaw_g_tmp.y() * 2 * qw;
        if (pow(vx, 2) + pow(vy, 2) > 1e-6) {
            double vang = std::atan2(vy, vx);
            double vxynorm = pow(vx, 2) + pow(vy, 2);
            Vector2d vang_g(-vy / vxynorm, vx / vxynorm);
            if (yaw - vang > M_PI) {
                yawcost += 2 * M_PI - (yaw - vang);
                yawcost_g.block(0, 0, u_dim_ * _n_step, 1) += 
                    (-yaw_g.transpose() * state_g[k].block(6, 0, 4, u_dim_ * _n_step)).transpose()
                    + (vang_g.transpose() * state_g[k].block(3, 0, 2, u_dim_ * _n_step)).transpose();
            } else if (vang - yaw > M_PI) {
                yawcost += 2 * M_PI + (yaw - vang);
                yawcost_g.block(0, 0, u_dim_ * _n_step, 1) += 
                    (yaw_g.transpose() * state_g[k].block(6, 0, 4, u_dim_ * _n_step)).transpose()
                    + (-vang_g.transpose() * state_g[k].block(3, 0, 2, u_dim_ * _n_step)).transpose();
            } else if (yaw > vang) {
                yawcost += yaw - vang;
                yawcost_g.block(0, 0, u_dim_ * _n_step, 1) += 
                    (yaw_g.transpose() * state_g[k].block(6, 0, 4, u_dim_ * _n_step)).transpose()
                    + (-vang_g.transpose() * state_g[k].block(3, 0, 2, u_dim_ * _n_step)).transpose();
            } else {
                yawcost += -yaw + vang;
                yawcost_g.block(0, 0, u_dim_ * _n_step, 1) += 
                    (-yaw_g.transpose() * state_g[k].block(6, 0, 4, u_dim_ * _n_step)).transpose()
                    + (vang_g.transpose() * state_g[k].block(3, 0, 2, u_dim_ * _n_step)).transpose();
            }
        }
    }

    //calculate the cost of violating velocity constraints
    double vcost = 0.0;
    Matrix<double, u_dim_ * _n_step + _n_step + 1, 1> vcost_g;
    vcost_g.setZero();
    for (int k = 0; k < _n_step; k++) {
        double tmp = pow(state[k][3], 2) + pow(state[k][4], 2) + pow(state[k][5], 2) - pow(10, 2);
        if (tmp > 0) {
            vcost += tmp;
            vcost_g.block(0, 0, u_dim_ * _n_step, 1) += 2 * state[k][3] * state_g[k].block(3, 0, 1, u_dim_ * _n_step).transpose()
                                                    + 2 * state[k][4] * state_g[k].block(4, 0, 1, u_dim_ * _n_step).transpose()
                                                    + 2 * state[k][5] * state_g[k].block(5, 0, 1, u_dim_ * _n_step).transpose();
        }
    }

    //calculate the cost of control input
    double ucost = 0.0;
    for (int k = 0; k < _n_step; k++) {
        int of = k * u_dim_;
        Matrix<double, u_dim_, 1> past_u;
        if (k == 0) {
            past_u = instance->last_u_;
        } else {
            past_u = Matrix<double, u_dim_, 1>(
                u[of - 4], u[of - 3], u[of - 2], u[of - 1]
            );
        }
        ucost += 
            // + cost_w[3] * pow(u[of + 0] - ref_u(k, 0), 2)
            // + cost_w[4] * pow(u[of + 1] - ref_u(k, 1), 2)
            + cost_w[6] * pow(u[of + 2] - ref_u(k, 2), 2)
            // + cost_w[6] * pow(u[of + 3] - ref_u(k, 3), 2)
            + cost_w[3] * pow(acc[k][0], 2)
            + cost_w[4] * pow(acc[k][1], 2)
            + cost_w[5] * pow(acc[k][2], 2)
            + cost_w[8] * pow(u[of + 0] - past_u(0), 2)
            + cost_w[9] * pow(u[of + 1] - past_u(1), 2)
            + cost_w[10] * pow(u[of + 2] - past_u(2), 2)
#if USE_EXTENDED_DYNAMICS
            + cost_w[11] * pow(u[of + 3], 2);
#else
            + cost_w[11] * pow(u[of + 3] - past_u(3), 2);
#endif
        // g[of + 0] += 2 * cost_w[3] * (u[of + 0] - ref_u(k, 0));
        // g[of + 1] += 2 * cost_w[4] * (u[of + 1] - ref_u(k, 1));
        g[of + 2] += 2 * cost_w[6] * (u[of + 2] - ref_u(k, 2));
        // g[of + 3] += 2 * cost_w[6] * (u[of + 3] - ref_u(k, 3));
        g.block(0, 0, u_dim_ * _n_step, 1) += 2 * cost_w[3] * acc[k][0] * acc_g[k].row(0).transpose();
        g.block(0, 0, u_dim_ * _n_step, 1) += 2 * cost_w[4] * acc[k][1] * acc_g[k].row(1).transpose();
        g.block(0, 0, u_dim_ * _n_step, 1) += 2 * cost_w[5] * acc[k][2] * acc_g[k].row(2).transpose();
        g[of + 0] += 2 * cost_w[8] * (u[of + 0] - past_u(0));
        g[of + 1] += 2 * cost_w[9] * (u[of + 1] - past_u(1));
        g[of + 2] += 2 * cost_w[10] * (u[of + 2] - past_u(2));
#if USE_EXTENDED_DYNAMICS
        g[of + 3] += 2 * cost_w[11] * u[of + 3];
#else
        g[of + 3] += 2 * cost_w[11] * (u[of + 3] - past_u(3));
#endif
        if (k != 0) {
            g[of - 4] -= 2 * cost_w[8] * (u[of + 0] - past_u(0));
            g[of - 3] -= 2 * cost_w[9] * (u[of + 1] - past_u(1));
            g[of - 2] -= 2 * cost_w[10] * (u[of + 2] - past_u(2));
#if !USE_EXTENDED_DYNAMICS
            g[of - 1] -= 2 * cost_w[11] * (u[of + 3] - past_u(3));
#endif
        }
    }

    // sum the above costs
    double cost = cost_w[12] * cbf_cost   + cost_w[7] * c_cost   + cost_w[2] * t_cost   + 0.15 * yawcost   + 1.0 * vcost   + cost_w[13] * v_end_cost + ucost;
             g += cost_w[12] * cbf_cost_g + cost_w[7] * c_cost_g + cost_w[2] * t_cost_g + 0.15 * yawcost_g + 1.0 * vcost_g + cost_w[13] * v_end_cost_g;  
    double lag_cost = 0.0;
    double contour_cost = 0.0;
    for (int k = 0; k < _n_step + 1; k++) {
        lag_cost += lag_e[k];
        contour_cost += contour_e[k];
        if (k == _n_step || k == 0) {
            cost += 2 * /*exp(-cbf_cost * 1e5) * */contour_e[k] + 0.5 * contour_e[k];
            g += 2 * (/*exp(-cbf_cost * 1e5) * */contour_e_g[k]/* - 1e5 * exp(-cbf_cost * 1e5) * contour_e[k] * cbf_cost_g*/) + 0.5 * contour_e_g[k];
            cost += cost_w[1] * lag_e[k];
            g += cost_w[1] * lag_e_g[k];
        } else {
            cost += cost_w[0] * contour_e[k];
            g += cost_w[0] * contour_e_g[k];
            cost += cost_w[1]* lag_e[k];
            g += cost_w[1] * lag_e_g[k];
        }
    }

    for (int i = 0; i < grad.size(); i++) {
        grad[i] = g[i];
    }

    instance->tmp_u_ = u;

    return cost;
}

void NominalMpcc::set_w(Matrix<double, 3 + u_dim_ + 1 + u_dim_ + 1 + 1 + 1, 1> &w) {
    cost_w = w;
}

int NominalMpcc::solve(const Matrix<double, x_dim_, 1> &state,
    const SdfMap &sdf,
    const MatrixXd &ctrl_pts,
    const MatrixXd &v_ctrl_pts,
    const MatrixXd &a_ctrl_pts,
    const vector<vector<vector<Eigen::Vector3d>>>& predPos,
    const vector<vector<vector<Eigen::Vector3d>>>& predSize,
    const vector<Eigen::VectorXd>& intentProb,
    const double ts,
    const double len,
    const Matrix<double, u_dim_, 1> last_u,
    const Vector3d disturbance_acc,
    Matrix<double, _n_step, u_dim_> &u, 
    Matrix<double, _n_step, x_dim_> &x_predict,
    Matrix<double, _n_step + 1, 1> &t_index,
    double &solve_time) {
    const double t_min = ts * 3;
    const double t_max = ctrl_pts.rows() * ts;
    vector<double> uv(u_dim_ * _n_step + _n_step + 1);
    //initial solution
    for (int k = 0; k < _n_step; k++) {
        uv[k * u_dim_ + 0] = u(k, 0);
        uv[k * u_dim_ + 1] = u(k, 1);
        uv[k * u_dim_ + 2] = u(k, 2);
        uv[k * u_dim_ + 3] = u(k, 3);
    }
    for (int k = 0; k < _n_step; k++) {
        uv[_n_step * u_dim_ + k] = t_index[k + 1] - t_index[k];
    }
    uv[_n_step * u_dim_ + _n_step] = t_index[0];

    // boundaries on optimization variables
    vector<double> lb(uv.size()), ub(uv.size());
    for (int k = 0; k < _n_step; k++) {
        lb[k * u_dim_ + 0] = -M_PI * 1.8;
        lb[k * u_dim_ + 1] = -M_PI * 1.8;
        lb[k * u_dim_ + 2] = -M_PI * 1.8;
#if USE_EXTENDED_DYNAMICS
        lb[k * u_dim_ + 3] = -10.0;
#else
        lb[k * u_dim_ + 3] = 0.0;
#endif
        ub[k * u_dim_ + 0] = M_PI * 1.8;
        ub[k * u_dim_ + 1] = M_PI * 1.8;
        ub[k * u_dim_ + 2] = M_PI * 1.8;
#if USE_EXTENDED_DYNAMICS
        ub[k * u_dim_ + 3] = 10.0;
#else
        ub[k * u_dim_ + 3] = 0.8;
#endif
    }
    for (int k = 0; k < _n_step; k++) {
        lb[_n_step * u_dim_ + k] = 0.002 * 0.1 * (t_max - t_min) / len;
        ub[_n_step * u_dim_ + k] = 40.0 * 0.1 * (t_max - t_min) / len;
    }
    // 设置控制起始时间的最值
    lb[_n_step * u_dim_ + _n_step] = t_index[0] + 1e-3 > t_max ? t_max : (t_index[0] + 1e-3);
    ub[_n_step * u_dim_ + _n_step] = t_max;  
    opt_.set_lower_bounds(lb);
    opt_.set_upper_bounds(ub);

    for (int i = 0; i < uv.size(); i++){
        if (uv[i] > ub[i]) {
            uv[i] = ub[i];
        } else if (uv[i] < lb[i]) {
            uv[i] = lb[i];
        }
    }
    
    init_state_ = state;
    sdf_ = &sdf;
    ctrl_pts_ = ctrl_pts;
    v_ctrl_pts_ = v_ctrl_pts;
    a_ctrl_pts_ = a_ctrl_pts;
    predPos_= predPos;
    predSize_ = predSize;
    intentProb_ = intentProb;
    ts_ = ts;
    t_max_ = t_max;
    last_u_ = last_u;
    disturbance_acc_ = disturbance_acc;
    for (int k = 0; k < _n_step; k++) {
        ref_u_.row(k) << 0, 0, 0, this->hover_ratio_;
    }

    double minf;
    auto beforeTime = chrono::steady_clock::now();
    try{
        flag = false;
        cnt = 0;
        //solve optimization problem
        opt_.optimize(uv, minf);
        auto afterTime = chrono::steady_clock::now();
        solve_time = chrono::duration<double>(afterTime - beforeTime).count();

        //get control inputs from optimization variables
        for (int k = 0; k < _n_step; k++) {
            u(k, 0) = uv[k * u_dim_ + 0];
            u(k, 1) = uv[k * u_dim_ + 1];
            u(k, 2) = uv[k * u_dim_ + 2];
            u(k, 3) = uv[k * u_dim_ + 3];
        }
        for (int k = 0; k < _n_step; k++) {
            x_predict.row(k) = state_[k];
        }
        t_index[0] = uv[u_dim_ * _n_step + _n_step]; // 控制开始时间
        for (int k = 0; k < _n_step; k++) {
            t_index[k + 1] = t_index[k] + uv[u_dim_ * _n_step + k];
        }

        return EXIT_SUCCESS;
    } catch(exception &e) {
        auto afterTime = chrono::steady_clock::now();
        solve_time = chrono::duration<double>(afterTime - beforeTime).count();
        cerr << "nlopt failed: " << e.what() << endl;

        for (int k = 0; k < _n_step; k++) {
            u(k, 0) = uv[k * u_dim_ + 0];
            u(k, 1) = uv[k * u_dim_ + 1];
            u(k, 2) = uv[k * u_dim_ + 2];
            u(k, 3) = uv[k * u_dim_ + 3];
        }
        for (int k = 0; k < _n_step; k++) {
            x_predict.row(k) = state_[k];
        }
        t_index[0] = uv[u_dim_ * _n_step + _n_step];
        for (int k = 0; k < _n_step; k++) {
            t_index[k + 1] = t_index[k] + uv[u_dim_ * _n_step + k];
        }

        // uv = tmp_u_;
        // vector<double> grad(uv.size());
        // VectorXd grad2(uv.size());
        // flag = false;
        // double cost = cost_func(uv, grad, this);
        // double vub[_n_step * 3];
        // double vub_g[_n_step * 3 * (_n_step * u_dim_ + _n_step + 1)];
        // double vub_g2[_n_step * 3 * (_n_step * u_dim_ + _n_step + 1)];
        // flag = false;
        // for (int i = 0; i < uv.size(); i++) {
        //     double delta = 1e-8;
        //     vector<double> uv_new = uv;
        //     uv_new[i] += delta;
        //     vector<double> grad(uv.size());
        //     double cost_new = cost_func(uv_new, grad, this);
        //     double vub_new[_n_step * 3];
        //     double vub_g[_n_step * 3 * (_n_step * u_dim_ + _n_step + 1)];
        //     for (int j = 0; j < _n_step * 3; j++) {
        //         vub_g2[j * uv.size() + i] = (vub_new[j] - vub[j]) / delta;
        //     }
        //     grad2[i] = (cost_new - cost) / delta;
        // }
        
        // cout << "grad: " << fixed << setprecision(8) << endl << vector2Vector(grad).transpose() << endl;
        // cout << "grad2: " << fixed << setprecision(8) << endl << grad2.transpose() << endl;

        return EXIT_FAILURE;
    }
}


// predPos / predSize
// │
// ├── [障碍物0] 
// │   ├── [意图0: 前进] → [位置/尺寸 t0, t1, t2, ...]
// │   ├── [意图1: 左转] → [位置/尺寸 t0, t1, t2, ...]
// │   ├── [意图2: 右转] → [位置/尺寸 t0, t1, t2, ...]
// │   └── [意图3: 停止] → [位置/尺寸 t0, t1, t2, ...]
// │
// ├── [障碍物1]
// │   ├── [意图0] → ...
// │   └── ...
// └── ...
// this->posPred_存储的是全局坐标，posPred_[i][j][k](2)是中心点的z轴坐标，为z轴高度的一半
// predSize[i][j][k](0) - X轴方向的长度/宽度
// predSize[i][j][k](1) - Y轴方向的长度/宽度
// predSize[i][j][k](2) - Z轴方向的高度
// 尺寸始终保持轴对齐,即不考虑旋转,
// intentProb
// │
// ├── [障碍物0] → [前进概率, 左转概率, 右转概率, 停止概率]
// ├── [障碍物1] → [前进概率, 左转概率, 右转概率, 停止概率]
// 概率值按固定索引顺序存储，不是按概率值从大到小排序，顺序始终是: [前进, 左转, 右转, 停止]
// 时间步长 = 预测步数 + 1 (numPred_ + 1)， 索引 k 对应时间步长 (k=0 是当前时刻)
// k=0 总是表示当前时刻，k=1，表示第一个预测时间步 (dt_ 秒后)，k=numPred_ 表示最后一个预测时间步
//    安全距离扩展（dynamicSafetyDist_ / 2）
//  ┌───────────────────┐
//  │  ┌─────────────┐  │
//  │  │  障碍物原始   │  │
//  │  │    尺寸      │  │  → osize_[k].row(i) 
//  │  └─────────────┘  │    包含安全距离的半轴长
//  └───────────────────┘
//        ↑
//     oxyz_[k].row(i)
//    (障碍物中心坐标)
void NominalMpcc::updatePredObstacles(const vector<vector<vector<Eigen::Vector3d>>>& predPos, 
                                      const vector<vector<vector<Eigen::Vector3d>>>& predSize, 
                                      const vector<Eigen::VectorXd>& intentProb,
                                      vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &oxyz, 
                                      vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &osize, 
                                      int &numDynamicOb){
    // 更新预测障碍物信息 
    if (predPos.size()) {
        // 如果预测位置不为空
        vector<vector<Eigen::Vector3d>> dynamicObstaclesPosTemp; // 临时存储动态障碍物位置 dynamicObstaclesPosTemp[障碍物索引][时间步索引]
        vector<vector<Eigen::Vector3d>> dynamicObstaclesSizeTemp; // 临时存储动态障碍物尺寸 dynamicObstaclesSizeTemp[障碍物索引][时间步索引]
        vector<vector<vector<Eigen::Vector3d>>> obPredPos_;
        vector<vector<vector<Eigen::Vector3d>>> obPredSize_;
        vector<Eigen::VectorXd> obIntentProb_;
        dynamicObstaclesPosTemp.clear(); // 清空临时位置容器
        dynamicObstaclesSizeTemp.clear(); // 清空临时尺寸容器
        obPredPos_.clear(); // 清空预测位置容器
        obPredSize_.clear(); // 清空预测尺寸容器
        obIntentProb_.clear(); // 清空意图概率容器

        dynamicObstaclesPosTemp.resize(predPos.size()); // 根据预测障碍物数量调整容器大小
        dynamicObstaclesSizeTemp.resize(predPos.size());
        for (int i = 0; i < int(predPos.size()); i++) {
            // 遍历每个预测障碍物
            Eigen::Vector3d pos = predPos[i][0][0]; // 获取动态障碍物此刻的位置  k=0 总是表示当前时刻 无论选择哪种意图类型（因为所有意图都是从同一当前位置开始预测的），predPos[i][0][0] 和 predSize[i][0][0] 确实表示障碍物在当前时刻的位置和尺寸
            Eigen::Vector3d size = predSize[i][0][0]; // 获取动态障碍物此刻的尺寸
            for (int j = 0; j < _n_step; j++) {
                // 将动态障碍物此刻的位置和尺寸信息复制到每个时间步
                dynamicObstaclesPosTemp[i].push_back(pos);
                dynamicObstaclesSizeTemp[i].push_back(size);
            }
        }
        obPredPos_ = predPos;
        obPredSize_ = predSize;
        obIntentProb_ = intentProb;

        vector<vector<Eigen::Vector3d>> intentCombPos;
        vector<vector<Eigen::Vector3d>> intentCombSize;
        intentCombPos.clear(); 
        intentCombSize.clear(); 
        intentCombPos.resize(obPredPos_.size());
        intentCombSize.resize(obPredSize_.size());
        // 动态障碍物最大意图下的预测位置和尺寸
        for (int j = 0; j < int(obPredPos_.size()); j++) {
            int maxIntent;
            double maxProb = obIntentProb_[j].maxCoeff(&maxIntent); // 获取第j个动态障碍物的最大意图索引maxIntent及其概率maxProb
            intentCombPos[j] = obPredPos_[j][maxIntent];// 存储第j个动态障碍物最大意图的预测位置 intentCombPos[障碍物索引][时间步索引] -> 存储最大意图的预测位置
            intentCombSize[j] = obPredSize_[j][maxIntent];// 存储第j个动态障碍物最大意图的预测尺寸 
        }
        numDynamicOb = intentCombPos.size(); // 动态障碍物的数量
        oxyz.clear(); 
        osize.clear(); 
        oxyz.resize(_n_step + 1);
        osize.resize(_n_step + 1);

        for (int j = 0; j < _n_step + 1; j++) {
            oxyz[j].resize(numDynamicOb, 3);
            osize[j].resize(numDynamicOb, 3); 

            // 遍历动态障碍物
            for (int i = 0; i < numDynamicOb; i++) {
                if (j < intentCombPos[i].size()) {
                    // 如果当前预测步在动态障碍物的预测范围内
                    oxyz[j](i, 0) = intentCombPos[i][j](0); // 存储MPC预测第j步时第i个动态障碍物的膨胀后的x方向位置
                    oxyz[j](i, 1) = intentCombPos[i][j](1); 
                    oxyz[j](i, 2) = intentCombPos[i][j](2); 
                    osize[j](i, 0) = intentCombSize[i][j](0) / 2 + this->dynamicSafetyDist_; // 存储MPC预测第j步时第i个动态障碍物的膨胀后的x方向尺寸
                    osize[j](i, 1) = intentCombSize[i][j](1) / 2 + this->dynamicSafetyDist_; 
                    osize[j](i, 2) = intentCombSize[i][j](2) / 2 + this->dynamicSafetyDist_; 
                } else {
                    // 如果当前预测步超出动态障碍物的预测范围，使用最后一个预测步的数据
                    oxyz[j](i, 0) = intentCombPos[i].back()(0); 
                    oxyz[j](i, 1) = intentCombPos[i].back()(1); 
                    oxyz[j](i, 2) = intentCombPos[i].back()(2); 
                    osize[j](i, 0) = intentCombSize[i].back()(0) / 2 + this->dynamicSafetyDist_; 
                    osize[j](i, 1) = intentCombSize[i].back()(1) / 2 + this->dynamicSafetyDist_; 
                    osize[j](i, 2) = intentCombSize[i].back()(2) / 2 + this->dynamicSafetyDist_; 
                }
            }
        }
    } else {
        // 如果预测位置为空，清空相关变量
        oxyz.clear();
        osize.clear();
        oxyz.resize(_n_step + 1);
        osize.resize(_n_step + 1);
        numDynamicOb = 0;
    }

}

void NominalMpcc::get_dis_ellipsoid(const Vector3d pos, const Vector3d pos_obs, const Vector3d size_obs, double *dis, Vector3d *g){
    Vector3d m = pos - pos_obs; // 忽略旋转
    Vector3d mx(m.x(), 0, 0);
    Vector3d my(0, m.y(), 0);
    Vector3d mz(0, 0, m.z());
    double cosx = mx.squaredNorm() / m.squaredNorm();
    double cosy = my.squaredNorm() / m.squaredNorm();
    double cosz = mz.squaredNorm() / m.squaredNorm();
    double l2 = 1 / (cosx / pow(size_obs.x() / 2, 2) + cosy / pow(size_obs.y() / 2, 2) + cosz / pow(size_obs.z() / 2, 2));
    double l = sqrt(l2);
    double d = (pos - pos_obs).norm();
    *dis = d - l;
    if (g) {
        double dis1, dis2, dis3;
        double del = 1e-4;
        get_dis_ellipsoid(pos + Vector3d(del, 0, 0), pos_obs, size_obs, &dis1);
        get_dis_ellipsoid(pos + Vector3d(0, del, 0), pos_obs, size_obs, &dis2);
        get_dis_ellipsoid(pos + Vector3d(0, 0, del), pos_obs, size_obs, &dis3);
        *g = Vector3d((dis1 - *dis) / del, (dis2 - *dis) / del, (dis3 - *dis) / del);
        // cout << g->transpose() << endl;
    }
}

void NominalMpcc::get_dis_ellipsoid2(const Vector3d pos, const Vector3d pos_obs, const Vector3d size_obs, double *dis, Vector3d *g){
    Vector3d m = pos - pos_obs; // 忽略旋转
    *dis = pow(m.x(), 2) / pow(size_obs.x() / 2 + 0.4, 2)
            + pow(m.y(), 2) / pow(size_obs.y() / 2 + 0.4, 2)
            + pow(m.z(), 2) / pow(size_obs.z() / 2 + 0.4, 2)
            - 1 + 0.4;
    if (g) {
        double dis1, dis2, dis3;
        double del = 1e-10;
        get_dis_ellipsoid2(pos + Vector3d(del, 0, 0), pos_obs, size_obs, &dis1);
        get_dis_ellipsoid2(pos + Vector3d(0, del, 0), pos_obs, size_obs, &dis2);
        get_dis_ellipsoid2(pos + Vector3d(0, 0, del), pos_obs, size_obs, &dis3);
        *g = Vector3d((dis1 - *dis) / del, (dis2 - *dis) / del, (dis3 - *dis) / del);
        // cout << g->transpose() << endl;
    }
}


// void NominalMpcc::updatePredObstacles(const vector<vector<vector<Eigen::Vector3d>>>& predPos, const vector<vector<vector<Eigen::Vector3d>>>& predSize, const vector<Eigen::VectorXd>& intentProb) {
//     // 更新预测障碍物信息
//     if (predPos.size()) {
//         // 如果预测位置不为空
//         vector<vector<Eigen::Vector3d>> dynamicObstaclesPosTemp; // 临时存储动态障碍物位置 dynamicObstaclesPosTemp[障碍物索引][时间步索引]
//         vector<vector<Eigen::Vector3d>> dynamicObstaclesSizeTemp; // 临时存储动态障碍物尺寸 dynamicObstaclesSizeTemp[障碍物索引][时间步索引]
//         dynamicObstaclesPosTemp.clear(); // 清空临时位置容器
//         dynamicObstaclesSizeTemp.clear(); // 清空临时尺寸容器
//         dynamicObstaclesPosTemp.resize(predPos.size()); // 根据预测障碍物数量调整容器大小
//         dynamicObstaclesSizeTemp.resize(predPos.size());
//         for (int i = 0; i < int(predPos.size()); i++) {
//             // 遍历每个预测障碍物
//             Eigen::Vector3d pos = predPos[i][0][0]; // 获取动态障碍物此刻的位置  k=0 总是表示当前时刻 无论选择哪种意图类型（因为所有意图都是从同一当前位置开始预测的），predPos[i][0][0] 和 predSize[i][0][0] 确实表示障碍物在当前时刻的位置和尺寸
//             Eigen::Vector3d size = predSize[i][0][0]; // 获取动态障碍物此刻的尺寸
//             for (int j = 0; j < _n_step; j++) {
//                 // 将动态障碍物此刻的位置和尺寸信息复制到每个时间步
//                 dynamicObstaclesPosTemp[i].push_back(pos);
//                 dynamicObstaclesSizeTemp[i].push_back(size);
//             }
//         }
//         // 这两个变量用于存储动态障碍物当前时刻的位置和尺寸
//         this->dynamicObstaclesPos_ = dynamicObstaclesPosTemp;
//         this->dynamicObstaclesSize_ = dynamicObstaclesSizeTemp;
//         // 这三个成员变量用于获取最大意图下的预测位置和尺寸
//         this->obPredPos_ = predPos;
//         this->obPredSize_ = predSize;
//         this->obIntentProb_ = intentProb;
//     } else {
//         // 如果预测位置为空，清空相关成员变量
//         this->dynamicObstaclesPos_.clear();
//         this->dynamicObstaclesSize_.clear();
//         this->obPredPos_.clear();
//         this->obPredSize_.clear();
//         this->obIntentProb_.clear();
//     }
// }

// // 关键函数
// void NominalMpcc::getIntentComb(vector<vector<Eigen::Vector3d>> &intentCombPos, vector<vector<Eigen::Vector3d>> &intentCombSize){
//     // updatePredObstacles函数负责获取所有的动态障碍物位置尺寸和意图，getIntentComb负责获取动态障碍物最大意图下的预测位置和尺寸。
//     intentCombPos.clear(); 
//     intentCombSize.clear(); 
//     intentCombPos.resize(this->obPredPos_.size());
//     intentCombSize.resize(this->obPredPos_.size());
//     // 动态障碍物最大意图下的预测位置和尺寸
//     for (int j = 0; j < int(this->obPredPos_.size()); j++) {
//         int maxIntent;
//         double maxProb = this->obIntentProb_[j].maxCoeff(&maxIntent); // 获取第j个动态障碍物的最大意图索引maxIntent及其概率maxProb
//         intentCombPos.push_back(this->obPredPos_[j][maxIntent]); // 获取所有动态障碍物最大意图的预测位置
//         intentCombSize.push_back(this->obPredSize_[j][maxIntent]); // 获取所有动态障碍物最大意图的预测尺寸
//     }

// }

// void NominalMpcc::updateObstacleParam_(const vector<vector<Eigen::Vector3d>> &dynamicObstaclesPos, 
//                                     const vector<vector<Eigen::Vector3d>> &dynamicObstaclesSize, 
//                                     vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &oxyz, 
//                                     vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &osize,
//                                     int &numDynamicOb) {
//     numDynamicOb = dynamicObstaclesPos.size(); // 动态障碍物的数量
//     oxyz.resize(_n_step); // 根据MPC窗口大小调整障碍物位置数组的大小   障碍物的位置矩阵，每个时间步包含所有障碍物的三维位置（x, y, z）
//     osize.resize(_n_step); // 根据MPC窗口大小调整障碍物尺寸数组的大小  障碍物的尺寸矩阵，每个时间步包含所有障碍物的三维尺寸（x, y, z）

//     for (int j = 0; j < _n_step; j++) {
//         oxyz[j].resize(numDynamicOb, 3); // 初始化每个时间步的障碍物位置矩阵，大小为障碍物数量×3
//         osize[j].resize(numDynamicOb, 3); // 初始化每个时间步的障碍物尺寸矩阵，大小为障碍物数量×3

//         // 遍历动态障碍物
//         for (int i = 0; i < numDynamicOb; i++) {
//             if (j < dynamicObstaclesPos[i].size()) {
//                 // 如果当前时间步在动态障碍物的预测范围内
//                 oxyz[j](i, 0) = dynamicObstaclesPos[i][j](0); // 存储MPC预测第j步时所有动态障碍物的x坐标
//                 oxyz[j](i, 1) = dynamicObstaclesPos[i][j](1); // 存储MPC预测第j步时所有动态障碍物的y坐标
//                 oxyz[j](i, 2) = dynamicObstaclesPos[i][j](2); // 存储MPC预测第j步时所有动态障碍物的z坐标
//                 osize[j](i, 0) = dynamicObstaclesSize[i][j](0) / 2 + this->dynamicSafetyDist_; // 存储MPC预测第j步时所有动态障碍物的x方向尺寸
//                 osize[j](i, 1) = dynamicObstaclesSize[i][j](1) / 2 + this->dynamicSafetyDist_; // 存储MPC预测第j步时所有动态障碍物的y方向尺寸
//                 osize[j](i, 2) = dynamicObstaclesSize[i][j](2) / 2 + this->dynamicSafetyDist_; // 存储MPC预测第j步时所有动态障碍物的z方向尺寸
//             } else {
//                 // 如果当前时间步超出动态障碍物的预测范围，使用最后一个时间步的数据
//                 oxyz[j](i, 0) = dynamicObstaclesPos[i].back()(0); // 存储MPC预测第j步时所有动态障碍物的x坐标
//                 oxyz[j](i, 1) = dynamicObstaclesPos[i].back()(1); // 存储MPC预测第j步时所有动态障碍物的y坐标
//                 oxyz[j](i, 2) = dynamicObstaclesPos[i].back()(2); // 存储MPC预测第j步时所有动态障碍物的z坐标
//                 osize[j](i, 0) = dynamicObstaclesSize[i].back()(0) / 2 + this->dynamicSafetyDist_; // 存储MPC预测第j步时所有动态障碍物的x方向尺寸
//                 osize[j](i, 1) = dynamicObstaclesSize[i].back()(1) / 2 + this->dynamicSafetyDist_; // 存储MPC预测第j步时所有动态障碍物的y方向尺寸
//                 osize[j](i, 2) = dynamicObstaclesSize[i].back()(2) / 2 + this->dynamicSafetyDist_; // 存储MPC预测第j步时所有动态障碍物的z方向尺寸
//             }
//         }
//     }
// }