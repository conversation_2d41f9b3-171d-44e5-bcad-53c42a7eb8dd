#ifndef _NOMINAL_MPCC_HPP
#define _NOMINAL_MPCC_HPP

#define USE_EXTENDED_DYNAMICS 0

#include <iostream>
#include <vector>
#include <string>
#include <cmath>
#include <iomanip>
#include <nlopt.hpp>
#include <Eigen/Dense>
#include <chrono>

#if USE_EXTENDED_DYNAMICS
#include "quadrotor_dynamics/extended_quad_dynamic.hpp"
#else
#include "quadrotor_dynamics/nominal_quad_dynamic.hpp"
#endif
#include "bspline/uniform_bspline.hpp"
#include "map/sdf.hpp"
#include "common/rotation_math.hpp"

using namespace std;
using namespace Eigen;

#if USE_EXTENDED_DYNAMICS
class NominalMpcc : protected ExtendedQuadDynamic {
#else
class NominalMpcc : protected NominalQuadDynamic {
#endif
public:
#if USE_EXTENDED_DYNAMICS
    static constexpr int x_dim_ = ExtendedQuadDynamic::x_dim_;
#else
    static constexpr int x_dim_ = NominalQuadDynamic::x_dim_;
#endif
    static constexpr int u_dim_ = 4;
    static constexpr int _dt_num = 1;
    static constexpr int _dt_den = 10;
    static constexpr int _n_step = 10;

private:
    Matrix<double, x_dim_, 1> state_[_n_step];
    Matrix<double, x_dim_, _n_step * u_dim_> state_g_[_n_step];
    Matrix<double, 3, 1> acc_[_n_step];
    Matrix<double, 3, _n_step * u_dim_> acc_g_[_n_step];
    // Matrix<double, _n_step, x_dim_> ref_state_;
    const SdfMap *sdf_;
    MatrixXd ctrl_pts_;
    MatrixXd v_ctrl_pts_;
    MatrixXd a_ctrl_pts_;
    double ts_;
    double t_max_;
    Matrix<double, u_dim_, 1> last_u_;
    Matrix<double, _n_step, u_dim_> ref_u_;
    Matrix<double, x_dim_, 1> init_state_;
    Matrix<double, 3 + u_dim_ + 1 + u_dim_ + 1 + 1 + 1, 1> cost_w; //contour_err_w, lag_err_w, time_w, u_w
    vector<double> tmp_u_;
    bool flag;
    const double dt_;
    Vector3d disturbance_acc_;

    double cbf_cost_;
    double t_w_ratio_;

    nlopt::opt opt_;

    // vector<vector<Eigen::Vector3d>> dynamicObstaclesPos_;
    // vector<vector<Eigen::Vector3d>> dynamicObstaclesSize_;
    // vector<vector<vector<Eigen::Vector3d>>> obPredPos_;
    // vector<vector<vector<Eigen::Vector3d>>> obPredSize_;
    // vector<Eigen::VectorXd> obIntentProb_;
    vector<vector<vector<Eigen::Vector3d>>> predPos_;
    vector<vector<vector<Eigen::Vector3d>>> predSize_;
    vector<Eigen::VectorXd> intentProb_;
    double dynamicSafetyDist_ = 0.6;  

public:
    NominalMpcc(double hover_ratio, string algorithm = "LD_LBFGS", int maxeval = 50);

    static void v_b_constraint(unsigned m, double *result, unsigned n, const double *u,
                             double *gradient, /* NULL if not needed */
                             NominalMpcc *instance);

    static void collision_constraint(unsigned m, double *result, unsigned n, const double *u,
                             double *gradient, /* NULL if not needed */
                             NominalMpcc *instance);

    static void cbf_constraint(unsigned m, double *result, unsigned n, const double *u,
                             double *gradient, /* NULL if not needed */
                             NominalMpcc *instance);

    static double cost_func(const vector<double> &u, vector<double> &grad, NominalMpcc *instance);

    // void updatePredObstacles(const vector<vector<vector<Eigen::Vector3d>>>& predPos, const vector<vector<vector<Eigen::Vector3d>>>& predSize, const vector<Eigen::VectorXd>& intentProb);
    // void getIntentComb(vector<vector<Vector3d>> &intentCombPos, vector<vector<Vector3d>> &intentCombSize);
    // void updateObstacleParam_(const vector<vector<Eigen::Vector3d>> &dynamicObstaclesPos, 
    //                                         const vector<vector<Eigen::Vector3d>> &dynamicObstaclesSize, 
    //                                         vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &oxyz, 
    //                                         vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &osize,
    //                                         int &numDynamicOb);
    void get_dis_ellipsoid(const Vector3d pos, const Vector3d pos_obs, const Vector3d size_obs, double *dis, Vector3d *g = nullptr);
    void get_dis_ellipsoid2(const Vector3d pos, const Vector3d pos_obs, const Vector3d size_obs, double *dis, Vector3d *g = nullptr);
    void updatePredObstacles(const vector<vector<vector<Eigen::Vector3d>>>& predPos, 
                                      const vector<vector<vector<Eigen::Vector3d>>>& predSize, 
                                      const vector<Eigen::VectorXd>& intentProb,
                                      vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &oxyz, 
                                      vector<Eigen::Matrix<double, Eigen::Dynamic, 3>> &osize,  // 将这个改写成vector<vector<vector<Eigen::Vector3d>>>类型
                                      int &numDynamicOb);
    void set_w(Matrix<double, 3 + u_dim_ + 1 + u_dim_ + 1 + 1 + 1, 1> &w);

    double hover_ratio_;

    int solve(const Matrix<double, x_dim_, 1> &state,
        const SdfMap &sdf,
        const MatrixXd &ctrl_pts,
        const MatrixXd &v_ctrl_pts,
        const MatrixXd &a_ctrl_pts,
        const vector<vector<vector<Eigen::Vector3d>>>& predPos,
        const vector<vector<vector<Eigen::Vector3d>>>& predSize,
        const vector<Eigen::VectorXd>& intentProb,
        const double ts,
        const double len,
        const Matrix<double, u_dim_, 1> last_u,
        const Vector3d disturbance_acc,
        Matrix<double, _n_step, u_dim_> &u, 
        Matrix<double, _n_step, x_dim_> &x_predict,
        Matrix<double, _n_step + 1, 1> &t_index,
        double &solve_time);
};

#endif
