#pragma once

#include <iostream>
#include <fstream>
#include <Eigen/Core>
#include <sys/stat.h>
#include <sys/types.h>

using namespace std;
using namespace Eigen;

class Logger {
public:
    ofstream file_;

    //data
    Vector3d pos_;
    Vector3d vel_;
    Vector3d acc_;
    Vector3d att_;
    Vector3d rate_;
    Quaterniond quat_;
    double tilt_;
    Vector3d disturbance_;
    Vector4d u_;
    double dis_to_obs_;
    double solution_time_;
    double length_;
    double time_;

    // 原有构造函数（保持向后兼容）
    Logger(string logname) : Logger(logname, "") {
    }

    // 新的构造函数，支持指定存储路径
    Logger(string logname, string log_path) {
        time_t timep;
        time(&timep);
        char tmp[64];
        strftime(tmp, sizeof(tmp), ("%Y-%m-%d-%H-%M-%S--" + logname + ".txt").c_str(), localtime(&timep));
        
        string full_path;
        if (log_path.empty()) {
            // 如果没有指定路径，使用当前目录
            full_path = tmp;
        } else {
            // 确保路径以 / 结尾
            if (log_path.back() != '/') {
                log_path += "/";
            }
            
            // 创建目录（如果不存在）
            createDirectory(log_path);
            
            // 构建完整路径
            full_path = log_path + tmp;
        }
        
        file_.open(full_path, fstream::out);
        if (!file_) {
            cerr << "Fail to open log file: " << full_path << endl;
            throw "Fail to open logger file";
        }
        cout << "Create log file " << full_path << endl;
    }
    
    ~Logger() {
        file_.close();
    }

    void update() {
        file_ << pos_(0) << " " << pos_(1) << " " << pos_(2) << " ";
        file_ << vel_(0) << " " << vel_(1) << " " << vel_(2) << " ";
        file_ << acc_(0) << " " << acc_(1) << " " << acc_(2) << " ";
        file_ << quat_.w() << " " << quat_.x() << " " << quat_.y() << " " << quat_.z() << " ";
        file_ << att_(0) << " " << att_(1) << " " << att_(2) << " ";
        file_ << rate_(0) << " " << rate_(1) << " " << rate_(2) << " ";
        file_ << tilt_ << " ";
        file_ << disturbance_(0) << " " << disturbance_(1) << " " << disturbance_(2) << " ";
        file_ << u_(0) << " " << u_(1) << " " << u_(2) << " " << u_(3) << " ";
        file_ << dis_to_obs_ << " ";
        file_ << solution_time_ << " ";
        file_ << length_ << " ";
        file_ << time_ << " ";
        file_ << endl;
    }

private:
    // 创建目录的辅助函数
    void createDirectory(const string& path) {
        // 移除末尾的斜杠用于创建目录
        string dir_path = path;
        if (dir_path.back() == '/') {
            dir_path.pop_back();
        }
        
        struct stat st = {0};
        if (stat(dir_path.c_str(), &st) == -1) {
            if (mkdir(dir_path.c_str(), 0755) != 0) {
                cerr << "Warning: Could not create directory " << dir_path << endl;
            } else {
                cout << "Created directory: " << dir_path << endl;
            }
        }
    }
};