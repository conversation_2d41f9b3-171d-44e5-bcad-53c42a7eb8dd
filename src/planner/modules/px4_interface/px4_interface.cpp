#include <iostream>

#include "px4_interface.hpp"

using namespace std;
using namespace Eigen;

Px4Interface::Px4Interface(double size_x_, double size_y_) {
    ros::NodeHandle nh;
    size_x = size_x_;
    size_y = size_y_;
    px4_armed_ = false;
    origin_received_ = false; 
    first_time = true;
    prevVel_ = Eigen::Vector3d::Zero();
    currVel_ = Eigen::Vector3d::Zero();
    currAcc_ = Eigen::Vector3d::Zero();
    // 位姿、速度和角速度(ENU本地坐标系)，本地速度和全局速度是一样的。
    pose_sub_ = nh.subscribe<geometry_msgs::PoseStamped>("/mavros/local_position/pose", 2, boost::bind(&Px4Interface::local_pose_callback, this, _1));
    vel_sub_ = nh.subscribe<geometry_msgs::TwistStamped>("/mavros/local_position/velocity_local", 2, boost::bind(&Px4Interface::local_vel_callback, this, _1));
    rate_sub_ = nh.subscribe<geometry_msgs::TwistStamped>("/mavros/local_position/velocity_body" , 2, boost::bind(&Px4Interface::local_rate_callback, this, _1));
    // 位姿和速度（全局坐标）
    global_odom_sub_ = nh.subscribe<nav_msgs::Odometry>("/global_odom/odom", 2, boost::bind(&Px4Interface::global_odom_callback, this, _1));
    target_pose_sub_ = nh.subscribe<geometry_msgs::PoseStamped>("target/vision_pose/pose", 2, boost::bind(&Px4Interface::target_pose_callback, this, _1));
    target_vel_sub_ = nh.subscribe<geometry_msgs::TwistStamped>("target/vision_speed/speed", 2, boost::bind(&Px4Interface::target_vel_callback, this, _1));
    ball_pose_sub_ = nh.subscribe<geometry_msgs::PoseStamped>("ball/vision_pose/pose", 2, boost::bind(&Px4Interface::ball_pose_callback, this, _1));
    ball_vel_sub_ = nh.subscribe<geometry_msgs::TwistStamped>("ball/vision_speed/speed", 2, boost::bind(&Px4Interface::ball_vel_callback, this, _1));
    // 话题/mavros/imu/data_raw为前左上body坐标系
    acc_sub_ = nh.subscribe<sensor_msgs::Imu>("/mavros/imu/data_raw", 2, boost::bind(&Px4Interface::acc_callback, this, _1));
    px4_state_sub_ = nh.subscribe<mavros_msgs::State>("/mavros/state", 2, boost::bind(&Px4Interface::px4_state_callback, this, _1));
    // 预测数据订阅器
    prediction_sub_ = nh.subscribe<dynamic_predictor::PredictionData>("/planner/prediction", 2, boost::bind(&Px4Interface::prediction_callback, this, _1));
    // /mavros/setpoint_raw/local为本地坐标系
    pos_target_pub_ = nh.advertise<geometry_msgs::PoseStamped>("/mavros/setpoint_position/local", 10);
    att_target_pub_ = nh.advertise<mavros_msgs::AttitudeTarget>("/mavros/setpoint_raw/attitude", 10);
    state_pub_ = nh.advertise<tracking_controller::Target>("/planner/target_state", 10);
    px4_mode_srv_ = nh.serviceClient<mavros_msgs::SetMode>("/mavros/set_mode");
    px4_arm_srv_ = nh.serviceClient<mavros_msgs::CommandBool>("/mavros/cmd/arming");
}

void Px4Interface::global_odom_callback(nav_msgs::OdometryConstPtr msg) {
    // 从里程计消息中提取全局位姿信息
    global_pos_.x() = msg.get()->pose.pose.position.x + size_x / 2;
    global_pos_.y() = msg.get()->pose.pose.position.y + size_y / 2;
    global_pos_.z() = msg.get()->pose.pose.position.z;
    if(!origin_received_){
        origin_position_.x() = msg.get()->pose.pose.position.x + size_x / 2;
        origin_position_.y() = msg.get()->pose.pose.position.y + size_y / 2;
        origin_position_.z() = msg.get()->pose.pose.position.z;
        origin_received_ = true; // 标记原点已接收
    }
    global_quat_.x() = msg.get()->pose.pose.orientation.x;
    global_quat_.y() = msg.get()->pose.pose.orientation.y;
    global_quat_.z() = msg.get()->pose.pose.orientation.z;
    global_quat_.w() = msg.get()->pose.pose.orientation.w;
    
    // 从里程计消息中提取全局速度信息
    global_vel_.x() = msg.get()->twist.twist.linear.x;
    global_vel_.y() = msg.get()->twist.twist.linear.y;
    global_vel_.z() = msg.get()->twist.twist.linear.z;
    ros::Time currTime = ros::Time::now(); // 获取当前时间
    if (first_time){
        currAcc_ = Eigen::Vector3d (0.0, 0.0, 0.0); // 初始化加速度
        prevVel_ = global_vel_;
        prevStateTime_ = currTime; // 初始化时间戳
        first_time = false; // 标记首次更新完成
    }
    else{
        double dt = (currTime - prevStateTime_).toSec(); // 计算时间间隔
        currAcc_ = (global_vel_ - prevVel_)/dt; // 计算加速度
        prevVel_ = global_vel_; // 更新上一时刻速度
        prevStateTime_ = currTime; // 更新上一时刻时间
    }
    global_rate_.x() = msg.get()->twist.twist.angular.x;
    global_rate_.y() = msg.get()->twist.twist.angular.y;
    global_rate_.z() = msg.get()->twist.twist.angular.z;
    
    // 保存时间戳
    global_odom_stamp_ = msg.get()->header.stamp;
}

void Px4Interface::target_pose_callback(geometry_msgs::PoseStampedConstPtr msg) {
    target_pos_.x() = msg.get()->pose.position.x + size_x / 2;
    target_pos_.y() = msg.get()->pose.position.y + size_y / 2;
    target_pos_.z() = msg.get()->pose.position.z;
    // target_quat_.x() = msg.get()->pose.orientation.x;
    // target_quat_.y() = msg.get()->pose.orientation.y;
    // target_quat_.z() = msg.get()->pose.orientation.z;
    // target_quat_.w() = msg.get()->pose.orientation.w;
}

void Px4Interface::target_vel_callback(geometry_msgs::TwistStampedConstPtr msg) {
    target_vel_.x() = msg.get()->twist.linear.x;
    target_vel_.y() = msg.get()->twist.linear.y;
    target_vel_.z() = msg.get()->twist.linear.z;
    target_vel_stamp_ = msg.get()->header.stamp;
    // target_rate_.x() = msg.get()->twist.angular.x;
    // target_rate_.y() = msg.get()->twist.angular.y;
    // target_rate_.z() = msg.get()->twist.angular.z;
}

void Px4Interface::ball_pose_callback(geometry_msgs::PoseStampedConstPtr msg){
    ball_pos_.x() = msg.get()->pose.position.x + size_x / 2;
    ball_pos_.y() = msg.get()->pose.position.y + size_y / 2;
    ball_pos_.z() = msg.get()->pose.position.z;
    // ball_quat_.x() = msg.get()->pose.orientation.x;
    // ball_quat_.y() = msg.get()->pose.orientation.y;
    // ball_quat_.z() = msg.get()->pose.orientation.z;
    // ball_quat_.w() = msg.get()->pose.orientation.w;
}

void Px4Interface::ball_vel_callback(geometry_msgs::TwistStampedConstPtr msg) {
    ball_vel_.x() = msg.get()->twist.linear.x;
    ball_vel_.y() = msg.get()->twist.linear.y;
    ball_vel_.z() = msg.get()->twist.linear.z;
    ball_vel_stamp_ = msg.get()->header.stamp;
    // ball_rate_.x() = msg.get()->twist.angular.x;
    // ball_rate_.y() = msg.get()->twist.angular.y;
    // ball_rate_.z() = msg.get()->twist.angular.z;
}

void Px4Interface::set_rate_with_trust(double rx, double ry, double rz , double thrust) {
    mavros_msgs::AttitudeTarget cmd;
    cmd.header.stamp = ros::Time::now();
    cmd.header.frame_id = "FCU";
    cmd.body_rate.x = rx;
    cmd.body_rate.y = ry;
    cmd.body_rate.z = rz;
    cmd.thrust = thrust;
    cmd.type_mask = mavros_msgs::AttitudeTarget::IGNORE_ATTITUDE;
    att_target_pub_.publish(cmd);
}

void Px4Interface::set_attitude_with_trust(Quaterniond q, double thrust) {
    mavros_msgs::AttitudeTarget cmd;
    cmd.header.stamp = ros::Time::now();
    cmd.header.frame_id = "FCU";
    cmd.orientation.x = q.x();
	cmd.orientation.y = q.y();
	cmd.orientation.z = q.z();
	cmd.orientation.w = q.w();
    cmd.thrust = thrust;
    cmd.type_mask = mavros_msgs::AttitudeTarget::IGNORE_ROLL_RATE |
					mavros_msgs::AttitudeTarget::IGNORE_PITCH_RATE |
					mavros_msgs::AttitudeTarget::IGNORE_YAW_RATE;
    att_target_pub_.publish(cmd);
}

void Px4Interface::set_pos(double x, double y, double z, Eigen::Quaterniond quat) {
    geometry_msgs::PoseStamped ps; 
    ps.header.frame_id = "map"; // 设置坐标系
    ps.header.stamp = ros::Time::now(); // 设置时间戳
    ps.pose.position.x = x; // 设置当前位置 x
    ps.pose.position.y = y; // 设置当前位置 y
    ps.pose.position.z = z; // 设置目标高度
    ps.pose.orientation.x = quat.x();
    ps.pose.orientation.y = quat.y();
    ps.pose.orientation.z = quat.z();
    ps.pose.orientation.w = quat.w(); // 设置当前姿态
    pos_target_pub_.publish(ps);
}

void Px4Interface::arm() {
    mavros_msgs::CommandBool req;
    req.request.value = true;
    if (px4_arm_srv_.call(req)) {
        cout << "Arm" << endl;
    } else {
        cout << "Vehicle arming failed" << endl;
    }
}

void Px4Interface::disarm() {
    mavros_msgs::CommandBool req;
    req.request.value = false;
    if (px4_arm_srv_.call(req)) {
        cout << "Disarm" << endl;
    } else {
        cout << "Vehicle disarming failed" << endl;
    }
}

void Px4Interface::set_px4_mode(string mode) {
    mavros_msgs::SetMode req;
    req.request.custom_mode = mode;
    if (px4_mode_srv_.call(req)) {
        cout << "Set mode: " << mode << endl;
    } else {
        cout << "Failed to set mode: " << mode << endl;
    }
}

void Px4Interface::local_pose_callback(geometry_msgs::PoseStampedConstPtr msg) {
    pos_.x() = msg.get()->pose.position.x;
    pos_.y() = msg.get()->pose.position.y;
    pos_.z() = msg.get()->pose.position.z;
    quat_.x() = msg.get()->pose.orientation.x;
    quat_.y() = msg.get()->pose.orientation.y;
    quat_.z() = msg.get()->pose.orientation.z;
    quat_.w() = msg.get()->pose.orientation.w;
}

void Px4Interface::local_vel_callback(geometry_msgs::TwistStampedConstPtr msg) {
    vel_.x() = msg.get()->twist.linear.x;
    vel_.y() = msg.get()->twist.linear.y;
    vel_.z() = msg.get()->twist.linear.z;
    vel_stamp_ = msg.get()->header.stamp;
}

void Px4Interface::acc_callback(sensor_msgs::ImuConstPtr msg) {
    acc_.x() = msg.get()->linear_acceleration.x;
    acc_.y() = msg.get()->linear_acceleration.y;
    acc_.z() = msg.get()->linear_acceleration.z;
}

void Px4Interface::local_rate_callback(geometry_msgs::TwistStampedConstPtr msg) {
    rate_.x() = msg.get()->twist.angular.x;
    rate_.y() = msg.get()->twist.angular.y;
    rate_.z() = msg.get()->twist.angular.z;
}

void Px4Interface::px4_state_callback(mavros_msgs::StateConstPtr msg) {
    px4_armed_ = msg.get()->armed;
    px4_mode_ = msg.get()->mode;
    current_state = *msg;
}

void Px4Interface::prediction_callback(const dynamic_predictor::PredictionData::ConstPtr& msg) {
    // 清空之前的数据
    posPred.clear();
    sizePred.clear();
    intentProb.clear();
    
    // 如果没有障碍物数据，直接返回
    if (msg->num_obstacles == 0) {
        return;
    }
    
    int numObstacles = msg->num_obstacles;
    int numIntents = msg->num_intents;
    int numTimeSteps = msg->num_time_steps;
    
    // 初始化数据结构
    posPred.resize(numObstacles);
    sizePred.resize(numObstacles);
    intentProb.resize(numObstacles);
    
    // 解析意图概率数据
    for (int i = 0; i < numObstacles; i++) {
        intentProb[i].resize(numIntents);
        for (int j = 0; j < numIntents; j++) {
            int probIndex = i * numIntents + j;
            intentProb[i](j) = msg->intent_probs_data[probIndex];
        }
    }
    
    // 解析位置和尺寸预测数据
    for (int i = 0; i < numObstacles; i++) {
        posPred[i].resize(numIntents);
        sizePred[i].resize(numIntents);
        
        for (int j = 0; j < numIntents; j++) {
            posPred[i][j].resize(numTimeSteps);
            sizePred[i][j].resize(numTimeSteps);
            
            for (int k = 0; k < numTimeSteps; k++) {
                // 计算在一维数组中的索引
                int dataIndex = i * numIntents * numTimeSteps + j * numTimeSteps + k;
                
                // 解析位置数据
                posPred[i][j][k] = Eigen::Vector3d(
                    msg->pos_pred_data[dataIndex].x + size_x / 2,
                    msg->pos_pred_data[dataIndex].y + size_y / 2, 
                    msg->pos_pred_data[dataIndex].z
                );
                
                // 解析尺寸数据
                sizePred[i][j][k] = Eigen::Vector3d(
                    msg->size_pred_data[dataIndex].x,
                    msg->size_pred_data[dataIndex].y,
                    msg->size_pred_data[dataIndex].z
                );
            }
        }
    }
}
