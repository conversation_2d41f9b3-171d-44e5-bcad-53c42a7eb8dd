#ifndef _PX4_INTERFACE_HPP
#define _PX4_INTERFACE_HPP

#include <string>

#include <Eigen/Dense>
#include <ros/ros.h>

#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <sensor_msgs/Imu.h>
#include <nav_msgs/Odometry.h>
#include <mavros_msgs/State.h>
#include <mavros_msgs/AttitudeTarget.h>
#include <mavros_msgs/PositionTarget.h>
#include <mavros_msgs/SetMode.h>
#include <mavros_msgs/CommandBool.h>
#include <tracking_controller/Target.h>
#include <dynamic_predictor/PredictionData.h>
#include "map/map.hpp"
#include "map/sdf.hpp"

using namespace std;
using namespace Eigen;

class Px4Interface {
private:
    ros::Subscriber pose_sub_;
    ros::Subscriber global_pose_sub_;
    ros::Subscriber global_odom_sub_;
    ros::Subscriber gloable_vel_sub_;
    ros::Subscriber target_pose_sub_;
    ros::Subscriber target_vel_sub_;
    ros::Subscriber ball_pose_sub_;
    ros::Subscriber ball_vel_sub_;
    ros::Subscriber vel_sub_;
    ros::Subscriber acc_sub_;
    ros::Subscriber rate_sub_;
    ros::Subscriber px4_state_sub_;
    ros::Subscriber prediction_sub_;  // 预测数据订阅器
    ros::Publisher att_target_pub_;

    Matrix<double, 3, 1> pos_, vel_, acc_, rate_, global_pos_, global_vel_, global_rate_, target_pos_, target_vel_, ball_pos_, ball_vel_; // vel_是速度 rate_是角速度
    Matrix<double, 4, 1> quat_, global_quat_;
    double size_x, size_y;
    bool px4_armed_;
    string px4_mode_;
    ros::Time vel_stamp_, target_vel_stamp_, global_vel_stamp_, ball_vel_stamp_, global_odom_stamp_;

    // 加速度计算相关变量
    Eigen::Vector3d prevVel_, currVel_, currAcc_;  // 速度和加速度变量
    ros::Time prevStateTime_;                       // 上一时刻时间戳
    
    // 预测数据存储变量
    std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred;
    std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred;
    std::vector<Eigen::VectorXd> intentProb;

    void local_pose_callback(geometry_msgs::PoseStampedConstPtr msg);
    void global_odom_callback(nav_msgs::OdometryConstPtr msg);
    void target_pose_callback(geometry_msgs::PoseStampedConstPtr msg);
    void target_vel_callback(geometry_msgs::TwistStampedConstPtr msg);
    void ball_pose_callback(geometry_msgs::PoseStampedConstPtr msg);
    void ball_vel_callback(geometry_msgs::TwistStampedConstPtr msg);
    void local_vel_callback(geometry_msgs::TwistStampedConstPtr msg);
    void acc_callback(sensor_msgs::ImuConstPtr msg);
    void local_rate_callback(geometry_msgs::TwistStampedConstPtr msg);
    void px4_state_callback(mavros_msgs::StateConstPtr msg);
    void prediction_callback(const dynamic_predictor::PredictionData::ConstPtr& msg);  // 预测数据回调函数

public:
    ros::ServiceClient px4_mode_srv_;
    ros::ServiceClient px4_arm_srv_;
    ros::Publisher pos_target_pub_;
    ros::Publisher state_pub_;
    bool origin_received_;
    bool first_time;    
    Matrix<double, 3, 1> origin_position_;
    Px4Interface(double size_x_, double size_y_);
    void set_rate_with_trust(double rx, double ry, double rz, double thrust);
    void set_attitude_with_trust(Quaterniond q, double thrust);
    void set_pos(double x, double y, double z, Eigen::Quaterniond quat);
    void arm();
    void disarm();
    void set_px4_mode(string mode);
    const Matrix<double, 3, 1> &global_pos() {return global_pos_;}
    const Matrix<double, 3, 1> &global_vel() {return global_vel_;}
    const Matrix<double, 3, 1> &global_acc() {return currAcc_;}
    const Matrix<double, 4, 1> &global_quat() {return global_quat_;}
    const Matrix<double, 3, 1> &global_rate() {return global_rate_;}
    const Matrix<double, 3, 1> &target_pos() {return target_pos_;}
    const Matrix<double, 3, 1> &target_vel() {return target_vel_;}
    const Matrix<double, 3, 1> &ball_pos() {return ball_pos_;}
    const Matrix<double, 3, 1> &ball_vel() {return ball_vel_;}
    const Matrix<double, 3, 1> &pos() {return pos_;}
    const Matrix<double, 3, 1> &vel() {return vel_;}
    const Matrix<double, 4, 1> &quat() {return quat_;}
    const Matrix<double, 3, 1> &rate() {return rate_;}
    const Matrix<double, 3, 1> &acc() {return acc_;}
    mavros_msgs::State current_state;
    ros::Time &vel_stamp() {return vel_stamp_;}
    ros::Time &global_odom_stamp() {return global_odom_stamp_;}
    
    // 获取预测数据的访问器函数
    const std::vector<std::vector<std::vector<Eigen::Vector3d>>>& get_pos_pred() const { return posPred; }
    const std::vector<std::vector<std::vector<Eigen::Vector3d>>>& get_size_pred() const { return sizePred; }
    const std::vector<Eigen::VectorXd>& get_intent_prob() const { return intentProb; }
};

#endif