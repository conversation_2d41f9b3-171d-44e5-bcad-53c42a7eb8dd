#pragma once

#include <Eigen/Core>
#include <cmath>
#include <vector>

using namespace Eigen;

/**
 * @brief DOp-CBF扰动观测器类
 * 基于论文"Disturbance Observer Parameterized Control Barrier Functions"的思路
 * 实现更精确的扰动估计和误差补偿，用于动态参数化的安全屏障函数
 */
class DopCbfObserver {
private:
    // 观测器状态
    Vector3d z_;                    // 观测器内部状态
    Vector3d d_hat_;               // 扰动估计值
    Vector3d e_d_;                 // 观测误差 (d - d_hat)
    
    // 观测器参数
    Vector3d l_gain_;              // 观测器增益 l(x)
    double omega_;                 // 扰动变化率上界 ||ḋ|| ≤ ω
    
    // 李雅普诺夫函数相关
    double V_e_;                   // 误差李雅普诺夫函数 V_e = 0.5 * ||e_d||²
    Vector3d grad_V_e_;           // V_e的梯度
    
    // 鲁棒性参数
    double sigma_;                 // 安全系数，用于h_de = h_d̂ - σV_e
    double convergence_rate_;      // 期望的收敛率
    
    // 自适应参数
    bool enable_adaptive_;         // 是否启用自适应增益
    Vector3d l_adaptive_;         // 自适应增益
    double adaptation_rate_;       // 自适应速率
    
    // 历史数据用于趋势分析
    std::vector<Vector3d> d_history_;  // 扰动历史
    std::vector<double> time_history_; // 时间历史
    size_t history_size_;              // 历史数据大小
    
public:
    /**
     * @brief 构造函数
     * @param l_gain 观测器增益
     * @param omega 扰动变化率上界
     * @param sigma 安全系数
     * @param convergence_rate 期望收敛率
     */
    DopCbfObserver(const Vector3d& l_gain, double omega, double sigma = 1.0, 
                   double convergence_rate = 1.0, bool enable_adaptive = false) :
        l_gain_(l_gain), omega_(omega), sigma_(sigma), 
        convergence_rate_(convergence_rate), enable_adaptive_(enable_adaptive),
        adaptation_rate_(0.1), history_size_(10) {
        
        z_.setZero();
        d_hat_.setZero();
        e_d_.setZero();
        V_e_ = 0.0;
        grad_V_e_.setZero();
        l_adaptive_ = l_gain_;
        
        d_history_.reserve(history_size_);
        time_history_.reserve(history_size_);
    }
    
    /**
     * @brief 更新观测器状态
     * @param x 系统状态
     * @param u 控制输入
     * @param f_x 系统动力学 f(x)
     * @param g1_x 控制输入矩阵 g1(x)
     * @param g2_x 扰动输入矩阵 g2(x)
     * @param dt 时间步长
     */
    void update(const VectorXd& x, const Vector4d& u, const Vector3d& f_x,
                const Matrix<double, 3, 4>& g1_x, const Matrix3d& g2_x, double dt) {
        
        // 计算p(x) - 这里简化为零，实际应用中可以根据具体系统设计
        Vector3d p_x = Vector3d::Zero();
        
        // 更新扰动估计: d̂ = z + p(x)
        d_hat_ = z_ + p_x;
        
        // 更新观测器内部状态: ż = -l(x)(f(x) + g₁(x)u + g₂(x)d̂)
        Vector3d current_l = enable_adaptive_ ? l_adaptive_ : l_gain_;
        Vector3d system_dynamics = f_x + g1_x * u + g2_x * d_hat_;
        z_ += -current_l.cwiseProduct(system_dynamics) * dt;
        
        // 更新李雅普诺夫函数
        V_e_ = 0.5 * e_d_.squaredNorm();
        grad_V_e_ = e_d_;
        
        // 自适应增益调整
        if (enable_adaptive_) {
            updateAdaptiveGain(dt);
        }
        
        // 更新历史数据
        updateHistory(dt);
    }
    
    /**
     * @brief 获取扰动估计值
     */
    Vector3d getDisturbanceEstimate() const {
        return d_hat_;
    }
    
    /**
     * @brief 获取观测误差的李雅普诺夫函数值
     */
    double getErrorLyapunovFunction() const {
        return V_e_;
    }
    
    /**
     * @brief 获取鲁棒性补偿项 σV_e
     */
    double getRobustnessCompensation() const {
        return sigma_ * V_e_;
    }
    
    /**
     * @brief 计算前馈补偿项 ι_d(x)
     * 这是论文公式(22)中的关键项，用于抵消观测误差的影响
     */
    double getFeedforwardCompensation(const VectorXd& x, const Matrix3d& g2_x) const {
        // 简化版本的前馈补偿计算
        // 实际实现需要根据具体的屏障函数h和系统动力学来计算
        double L_g2_h_norm = 1.0; // ||L_g₂h||，需要根据具体的h计算
        double compensation = sigma_ * convergence_rate_ * V_e_ + 
                             sigma_ * L_g2_h_norm * omega_ * sqrt(2 * V_e_);
        return compensation;
    }
    
    /**
     * @brief 预测未来时刻的扰动
     * @param prediction_time 预测时间
     */
    Vector3d predictDisturbance(double prediction_time) const {
        if (d_history_.size() < 2) {
            return d_hat_; // 历史数据不足，返回当前估计
        }
        
        // 基于历史数据进行线性外推
        Vector3d trend = computeDisturbanceTrend();
        Vector3d predicted = d_hat_ + trend * prediction_time;
        
        // 应用衰减因子，假设扰动随时间衰减
        double decay_factor = std::exp(-0.1 * prediction_time);
        predicted *= decay_factor;
        
        return predicted;
    }
    
    /**
     * @brief 设置观测器参数
     */
    void setParameters(const Vector3d& l_gain, double omega, double sigma, 
                      double convergence_rate) {
        l_gain_ = l_gain;
        omega_ = omega;
        sigma_ = sigma;
        convergence_rate_ = convergence_rate;
        if (!enable_adaptive_) {
            l_adaptive_ = l_gain_;
        }
    }
    
    /**
     * @brief 启用/禁用自适应功能
     */
    void enableAdaptive(bool enable, double adaptation_rate = 0.1) {
        enable_adaptive_ = enable;
        adaptation_rate_ = adaptation_rate;
        if (!enable) {
            l_adaptive_ = l_gain_;
        }
    }
    
    /**
     * @brief 重置观测器状态
     */
    void reset() {
        z_.setZero();
        d_hat_.setZero();
        e_d_.setZero();
        V_e_ = 0.0;
        grad_V_e_.setZero();
        l_adaptive_ = l_gain_;
        d_history_.clear();
        time_history_.clear();
    }

private:
    /**
     * @brief 更新自适应增益
     */
    void updateAdaptiveGain(double dt) {
        // 基于观测误差大小调整增益
        double error_magnitude = e_d_.norm();
        
        for (int i = 0; i < 3; i++) {
            if (error_magnitude > 0.1) {
                // 误差较大时增加增益
                l_adaptive_(i) = std::min(l_adaptive_(i) * (1.0 + adaptation_rate_), 
                                        l_gain_(i) * 3.0);
            } else if (error_magnitude < 0.01) {
                // 误差较小时减少增益以提高稳定性
                l_adaptive_(i) = std::max(l_adaptive_(i) * (1.0 - adaptation_rate_), 
                                        l_gain_(i) * 0.3);
            }
        }
    }
    
    /**
     * @brief 更新历史数据
     */
    void updateHistory(double dt) {
        static double accumulated_time = 0.0;
        accumulated_time += dt;
        
        d_history_.push_back(d_hat_);
        time_history_.push_back(accumulated_time);
        
        // 保持历史数据大小
        if (d_history_.size() > history_size_) {
            d_history_.erase(d_history_.begin());
            time_history_.erase(time_history_.begin());
        }
    }
    
    /**
     * @brief 计算扰动趋势
     */
    Vector3d computeDisturbanceTrend() const {
        if (d_history_.size() < 2) {
            return Vector3d::Zero();
        }
        
        // 简单的线性回归计算趋势
        size_t n = d_history_.size();
        Vector3d trend = Vector3d::Zero();
        
        if (n >= 2) {
            double dt = time_history_[n-1] - time_history_[n-2];
            if (dt > 1e-6) {
                trend = (d_history_[n-1] - d_history_[n-2]) / dt;
            }
        }
        
        return trend;
    }
};
