#ifndef _NOMINAL_QUAD_DYNAMIC_HPP
#define _NOMINAL_QUAD_DYNAMIC_HPP

#include <iostream>
#include <vector>
#include <Eigen/Dense>

using namespace std;
using namespace Eigen;

#define PX 0
#define PY 1
#define PZ 2
#define VX 3
#define VY 4
#define VZ 5
#define QW 6
#define QX 7
#define QY 8
#define QZ 9
#define RX 0
#define RY 1
#define RZ 2
#define TH 3

class NominalQuadDynamic {
public:
    static constexpr int x_dim_ = 10;
    static constexpr int u_dim_ = 4;

protected:
    const double hover_ratio_;

    Matrix<double, x_dim_, 1> aux;
    Matrix<double, x_dim_, 1> xdot1;
    Matrix<double, x_dim_, 1> xdot2;
    Matrix<double, x_dim_, 1> xdot3;
    Matrix<double, x_dim_, 1> xdot4;
    Matrix<double, x_dim_, x_dim_> xd1gx0;
    Matrix<double, x_dim_, x_dim_> xd2gx1;
    Matrix<double, x_dim_, x_dim_> xd3gx2;
    Matrix<double, x_dim_, x_dim_> xd4gx3;
    Matrix<double, x_dim_, u_dim_> xd1gu;
    Matrix<double, x_dim_, u_dim_> xd2gu;
    Matrix<double, x_dim_, u_dim_> xd3gu;
    Matrix<double, x_dim_, u_dim_> xd4gu;
    Matrix<double, x_dim_, x_dim_> x1gx0;
    Matrix<double, x_dim_, x_dim_> x2gx0;
    Matrix<double, x_dim_, x_dim_> x3gx0;
    Matrix<double, x_dim_, u_dim_> x1gu;
    Matrix<double, x_dim_, u_dim_> x2gu;
    Matrix<double, x_dim_, u_dim_> x3gu;
    Matrix<double, x_dim_, x_dim_> iden;

public:
    NominalQuadDynamic(double hover_ratio) :
        hover_ratio_(hover_ratio) {
        iden = MatrixXd::Identity(x_dim_, x_dim_);
        aux.setZero();
        xdot1.setZero();
        xdot2.setZero();
        xdot3.setZero();
        xdot4.setZero();
        xd1gx0.setZero();
        xd2gx1.setZero();
        xd3gx2.setZero();
        xd4gx3.setZero();
        xd1gu.setZero();
        xd2gu.setZero();
        xd3gu.setZero();
        xd4gu.setZero();
        x1gx0.setZero();
        x2gx0.setZero();
        x3gx0.setZero();
        x1gu.setZero();
        x2gu.setZero();
        x3gu.setZero();
    }

    //xdot = f(x,u), gx = df / dx, gu = df / du
    void xdot_func(const Matrix<double, x_dim_, 1> &x, const VectorXd &u, const Vector3d &disturbance_acc,
        Matrix<double, x_dim_, 1> &xdot, Matrix<double, x_dim_, x_dim_> &gx, 
        Matrix<double, x_dim_, u_dim_> &gu) {
        const double &px = x(0);
        const double &py = x(1);
        const double &pz = x(2);
        const double &vx = x(3);
        const double &vy = x(4);
        const double &vz = x(5);
        const double &qw = x(6);
        const double &qx = x(7);
        const double &qy = x(8);
        const double &qz = x(9);
        const double &rx = u[0];
        const double &ry = u[1];
        const double &rz = u[2];
        const double thrust = /*1.084e-5 * pow(u[3] * 1000 + 100, 2) * 4 / 0.74;//*/u[3] * 9.79362 / hover_ratio_;
        xdot << vx, vy, vz, 
                2 * (qx * qz + qw * qy) * thrust + disturbance_acc.x(), 2 * (qy * qz - qw * qx) * thrust + disturbance_acc.y(), ((1 - 2 * (qx * qx + qy * qy)) * thrust - 9.79362 + disturbance_acc.z()),
                0.5 * (-rx * qx - ry * qy - rz * qz), 0.5 * (rx * qw + rz * qy - ry * qz), 0.5 * (ry * qw - rz * qx + rx * qz), 0.5 * (rz * qw + ry * qx - rx * qy);
        gx(PX, VX) = 1;
        gx(PY, VY) = 1;
        gx(PZ, VZ) = 1;
        gx(VX, QX) = 2 * qz * thrust, gx(VX, QY) = 2 * qw * thrust, gx(VX, QZ) = 2 * qx * thrust, gx(VX, QW) = 2 * qy * thrust;
        gx(VY, QX) = -2 * qw * thrust, gx(VY, QY) = 2 * qz * thrust, gx(VY, QZ) = 2 * qy * thrust, gx(VY, QW) = -2 * qx * thrust;
        gx(VZ, QX) = -4 * qx * thrust, gx(VZ, QY) = -4 * qy * thrust;
        gx(QW, QX) = 0.5 * -rx, gx(QW, QY) = 0.5 * -ry, gx(QW, QZ) = 0.5 * -rz;
        gx(QX, QW) = 0.5 * rx, gx(QX, QY) = 0.5 * rz, gx(QX, QZ) = 0.5 * -ry;
        gx(QY, QW) = 0.5 * ry, gx(QY, QX) = 0.5 * -rz, gx(QY, QZ) = 0.5 * rx;
        gx(QZ, QW) = 0.5 * rz, gx(QZ, QX) = 0.5 * ry, gx(QZ, QY) = 0.5 * -rx;
        gu(VX, TH) = 2 * (qx * qz + qw * qy) * /*1.084e-5 * 2 * (u[3] * 1000 + 100) * 1000 * 4 / 0.74;//*/9.79362 / hover_ratio_;
        gu(VY, TH) = 2 * (qy * qz - qw * qx) * /*1.084e-5 * 2 * (u[3] * 1000 + 100) * 1000 * 4 / 0.74;//*/9.79362 / hover_ratio_;
        gu(VZ, TH) = (1 - 2 * (qx * qx + qy * qy)) * /*1.084e-5 * 2 * (u[3] * 1000 + 100) * 1000 * 4 / 0.74;//*/9.79362 / hover_ratio_;
        gu(QW, RX) = 0.5 * -qx, gu(QW, RY) = 0.5 * -qy, gu(QW, RZ) = 0.5 * -qz;
        gu(QX, RX) = 0.5 * qw, gu(QX, RY) = 0.5 * -qz, gu(QX, RZ) = 0.5 * qy;
        gu(QY, RX) = 0.5 * qz, gu(QY, RY) = 0.5 * qw, gu(QY, RZ) = 0.5 * -qx;
        gu(QZ, RX) = 0.5 * -qy, gu(QZ, RY) = 0.5 * qx, gu(QZ, RZ) = 0.5 * qw;
    }

    void xdot_func_(const Matrix<double, x_dim_, 1> &x, const VectorXd &u, const Vector3d &disturbance_acc,
        Matrix<double, x_dim_, 1> &xdot, Matrix<double, x_dim_, x_dim_> &gx, 
        Matrix<double, x_dim_, u_dim_> &gu) {
        const double &px = x(0);
        const double &py = x(1);
        const double &pz = x(2);
        const double &vx = x(3);
        const double &vy = x(4);
        const double &vz = x(5);
        const double &qw = x(6);
        const double &qx = x(7);
        const double &qy = x(8);
        const double &qz = x(9);
        const double &rx = u[0];
        const double &ry = u[1];
        const double &rz = u[2];
        const double thrust = /*1.084e-5 * pow(u[3] * 1000 + 100, 2) * 4 / 0.74;//*/u[3] * 9.79362 / hover_ratio_;
        const double kh = 0.0292527;
        const double dx = 0.339147;
        const double dy = 0.517046;
        const double dz = 0.145518;
        xdot << vx, vy, vz, 
                -vx*(dx*pow(2*qy*qy + 2*qz*qz - 1, 2) + 4*dy*pow(qw*qz - qx*qy, 2) + 4*dz*pow(qw*qy + qx*qz, 2)) + 2*vy*(dx*(qw*qz + qx*qy)*(2*qy*qy + 2*qz*qz - 1) - dy*(qw*qz - qx*qy)*(2*qx*qx + 2*qz*qz - 1) + 2*dz*(qw*qx - qy*qz)*(qw*qy + qx*qz)) + 2*vz*(-dx*(qw*qy - qx*qz)*(2*qy*qy + 2*qz*qz - 1) + 2*dy*(qw*qx + qy*qz)*(qw*qz - qx*qy) + dz*(qw*qy + qx*qz)*(2*qx*qx + 2*qy*qy - 1)) + 2*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust)*(qw*qy + qx*qz) + disturbance_acc.x(), 
                2*vx*(dx*(qw*qz + qx*qy)*(2*qy*qy + 2*qz*qz - 1) - dy*(qw*qz - qx*qy)*(2*qx*qx + 2*qz*qz - 1) + 2*dz*(qw*qx - qy*qz)*(qw*qy + qx*qz)) - vy*(4*dx*pow(qw*qz + qx*qy, 2) + dy*pow(2*qx*qx + 2*qz*qz - 1, 2) + 4*dz*pow(qw*qx - qy*qz, 2)) + 2*vz*(2*dx*(qw*qy - qx*qz)*(qw*qz + qx*qy) + dy*(qw*qx + qy*qz)*(2*qx*qx + 2*qz*qz - 1) - dz*(qw*qx - qy*qz)*(2*qx*qx + 2*qy*qy - 1)) - 2*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust)*(qw*qx - qy*qz) + disturbance_acc.y(),
                2*vx*(-dx*(qw*qy - qx*qz)*(2*qy*qy + 2*qz*qz - 1) + 2*dy*(qw*qx + qy*qz)*(qw*qz - qx*qy) + dz*(qw*qy + qx*qz)*(2*qx*qx + 2*qy*qy - 1)) + 2*vy*(2*dx*(qw*qy - qx*qz)*(qw*qz + qx*qy) + dy*(qw*qx + qy*qz)*(2*qx*qx + 2*qz*qz - 1) - dz*(qw*qx - qy*qz)*(2*qx*qx + 2*qy*qy - 1)) - vz*(4*dx*pow(qw*qy - qx*qz, 2) + 4*dy*pow(qw*qx + qy*qz, 2) + dz*pow(2*qx*qx + 2*qy*qy - 1, 2)) - (kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust)*(2*qx*qx + 2*qy*qy - 1) - 9.79362 + disturbance_acc.z(),
                0.5 * (-rx * qx - ry * qy - rz * qz), 0.5 * (rx * qw + rz * qy - ry * qz), 0.5 * (ry * qw - rz * qx + rx * qz), 0.5 * (rz * qw + ry * qx - rx * qy);
        gx(PX, VX) = 1;
        gx(PY, VY) = 1;
        gx(PZ, VZ) = 1;
        gx(VX, VX) = -dx*pow(2*qy*qy + 2*qz*qz - 1, 2) - 4*dy*pow(qw*qz - qx*qy, 2) - 4*dz*pow(qw*qy + qx*qz, 2) - 4*kh*(qw*qy + qx*qz)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1);
        gx(VX, VY) = 2*dx*(qw*qz + qx*qy)*(2*qy*qy + 2*qz*qz - 1) - 2*dy*(qw*qz - qx*qy)*(2*qx*qx + 2*qz*qz - 1) + 4*dz*(qw*qx - qy*qz)*(qw*qy + qx*qz) + 4*kh*(qw*qy + qx*qz)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1);
        gx(VX, VZ) = -2*dx*(qw*qy - qx*qz)*(2*qy*qy + 2*qz*qz - 1) + 4*dy*(qw*qx + qy*qz)*(qw*qz - qx*qy) + 2*dz*(qw*qy + qx*qz)*(2*qx*qx + 2*qy*qy - 1) + 8*kh*(qw*qy + qx*qz)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(qw*qx - qw*qy + qx*qz + qy*qz);
        gx(VY, VX) = 2*dx*(qw*qz + qx*qy)*(2*qy*qy + 2*qz*qz - 1) - 2*dy*(qw*qz - qx*qy)*(2*qx*qx + 2*qz*qz - 1) + 4*dz*(qw*qx - qy*qz)*(qw*qy + qx*qz) + 4*kh*(qw*qx - qy*qz)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1);
        gx(VY, VY) = -4*dx*pow(qw*qz + qx*qy, 2) - dy*pow(2*qx*qx + 2*qz*qz - 1, 2) - 4*dz*pow(qw*qx - qy*qz, 2) - 4*kh*(qw*qx - qy*qz)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1);
        gx(VY, VZ) = 4*dx*(qw*qy - qx*qz)*(qw*qz + qx*qy) + 2*dy*(qw*qx + qy*qz)*(2*qx*qx + 2*qz*qz - 1) - 2*dz*(qw*qx - qy*qz)*(2*qx*qx + 2*qy*qy - 1) - 8*kh*(qw*qx - qy*qz)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(qw*qx - qw*qy + qx*qz + qy*qz);
        gx(VZ, VX) = -2*dx*(qw*qy - qx*qz)*(2*qy*qy + 2*qz*qz - 1) + 4*dy*(qw*qx + qy*qz)*(qw*qz - qx*qy) + 2*dz*(qw*qy + qx*qz)*(2*qx*qx + 2*qy*qy - 1) + 2*kh*(2*qx*qx + 2*qy*qy - 1)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1);
        gx(VZ, VY) = 4*dx*(qw*qy - qx*qz)*(qw*qz + qx*qy) + 2*dy*(qw*qx + qy*qz)*(2*qx*qx + 2*qz*qz - 1) - 2*dz*(qw*qx - qy*qz)*(2*qx*qx + 2*qy*qy - 1) - 2*kh*(2*qx*qx + 2*qy*qy - 1)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1);
        gx(VZ, VZ) = -4*dx*pow(qw*qy - qx*qz, 2) - 4*dy*pow(qw*qx + qy*qz, 2) - dz*pow(2*qx*qx + 2*qy*qy - 1, 2) - 4*kh*(2*qx*qx + 2*qy*qy - 1)*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz))*(qw*qx - qw*qy + qx*qz + qy*qz);
        gx(VX, QW) = 8*kh*(qw*qy + qx*qz)*(-qz*vx + qz*vy + vz*(qx - qy))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*qy*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) - 8*vx*(dy*qz*(qw*qz - qx*qy) + dz*qy*(qw*qy + qx*qz)) + 2*vy*(dx*qz*(2*qy*qy + 2*qz*qz - 1) - dy*qz*(2*qx*qx + 2*qz*qz - 1) + 2*dz*qx*(qw*qy + qx*qz) + 2*dz*qy*(qw*qx - qy*qz)) + 2*vz*(-dx*qy*(2*qy*qy + 2*qz*qz - 1) + 2*dy*qx*(qw*qz - qx*qy) + 2*dy*qz*(qw*qx + qy*qz) + dz*qy*(2*qx*qx + 2*qy*qy - 1));
        gx(VX, QX) = 8*kh*(qw*qy + qx*qz)*(qy*vx - vy*(2*qx - qy) + vz*(qw + qz))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*qz*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) + 8*vx*(dy*qy*(qw*qz - qx*qy) - dz*qz*(qw*qy + qx*qz)) + 2*vy*(dx*qy*(2*qy*qy + 2*qz*qz - 1) - 4*dy*qx*(qw*qz - qx*qy) + dy*qy*(2*qx*qx + 2*qz*qz - 1) + 2*dz*qw*(qw*qy + qx*qz) + 2*dz*qz*(qw*qx - qy*qz)) + 2*vz*(dx*qz*(2*qy*qy + 2*qz*qz - 1) + 2*dy*qw*(qw*qz - qx*qy) - 2*dy*qy*(qw*qx + qy*qz) + 4*dz*qx*(qw*qy + qx*qz) + dz*qz*(2*qx*qx + 2*qy*qy - 1));
        gx(VX, QY) = 8*kh*(qw*qy + qx*qz)*(qx*vy + vx*(qx - 2*qy) - vz*(qw - qz))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*qw*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) - 8*vx*(dx*qy*(2*qy*qy + 2*qz*qz - 1) - dy*qx*(qw*qz - qx*qy) + dz*qw*(qw*qy + qx*qz)) + 2*vy*(dx*qx*(2*qy*qy + 2*qz*qz - 1) + 4*dx*qy*(qw*qz + qx*qy) + dy*qx*(2*qx*qx + 2*qz*qz - 1) + 2*dz*qw*(qw*qx - qy*qz) - 2*dz*qz*(qw*qy + qx*qz)) - 2*vz*(dx*qw*(2*qy*qy + 2*qz*qz - 1) + 4*dx*qy*(qw*qy - qx*qz) + 2*dy*qx*(qw*qx + qy*qz) - 2*dy*qz*(qw*qz - qx*qy) - dz*qw*(2*qx*qx + 2*qy*qy - 1) - 4*dz*qy*(qw*qy + qx*qz));
        gx(VX, QZ) = 8*kh*(qw*qy + qx*qz)*(-vx*(qw + 2*qz) + vy*(qw - 2*qz) + vz*(qx + qy))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*qx*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) - 8*vx*(dx*qz*(2*qy*qy + 2*qz*qz - 1) + dy*qw*(qw*qz - qx*qy) + dz*qx*(qw*qy + qx*qz)) + 2*vy*(dx*qw*(2*qy*qy + 2*qz*qz - 1) + 4*dx*qz*(qw*qz + qx*qy) - dy*qw*(2*qx*qx + 2*qz*qz - 1) - 4*dy*qz*(qw*qz - qx*qy) + 2*dz*qx*(qw*qx - qy*qz) - 2*dz*qy*(qw*qy + qx*qz)) + 2*vz*(dx*qx*(2*qy*qy + 2*qz*qz - 1) - 4*dx*qz*(qw*qy - qx*qz) + 2*dy*qw*(qw*qx + qy*qz) + 2*dy*qy*(qw*qz - qx*qy) + dz*qx*(2*qx*qx + 2*qy*qy - 1));
        gx(VY, QW) = -8*kh*(qw*qx - qy*qz)*(-qz*vx + qz*vy + vz*(qx - qy))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) - 2*qx*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) + 2*vx*(dx*qz*(2*qy*qy + 2*qz*qz - 1) - dy*qz*(2*qx*qx + 2*qz*qz - 1) + 2*dz*qx*(qw*qy + qx*qz) + 2*dz*qy*(qw*qx - qy*qz)) - 8*vy*(dx*qz*(qw*qz + qx*qy) + dz*qx*(qw*qx - qy*qz)) + 2*vz*(2*dx*qy*(qw*qz + qx*qy) + 2*dx*qz*(qw*qy - qx*qz) + dy*qx*(2*qx*qx + 2*qz*qz - 1) - dz*qx*(2*qx*qx + 2*qy*qy - 1));
        gx(VY, QX) = -8*kh*(qw*qx - qy*qz)*(qy*vx - vy*(2*qx - qy) + vz*(qw + qz))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) - 2*qw*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) + 2*vx*(dx*qy*(2*qy*qy + 2*qz*qz - 1) - 4*dy*qx*(qw*qz - qx*qy) + dy*qy*(2*qx*qx + 2*qz*qz - 1) + 2*dz*qw*(qw*qy + qx*qz) + 2*dz*qz*(qw*qx - qy*qz)) - 8*vy*(dx*qy*(qw*qz + qx*qy) + dy*qx*(2*qx*qx + 2*qz*qz - 1) + dz*qw*(qw*qx - qy*qz)) + 2*vz*(2*dx*qy*(qw*qy - qx*qz) - 2*dx*qz*(qw*qz + qx*qy) + dy*qw*(2*qx*qx + 2*qz*qz - 1) + 4*dy*qx*(qw*qx + qy*qz) - dz*qw*(2*qx*qx + 2*qy*qy - 1) - 4*dz*qx*(qw*qx - qy*qz));
        gx(VY, QY) = -8*kh*(qw*qx - qy*qz)*(qx*vy + vx*(qx - 2*qy) - vz*(qw - qz))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*qz*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) + 2*vx*(dx*qx*(2*qy*qy + 2*qz*qz - 1) + 4*dx*qy*(qw*qz + qx*qy) + dy*qx*(2*qx*qx + 2*qz*qz - 1) + 2*dz*qw*(qw*qx - qy*qz) - 2*dz*qz*(qw*qy + qx*qz)) - 8*vy*(dx*qx*(qw*qz + qx*qy) - dz*qz*(qw*qx - qy*qz)) + 2*vz*(2*dx*qw*(qw*qz + qx*qy) + 2*dx*qx*(qw*qy - qx*qz) + dy*qz*(2*qx*qx + 2*qz*qz - 1) - 4*dz*qy*(qw*qx - qy*qz) + dz*qz*(2*qx*qx + 2*qy*qy - 1));
        gx(VY, QZ) = -8*kh*(qw*qx - qy*qz)*(-vx*(qw + 2*qz) + vy*(qw - 2*qz) + vz*(qx + qy))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*qy*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) + 2*vx*(dx*qw*(2*qy*qy + 2*qz*qz - 1) + 4*dx*qz*(qw*qz + qx*qy) - dy*qw*(2*qx*qx + 2*qz*qz - 1) - 4*dy*qz*(qw*qz - qx*qy) + 2*dz*qx*(qw*qx - qy*qz) - 2*dz*qy*(qw*qy + qx*qz)) - 8*vy*(dx*qw*(qw*qz + qx*qy) + dy*qz*(2*qx*qx + 2*qz*qz - 1) - dz*qy*(qw*qx - qy*qz)) + 2*vz*(2*dx*qw*(qw*qy - qx*qz) - 2*dx*qx*(qw*qz + qx*qy) + dy*qy*(2*qx*qx + 2*qz*qz - 1) + 4*dy*qz*(qw*qx + qy*qz) + dz*qy*(2*qx*qx + 2*qy*qy - 1));
        gx(VZ, QW) = -4*kh*(2*qx*qx + 2*qy*qy - 1)*(-qz*vx + qz*vy + vz*(qx - qy))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*vx*(-dx*qy*(2*qy*qy + 2*qz*qz - 1) + 2*dy*qx*(qw*qz - qx*qy) + 2*dy*qz*(qw*qx + qy*qz) + dz*qy*(2*qx*qx + 2*qy*qy - 1)) + 2*vy*(2*dx*qy*(qw*qz + qx*qy) + 2*dx*qz*(qw*qy - qx*qz) + dy*qx*(2*qx*qx + 2*qz*qz - 1) - dz*qx*(2*qx*qx + 2*qy*qy - 1)) - 8*vz*(dx*qy*(qw*qy - qx*qz) + dy*qx*(qw*qx + qy*qz));
        gx(VZ, QX) = -4*kh*(2*qx*qx + 2*qy*qy - 1)*(qy*vx - vy*(2*qx - qy) + vz*(qw + qz))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) - 4*qx*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) + 2*vx*(dx*qz*(2*qy*qy + 2*qz*qz - 1) + 2*dy*qw*(qw*qz - qx*qy) - 2*dy*qy*(qw*qx + qy*qz) + 4*dz*qx*(qw*qy + qx*qz) + dz*qz*(2*qx*qx + 2*qy*qy - 1)) + 2*vy*(2*dx*qy*(qw*qy - qx*qz) - 2*dx*qz*(qw*qz + qx*qy) + dy*qw*(2*qx*qx + 2*qz*qz - 1) + 4*dy*qx*(qw*qx + qy*qz) - dz*qw*(2*qx*qx + 2*qy*qy - 1) - 4*dz*qx*(qw*qx - qy*qz)) - 8*vz*(-dx*qz*(qw*qy - qx*qz) + dy*qw*(qw*qx + qy*qz) + dz*qx*(2*qx*qx + 2*qy*qy - 1));
        gx(VZ, QY) = -4*kh*(2*qx*qx + 2*qy*qy - 1)*(qx*vy + vx*(qx - 2*qy) - vz*(qw - qz))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) - 4*qy*(kh*pow(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz), 2) + thrust) - 2*vx*(dx*qw*(2*qy*qy + 2*qz*qz - 1) + 4*dx*qy*(qw*qy - qx*qz) + 2*dy*qx*(qw*qx + qy*qz) - 2*dy*qz*(qw*qz - qx*qy) - dz*qw*(2*qx*qx + 2*qy*qy - 1) - 4*dz*qy*(qw*qy + qx*qz)) + 2*vy*(2*dx*qw*(qw*qz + qx*qy) + 2*dx*qx*(qw*qy - qx*qz) + dy*qz*(2*qx*qx + 2*qz*qz - 1) - 4*dz*qy*(qw*qx - qy*qz) + dz*qz*(2*qx*qx + 2*qy*qy - 1)) - 8*vz*(dx*qw*(qw*qy - qx*qz) + dy*qz*(qw*qx + qy*qz) + dz*qy*(2*qx*qx + 2*qy*qy - 1));
        gx(VZ, QZ) = -4*kh*(2*qx*qx + 2*qy*qy - 1)*(-vx*(qw + 2*qz) + vy*(qw - 2*qz) + vz*(qx + qy))*(-vx*(2*qw*qz - 2*qx*qy + 2*qy*qy + 2*qz*qz - 1) + vy*(2*qw*qz - 2*qx*qx + 2*qx*qy - 2*qz*qz + 1) + 2*vz*(qw*qx - qw*qy + qx*qz + qy*qz)) + 2*vx*(dx*qx*(2*qy*qy + 2*qz*qz - 1) - 4*dx*qz*(qw*qy - qx*qz) + 2*dy*qw*(qw*qx + qy*qz) + 2*dy*qy*(qw*qz - qx*qy) + dz*qx*(2*qx*qx + 2*qy*qy - 1)) + 2*vy*(2*dx*qw*(qw*qy - qx*qz) - 2*dx*qx*(qw*qz + qx*qy) + dy*qy*(2*qx*qx + 2*qz*qz - 1) + 4*dy*qz*(qw*qx + qy*qz) + dz*qy*(2*qx*qx + 2*qy*qy - 1)) + 8*vz*(dx*qx*(qw*qy - qx*qz) - dy*qy*(qw*qx + qy*qz));
        gx(QW, QX) = 0.5 * -rx, gx(QW, QY) = 0.5 * -ry, gx(QW, QZ) = 0.5 * -rz;
        gx(QX, QW) = 0.5 * rx, gx(QX, QY) = 0.5 * rz, gx(QX, QZ) = 0.5 * -ry;
        gx(QY, QW) = 0.5 * ry, gx(QY, QX) = 0.5 * -rz, gx(QY, QZ) = 0.5 * rx;
        gx(QZ, QW) = 0.5 * rz, gx(QZ, QX) = 0.5 * ry, gx(QZ, QY) = 0.5 * -rx;
        gu(VX, TH) = (2*qw*qy + 2*qx*qz) * 9.79362 / hover_ratio_;
        gu(VY, TH) = (-2*qw*qx + 2*qy*qz) * 9.79362 / hover_ratio_;
        gu(VZ, TH) = (-2*qx*qx - 2*qy*qy + 1) * 9.79362 / hover_ratio_;
        gu(QW, RX) = 0.5 * -qx, gu(QW, RY) = 0.5 * -qy, gu(QW, RZ) = 0.5 * -qz;
        gu(QX, RX) = 0.5 * qw, gu(QX, RY) = 0.5 * -qz, gu(QX, RZ) = 0.5 * qy;
        gu(QY, RX) = 0.5 * qz, gu(QY, RY) = 0.5 * qw, gu(QY, RZ) = 0.5 * -qx;
        gu(QZ, RX) = 0.5 * -qy, gu(QZ, RY) = 0.5 * qx, gu(QZ, RZ) = 0.5 * qw;
    }

    void xdot_func(const Matrix<double, x_dim_, 1> &x, const VectorXd &u, const Vector3d &disturbance_acc,
        Matrix<double, x_dim_, 1> &xdot) {
        const double &px = x(0);
        const double &py = x(1);
        const double &pz = x(2);
        const double &vx = x(3);
        const double &vy = x(4);
        const double &vz = x(5);
        const double &qw = x(6);
        const double &qx = x(7);
        const double &qy = x(8);
        const double &qz = x(9);
        const double &rx = u[0];
        const double &ry = u[1];
        const double &rz = u[2];
        const double thrust = /*1.084e-5 * pow(u[3] * 1000 + 100, 2) * 4 / 0.74;*/u[3] * 9.79362 / hover_ratio_;
        xdot << vx, vy, vz, 
                2 * (qx * qz + qw * qy) * thrust + disturbance_acc.x(), 2 * (qy * qz - qw * qx) * thrust + disturbance_acc.y(), ((1 - 2 * (qx * qx + qy * qy)) * thrust - 9.79362 + disturbance_acc.z()),
                0.5 * (-rx * qx - ry * qy - rz * qz), 0.5 * (rx * qw + rz * qy - ry * qz), 0.5 * (ry * qw - rz * qx + rx * qz), 0.5 * (rz * qw + ry * qx - rx * qy);
    }

    //x1 = f_rk4(x0, u, dt), gx0 = dx1 / dx0, gu = dx1 / du
    void rk4_func(const Matrix<double, x_dim_, 1> &x0, const VectorXd &u, const Vector3d &disturbance_acc,
        const double &dt, Matrix<double, x_dim_, 1> &x1, 
        Matrix<double, x_dim_, x_dim_> &gx0, Matrix<double, x_dim_, u_dim_> &gu,
        Matrix<double, 3, 1> &acc, Matrix<double, 3, x_dim_> &accdotx0, Matrix<double, 3, u_dim_> &accdotu) {
        xdot_func(x0, u, disturbance_acc, xdot1, xd1gx0, xd1gu);
        acc = xdot1.block(3, 0, 3, 1);
        accdotx0 = xd1gx0.block(3, 0, 3, x_dim_);
        accdotu = xd1gu.block(3, 0, 3, u_dim_);
        aux = x0 + xdot1 * dt / 2.0;
        x1gx0 = iden + dt * 1 / 2.0 * xd1gx0;
        x1gu = dt / 2.0 * xd1gu;
        xdot_func(aux, u, disturbance_acc, xdot2, xd2gx1, xd2gu);
        aux = x0 + xdot2 * dt / 2.0;
        x2gx0 = iden + dt * 1 / 2.0 * xd2gx1 * x1gx0;
        x2gu = dt / 2.0 * (xd2gx1 * x1gu + xd2gu);
        xdot_func(aux, u, disturbance_acc, xdot3, xd3gx2, xd3gu);
        aux = x0 + xdot3 * dt;
        x3gx0 = iden + dt * xd3gx2 * x2gx0;
        x3gu = dt * (xd3gx2 * x2gu + xd3gu);
        xdot_func(aux, u, disturbance_acc, xdot4, xd4gx3, xd4gu);
        x1 = x0 + dt * (1 / 6. * xdot1 + 1 / 3. * xdot2 + 1 / 3. * xdot3 + 1 / 6. * xdot4);
        
        gx0 = iden + 
            dt * (1 / 6.0 * xd1gx0
            + 1 / 3.0 * xd2gx1 * x1gx0
            + 1 / 3.0 * xd3gx2 * x2gx0
            + 1 / 6.0 * xd4gx3 * x3gx0
            );
        gu = dt * (1 / 6.0 * xd1gu + 1 / 3.0 * (xd2gu + xd2gx1 * x1gu) + 1 / 3.0 * (xd3gu + xd3gx2 * x2gu) + 1 / 6.0 * (xd4gu + xd4gx3 * x3gu));
    }

    void rk4_func(const Matrix<double, x_dim_, 1> &x0, const VectorXd &u, const Vector3d &disturbance_acc,
        const double &dt, Matrix<double, x_dim_, 1> &x1) {
        xdot_func(x0, u, disturbance_acc, xdot1);
        aux = x0 + xdot1 * (dt / 2.0);
        xdot_func(aux, u, disturbance_acc, xdot2);
        aux = x0 + xdot2 * (dt / 2.0);
        xdot_func(aux, u, disturbance_acc, xdot3);
        aux = x0 + xdot3 * dt;
        xdot_func(aux, u, disturbance_acc, xdot4);
        x1 = x0 + (dt / 6.0) * (xdot1 + 2 * xdot2 + 2 * xdot3 + xdot4);
    }
};

#endif