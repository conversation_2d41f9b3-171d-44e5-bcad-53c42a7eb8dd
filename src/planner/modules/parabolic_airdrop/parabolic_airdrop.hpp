#pragma once

#include <iostream>
#include <cmath>
#include <vector>
#include <algorithm>
#include <Eigen/Eigen>
#include <Eigen/Dense>
#include "map/map.hpp"
#include "map/sdf.hpp"
#include "MRPT/Mrpt.h"


using namespace std;
using namespace Eigen;

class ParabolicAirdrop
{
  public:
    ParabolicAirdrop(const SdfMap &sdf,const Vector3d &target_pos,
                    const vector<Vector3d> &opt_bsp_pos, const vector<Vector3d> &opt_bsp_vel, double v_max,
                    double lambda_d, double lambda_v, double payload_offset);
    bool getoptimalpoint();
    Vector3d best_pos, best_vel, target_pos_;
    MatrixXd parabola_;

  private:
    SdfMap sdfmap_;
    vector<Vector3d> opt_bsp_pos_, opt_bsp_vel_;
    double psi_min_, psi_max_, max_z_, v_max_, payload_offset_, lambda_d_, lambda_v_;
    MatrixXd constructParabola(double z, double theta,
                               double v, double duration, double g);
    MatrixXd transformParabola(MatrixXd parabola, VectorXd target,
                               double psi, double dp);
    bool isvalid(MatrixXd parabola);
    MatrixXf vectorToMatrixXf(const vector<Vector3d> &vec3d);
    VectorXf Vector3dToVectorXf(const Eigen::Vector3d& vec3d);
    double vectorsimilarity(const Vector3d &vec1, const Vector3d &vec2);
    Vector3d offsetvector(const double &roll, const double &pitch, double payload_offset);
};

inline double deg2rad(double deg);
inline double chrono2duration(chrono::steady_clock::time_point begin, chrono::steady_clock::time_point end);
inline chrono::steady_clock::time_point tick();
