#include "parabolic_airdrop.hpp"

ParabolicAirdrop::ParabolicAirdrop(const SdfMap &sdf,const Vector3d &target_pos,
                const vector<Vector3d> &opt_bsp_pos, const vector<Vector3d> &opt_bsp_vel, double v_max,
                double lambda_d, double lambda_v, double payload_offset): sdfmap_(sdf)
{
    max_z_ = sdf.map_size_[2];
    opt_bsp_pos_ = opt_bsp_pos;
    opt_bsp_vel_ = opt_bsp_vel;
    target_pos_ = target_pos;
    lambda_d_ = lambda_d;
    lambda_v_ = lambda_v;
    v_max_ = v_max;
    best_pos = Vector3d(0.0, 0.0, 0.0);
    best_vel = Vector3d(0.0, 0.0, 0.0);
    payload_offset_ = payload_offset;
}

bool ParabolicAirdrop::getoptimalpoint()
{   
    double g = 9.79362;
    VectorXi indices_exact(1);
    MatrixXf Opt_bsp_pos = vectorToMatrixXf(opt_bsp_pos_).transpose();  // 注意要转置才行
    double sim_min = numeric_limits<double>::max();
    for (double v = 3.0; v < v_max_; v += 0.05){    // 参数设置要合理，对实际的场景要做调整。
        for (double theta = deg2rad(0); theta < deg2rad(35); theta +=  deg2rad(1)){
            for (double h = 1.8; h < 2.2; h +=  0.05){   
              double t = (v * sin(theta) + sqrt((v * sin(theta)) * (v * sin(theta)) + 2 * g * h)) / g;
              double d = v * cos(theta) * t;
              MatrixXd parabola = constructParabola(h, theta, v, t, g);
              for (double psi = deg2rad(0); psi <= deg2rad(360); psi += deg2rad(5)){
                  MatrixXd transformed_parabola = transformParabola(parabola, target_pos_, psi, d);
                  if(!isvalid(transformed_parabola)) continue;
                  Vector3d throw_pos = Vector3d(transformed_parabola(0,0), transformed_parabola(0,1), transformed_parabola(0,2) + payload_offset_);
                  Vector3d throw_vel = Vector3d(v * cos(theta) * cos(psi), v * cos(theta) * sin(psi), v * sin(theta));
                  // double rpy = acc2rpy(); 离散加速度，不离散roll和pitch，yaw用arctan(vy/vx)表示
                  // Vector3d offset = offsetvector(roll , pitch, payload_offset_)
                  MatrixXf Throw_pos_ = Vector3dToVectorXf(throw_pos);
                  Mrpt::exact_knn(Throw_pos_, Opt_bsp_pos, 1, indices_exact.data());
                  int opt_index = indices_exact(0);
                  Vector3d opt_pos = opt_bsp_pos_[opt_index];
                  Vector3d opt_vel = opt_bsp_vel_[opt_index];
                  double sim = lambda_d_ * vectorsimilarity(10 * opt_pos.normalized(), 10 * throw_pos / opt_pos.norm()) + 
                                lambda_v_ * vectorsimilarity(10 * opt_vel.normalized(), 10 * throw_vel  / opt_vel.norm());
                  if (sim < sim_min) {
                      sim_min = sim;
                      best_pos = throw_pos;
                      best_vel = throw_vel;
                      parabola_ = transformed_parabola;
                  }
              }
            }
        }
    }
    if(best_pos.isZero() || best_vel.isZero()){
        cerr << "Invalid parameter settings" << endl;
        return false;
    }else{
        return true;
    }
}

inline double ParabolicAirdrop::vectorsimilarity(const Vector3d &vecA, const Vector3d &vecB)
{
    double dotAB = vecA.dot(vecB);
    double mag_A = vecA.norm();
    double mag_B = vecB.norm();
    double cosineTheta = dotAB / (mag_A * mag_B);
    cosineTheta = max(-1.0, std::min(1.0, cosineTheta));
    double angle = acos(cosineTheta);
    double TS = 0.5 * mag_A * mag_B * sin(angle);
    double MD = abs(mag_A - mag_B);
    double ED = (vecA - vecB).norm();
    double SS = 0.5 * angle * pow(MD + ED, 2);
    return TS * SS;
}

inline Vector3d ParabolicAirdrop::offsetvector(const double &roll_, const double &pitch_, double payload_offset)
{
    Matrix3d R_x;
    R_x << 1,          0,            0,
           0, cos(roll_), - sin(roll_),
           0, sin(roll_),   cos(roll_);
    Matrix3d R_y;
    R_y <<   cos(pitch_), 0, sin(pitch_),
                       0, 1,           0,
           - sin(pitch_), 0, cos(pitch_);
    Matrix3d R = R_y * R_x;
    Vector3d vector(0, 0, payload_offset);
    return R * vector;
}

inline MatrixXd ParabolicAirdrop::constructParabola(double z, double theta, double v, double duration, double g)
{
  double ts = 0.02;  
  int n = round(duration/ts);  
  double t = 0.0;  
  MatrixXd parabola(n + 1, 3);  
  for (int i = 0; i <= n; i++){  
    parabola(i, 0) = v * cos(theta) * t;  
    parabola(i, 1) = 0.0;  
    parabola(i, 2) = v * sin(theta) * t - 0.5 * g * t * t + z; 
    t += ts;  
  }
  return parabola; 
}

inline MatrixXd ParabolicAirdrop::transformParabola(MatrixXd parabola, VectorXd target, double psi, double dp)  
{
  MatrixXd transformed_parabola(parabola.rows(), parabola.cols());
  double cy = cos(psi);
  double sy = sin(psi);

  for (int i = 0; i < parabola.rows(); i++) {
    transformed_parabola(i, 0) = target(0) - dp * cy + parabola(i, 0) * cy - parabola(i, 1) * sy;
    transformed_parabola(i, 1) = target(1) - dp * sy + parabola(i, 0) * sy + parabola(i, 1) * cy;
    transformed_parabola(i, 2) = target(2) + parabola(i, 2);
  }
  return transformed_parabola;
}

inline bool ParabolicAirdrop::isvalid(MatrixXd parabola)
{
  bool is_valid = true;
  for (int i = 0; i < parabola.rows(); i++){
    if(parabola(i, 2) > 0.05){
      is_valid &=(sdfmap_.get_dist_with_grad_trilinear(Vector3d((parabola.row(i)))).first > 0.05);
     }
  }
  return is_valid;
}

inline MatrixXf ParabolicAirdrop::vectorToMatrixXf(const vector<Vector3d> &vec3d)
{
    int numRows = vec3d.size();   
    int numCols = 3;             
    Eigen::MatrixXf matrix(numRows, numCols);
    for (int i = 0; i < numRows; ++i) {
        matrix.row(i) = vec3d[i].cast<float>(); 
    }
    return matrix;  // 必须要转置。
}

inline VectorXf ParabolicAirdrop::Vector3dToVectorXf(const Eigen::Vector3d& vec3d)
{
    Eigen::VectorXf vecXf(vec3d.size());
    for (int i = 0; i < vec3d.size(); ++i) {
        vecXf(i) = static_cast<float>(vec3d(i));
    }
    return vecXf;
}

inline double deg2rad(double deg)
{
  return deg * M_PI / 180.0;
}

inline double chrono2duration(chrono::steady_clock::time_point begin, chrono::steady_clock::time_point end)
{
  return double(chrono::duration_cast<chrono::nanoseconds>(end - begin).count()) * 1e-9;
}

inline chrono::steady_clock::time_point tick()
{
  return chrono::steady_clock::now();
}
