#include <fstream>
#include <stdio.h>

#include "map.hpp"

bool GridMap::read_map_file(string path) {
    ifstream file;
    file.open(path);
    if (!file) {
        cerr << "Fail to open map file" << endl;
        throw "Fail to open map file";
        return false;
    }
    cylinders_.clear();
    rings_.clear();
    pipelines_.clear();
    rectangle_.clear();
    string s;
    while (getline(file, s)) {
        char keyc[25];
        sscanf(s.c_str(), "%s", keyc);
        string key(keyc);
        if (key == "map") {
            double v1, v2, v3, v4;
            sscanf(s.c_str() + key.size(), "%lf %lf %lf %lf", &v1, &v2, &v3, &v4);
            resolution_ = v1;
            size_ = Vector3d(v2, v3, v4);
            isize_ = Vector3i(round(size_(0) / resolution_), round(size_(1) / resolution_), round(size_(2) / resolution_));
            grid_map_.resize(isize_(0) * isize_(1) * isize_(2));
            for (int i = 0; i < grid_map_.size(); i++) {
                grid_map_[i] = false;
            }
        } else if (key == "ObsPipeline") {
            double v1, v2, v3, v4, v5, v6;
            sscanf(s.c_str() + key.size(), "%lf %lf %lf %lf %lf %lf", &v1, &v2, &v3, &v4, &v5, &v6);
            pipelines_.push_back(ObsPipeline(Vector3d(v1, v2, v3), v4, v5, v6));
        } else if (key == "ObsCylinder") {
            double v1, v2, v3, v4, v5;
            sscanf(s.c_str() + key.size(), "%lf %lf %lf %lf %lf", &v1, &v2, &v3, &v4, &v5);
            cylinders_.push_back(ObsCylinder(Vector3d(v1, v2, v3), v4, v5));
        } else if (key == "ObsRing") {
            double v1, v2, v3, v4, v5, v6;
            sscanf(s.c_str() + key.size(), "%lf %lf %lf %lf %lf %lf", &v1, &v2, &v3, &v4, &v5, &v6);
            rings_.push_back(ObsRing(Vector3d(v1, v2, v3), v4, v5, v6));
        }else if (key == "ObsRectangle") {
            double v1, v2, v3, v4, v5, v6, v7;
            sscanf(s.c_str() + key.size(), "%lf %lf %lf %lf %lf %lf %lf", &v1, &v2, &v3, &v4, &v5, &v6 ,&v7);
            rectangle_.push_back(ObsRectangle(Vector3d(v1, v2, v3), v4, v5, v6, v7));
        } else {
            throw "Fail to recognize " + key;
            return false;
        }
    }

    return true;
}

void GridMap::update_grid_map() {
    grid_map_.resize(isize_(0) * isize_(1) * isize_(2));
    for (int i = 0; i < grid_map_.size(); i++) {
        grid_map_[i] = false;
    }
    for (int i = 0; i < cylinders_.size(); i++) {
        const auto &cyl = cylinders_[i];
        for (double r = resolution_ / 2.0; r < cyl.radius_; r += resolution_ / 2.0) {
            for (double ang = 0; ang < M_PI * 2; ang += M_PI / 200) {
                for (double h = 0.0; h < cyl.high_; h += resolution_ / 2.0) {
                    Vector3d pos = cyl.pos_;
                    pos(0) += r * sin(ang);
                    pos(1) += r * cos(ang);
                    pos(2) += h;
                    set_grid(pos, true);
                }
            }
        }
    }

    for (int i = 0; i < rectangle_.size(); i++) {
        const auto &rectangle = rectangle_[i];
        for (double l = - rectangle.length_ / 2.0; l < rectangle.length_ / 2.0; l += resolution_ / 2.0) {
            for (double w = - rectangle.width_ / 2.0; w< rectangle.width_ / 2.0; w += resolution_ / 2.0) {
                for (double h = 0.0; h < rectangle.high_; h += resolution_ / 2.0) {
                    Vector3d pos = rectangle.pos_;
                    pos(0) += l * cos(rectangle.yaw_) - w * sin(rectangle.yaw_);
                    pos(1) += l * sin(rectangle.yaw_) + w * cos(rectangle.yaw_);
                    pos(2) += h;
                    set_grid(pos, true);
                }
            }
        }
    }

    for (int i = 0; i < pipelines_.size(); i++) {
        const auto &pipeline = pipelines_[i];
        for (double l = - pipeline.length_ / 2; l < pipeline.length_ / 2; l += resolution_ / 2.0) {
            for (double ang = 0; ang < M_PI * 2; ang += M_PI / 200) {
                for (double r = 0; r < 0.1; r += resolution_ / 2.0) {
                    Vector3d pos = pipeline.pos_;
                    pos(0) += - (pipeline.radius_  + r )* cos(ang) * sin(pipeline.yaw_) + l * cos(pipeline.yaw_);
                    pos(1) += (pipeline.radius_  + r ) * cos(ang) * cos(pipeline.yaw_) + l * sin(pipeline.yaw_);
                    pos(2) += (pipeline.radius_  + r ) * sin(ang);
                    set_grid(pos, true);
                }
            }
        }
    }

    for (int i = 0; i < rings_.size(); i++) {
        const auto &ring = rings_[i];
        for (double r = ring.inner_r_; r < ring.inner_r_ + ring.wid_; r += resolution_ / 2.0) {
            for (double ang = 0; ang < M_PI * 2; ang += M_PI / 200) {
                for (double l = - 0.05; l < 0.05; l += resolution_ / 2.0) {
                    Vector3d pos = ring.pos_;
                    pos(0) += - r * cos(ang) * sin(ring.yaw_) + l * cos(ring.yaw_);
                    pos(1) += r * cos(ang) * cos(ring.yaw_) + l * sin(ring.yaw_);
                    pos(2) += r * sin(ang);
                    set_grid(pos, true);
                }
            }
        }
    }

    for (int z = 0; z < isize_(2); z++) {
        for (int x = 0; x < isize_(0); x++) {
            for (int y = 0; y < isize_(1); y++) {
                if (x == 0 || x == isize_(0) - 1 || y == 0 || y == isize_(1) - 1 || z == 0 || z == isize_(2) - 1) {
                    set_grid(Vector3i(x, y, z), true);
                }
            }
        }
    }
}