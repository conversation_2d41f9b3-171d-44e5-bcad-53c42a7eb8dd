/*
    FILE: dynamicDetector.cpp
    ---------------------------------
    function implementation of dynamic osbtacle detector
*/
#include <onboard_detector/dynamicDetector.h>

namespace onboardDetector{
    dynamicDetector::dynamicDetector(){
        this->ns_ = "onboard_detector";
        this->hint_ = "[onboardDetector]";
    }

    dynamicDetector::dynamicDetector(const ros::NodeHandle& nh){
        this->ns_ = "onboard_detector";
        this->hint_ = "[onboardDetector]";
        this->nh_ = nh;
        this->initParam();
        this->registerPub();
        this->registerCallback();
    }

    void dynamicDetector::initDetector(const ros::NodeHandle& nh){
        this->nh_ = nh;
        this->initParam();
        this->registerPub();
        this->registerCallback();
    }

    void dynamicDetector::initParam() {
        // 初始化参数函数，用于从ROS参数服务器中获取参数并设置默认值
    
        // 获取定位模式参数
        if (not this->nh_.getParam(this->ns_ + "/localization_mode", this->localizationMode_)) {
            this->localizationMode_ = 0; // 如果未设置，默认使用模式0（pose模式）
            cout << this->hint_ << ": No localization mode option. Use default: pose" << endl;
        } else {
            cout << this->hint_ << ": Localization mode: pose (0)/odom (1). Your option: " << this->localizationMode_ << endl;
        }
    
        // 获取深度图像主题名称
        if (not this->nh_.getParam(this->ns_ + "/depth_image_topic", this->depthTopicName_)) {
            this->depthTopicName_ = "/camera/depth/image_raw"; // 默认主题名称
            cout << this->hint_ << ": No depth image topic name. Use default: /camera/depth/image_raw" << endl;
        } else {
            cout << this->hint_ << ": Depth topic: " << this->depthTopicName_ << endl;
        }
    
        // 获取对齐深度图像主题名称
        if (not this->nh_.getParam(this->ns_ + "/aligned_depth_image_topic", this->alignedDepthTopicName_)) {
            this->alignedDepthTopicName_ = "/camera/aligned_depth_to_color/image_raw"; // 默认主题名称
            cout << this->hint_ << ": No aligned depth image topic name. Use default: /camera/aligned_depth_to_color/image_raw" << endl;
        } else {
            cout << this->hint_ << ": Aligned depth topic: " << this->alignedDepthTopicName_ << endl;
        }
    
        // 获取彩色图像主题名称
        if (not this->nh_.getParam(this->ns_ + "/color_image_topic", this->colorImgTopicName_)) {
            this->colorImgTopicName_ = "/camera/color/image_raw"; // 默认主题名称
            cout << this->hint_ << ": No color image topic name. Use default: /camera/color/image_raw" << endl;
        } else {
            cout << this->hint_ << ": Color image topic: " << this->colorImgTopicName_ << endl;
        }
    
        // 根据定位模式获取对应的主题名称
        if (this->localizationMode_ == 0) {
            // 如果是pose模式，获取pose主题名称
            if (not this->nh_.getParam(this->ns_ + "/pose_topic", this->poseTopicName_)) {
                this->poseTopicName_ = "/CERLAB/quadcopter/pose"; // 默认主题名称
                cout << this->hint_ << ": No pose topic name. Use default: /CERLAB/quadcopter/pose" << endl;
            } else {
                cout << this->hint_ << ": Pose topic: " << this->poseTopicName_ << endl;
            }
        }
    
        if (this->localizationMode_ == 1) {
            // 如果是odom模式，获取odom主题名称
            if (not this->nh_.getParam(this->ns_ + "/odom_topic", this->odomTopicName_)) {
                this->odomTopicName_ = "/CERLAB/quadcopter/odom"; // 默认主题名称
                cout << this->hint_ << ": No odom topic name. Use default: /CERLAB/quadcopter/odom" << endl;
            } else {
                cout << this->hint_ << ": Odom topic: " << this->odomTopicName_ << endl;
            }
        }
    
        // 获取深度相机内参
        std::vector<double> depthIntrinsics(4);
        if (not this->nh_.getParam(this->ns_ + "/depth_intrinsics", depthIntrinsics)) {
            cout << this->hint_ << ": Please check camera intrinsics!" << endl;
            exit(0); // 如果未设置，直接退出程序
        } else {
            this->fx_ = depthIntrinsics[0]; // 焦距fx
            this->fy_ = depthIntrinsics[1]; // 焦距fy
            this->cx_ = depthIntrinsics[2]; // 主点cx
            this->cy_ = depthIntrinsics[3]; // 主点cy
            cout << this->hint_ << ": fx, fy, cx, cy: " << "[" << this->fx_ << ", " << this->fy_ << ", " << this->cx_ << ", " << this->cy_ << "]" << endl;
        }
    
        // 获取深度比例因子
        if (not this->nh_.getParam(this->ns_ + "/depth_scale_factor", this->depthScale_)) {
            this->depthScale_ = 1000.0; // 默认比例因子
            cout << this->hint_ << ": No depth scale factor. Use default: 1000." << endl;
        } else {
            cout << this->hint_ << ": Depth scale factor: " << this->depthScale_ << endl;
        }
    
        // 获取深度最小值
        if (not this->nh_.getParam(this->ns_ + "/depth_min_value", this->depthMinValue_)) {
            this->depthMinValue_ = 0.2; // 默认最小值
            cout << this->hint_ << ": No depth min value. Use default: 0.2 m." << endl;
        } else {
            cout << this->hint_ << ": Depth min value: " << this->depthMinValue_ << endl;
        }
    
        // 获取深度最大值
        if (not this->nh_.getParam(this->ns_ + "/depth_max_value", this->depthMaxValue_)) {
            this->depthMaxValue_ = 5.0; // 默认最大值
            cout << this->hint_ << ": No depth max value. Use default: 5.0 m." << endl;
        } else {
            cout << this->hint_ << ": Depth max value: " << this->depthMaxValue_ << endl;
        }
    
        // 获取深度滤波边界
        if (not this->nh_.getParam(this->ns_ + "/depth_filter_margin", this->depthFilterMargin_)) {
            this->depthFilterMargin_ = 0; // 默认边界值
            cout << this->hint_ << ": No depth filter margin. Use default: 0." << endl;
        } else {
            cout << this->hint_ << ": Depth filter margin: " << this->depthFilterMargin_ << endl;
        }
    
        // 获取跳过的像素数
        if (not this->nh_.getParam(this->ns_ + "/depth_skip_pixel", this->skipPixel_)) {
            this->skipPixel_ = 1; // 默认跳过1个像素
            cout << this->hint_ << ": No depth skip pixel. Use default: 1." << endl;
        } else {
            cout << this->hint_ << ": Depth skip pixel: " << this->skipPixel_ << endl;
        }
    
        // 获取深度图像的列数
        if (not this->nh_.getParam(this->ns_ + "/image_cols", this->imgCols_)) {
            this->imgCols_ = 640; // 默认列数
            cout << this->hint_ << ": No depth image columns. Use default: 640." << endl;
        } else {
            cout << this->hint_ << ": Depth image columns: " << this->imgCols_ << endl;
        }
    
        // 获取深度图像的行数
        if (not this->nh_.getParam(this->ns_ + "/image_rows", this->imgRows_)) {
            this->imgRows_ = 480; // 默认行数
            cout << this->hint_ << ": No depth image rows. Use default: 480." << endl;
        } else {
            cout << this->hint_ << ": Depth image rows: " << this->imgRows_ << endl;
        }
    
        // 根据图像分辨率和跳过像素数调整点云大小
        this->projPoints_.resize(this->imgCols_ * this->imgRows_ / (this->skipPixel_ * this->skipPixel_));
        this->pointsDepth_.resize(this->imgCols_ * this->imgRows_ / (this->skipPixel_ * this->skipPixel_));

        // 获取从机体坐标系到相机坐标系的变换矩阵
        std::vector<double> body2CamVec(16); // 定义一个大小为16的向量，用于存储4x4变换矩阵的元素
        if (not this->nh_.getParam(this->ns_ + "/body_to_camera", body2CamVec)) {
            ROS_ERROR("[dynamicDetector]: Please check body to camera matrix!"); // 如果参数未找到，输出错误信息
        } else {
            for (int i = 0; i < 4; ++i) { // 遍历矩阵的行
                for (int j = 0; j < 4; ++j) { // 遍历矩阵的列
                    this->body2Cam_(i, j) = body2CamVec[i * 4 + j]; // 将向量中的值赋值到4x4矩阵中
                }
            }
        }
        
        // 获取彩色相机的内参
        std::vector<double> colorIntrinsics(4); // 定义一个大小为4的向量，用于存储相机内参
        if (not this->nh_.getParam(this->ns_ + "/color_intrinsics", colorIntrinsics)) {
            cout << this->hint_ << ": Please check camera intrinsics!" << endl; // 如果参数未找到，输出错误信息
            exit(0); // 退出程序
        } else {
            this->fxC_ = colorIntrinsics[0]; // 设置彩色相机的焦距fx
            this->fyC_ = colorIntrinsics[1]; // 设置彩色相机的焦距fy
            this->cxC_ = colorIntrinsics[2]; // 设置彩色相机的主点cx
            this->cyC_ = colorIntrinsics[3]; // 设置彩色相机的主点cy
            cout << this->hint_ << ": fxC, fyC, cxC, cyC: " << "[" << this->fxC_ << ", " << this->fyC_ << ", " << this->cxC_ << ", " << this->cyC_ << "]" << endl;
        }
        
        // 获取从机体坐标系到彩色相机坐标系的变换矩阵
        std::vector<double> body2CamColorVec(16); // 定义一个大小为16的向量，用于存储4x4变换矩阵的元素
        if (not this->nh_.getParam(this->ns_ + "/body_to_camera_color", body2CamColorVec)) {
            ROS_ERROR("[dynamicDetector]: Please check body to camera color matrix!"); // 如果参数未找到，输出错误信息
        } else {
            for (int i = 0; i < 4; ++i) { // 遍历矩阵的行
                for (int j = 0; j < 4; ++j) { // 遍历矩阵的列
                    this->body2CamColor_(i, j) = body2CamColorVec[i * 4 + j]; // 将向量中的值赋值到4x4矩阵中
                }
            }
        }
        
        // 获取光线投射的最大长度
        if (not this->nh_.getParam(this->ns_ + "/raycast_max_length", this->raycastMaxLength_)) {
            this->raycastMaxLength_ = 5.0; // 如果参数未找到，设置默认值为5.0
            cout << this->hint_ << ": No raycast max length. Use default: 5.0." << endl;
        } else {
            cout << this->hint_ << ": Raycast max length: " << this->raycastMaxLength_ << endl;
        }
        
        // 获取体素滤波中体素被占据的最小点数
        if (not this->nh_.getParam(this->ns_ + "/voxel_occupied_thresh", this->voxelOccThresh_)) {
            this->voxelOccThresh_ = 10; // 如果参数未找到，设置默认值为10
            cout << this->hint_ << ": No voxel_occupied_threshold. Use default: 10." << endl;
        } else {
            cout << this->hint_ << ": min num of points for a voxel to be occupied in voxel filter is set to be: " << this->voxelOccThresh_ << endl;
        }
        
        // 获取地面高度参数
        if (not this->nh_.getParam(this->ns_ + "/ground_height", this->groundHeight_)) {
            this->groundHeight_ = 0.1; // 如果参数未找到，设置默认值为0.1
            std::cout << this->hint_ << ": No ground height parameter. Use default: 0.1m." << std::endl;
        } else {
            std::cout << this->hint_ << ": Ground height is set to: " << this->groundHeight_ << std::endl;
        }
        
        // 获取DBSCAN算法中每个聚类的最小点数
        if (not this->nh_.getParam(this->ns_ + "/dbscan_min_points_cluster", this->dbMinPointsCluster_)) {
            this->dbMinPointsCluster_ = 18; // 如果参数未找到，设置默认值为18
            cout << this->hint_ << ": No DBSCAN minimum point in each cluster parameter. Use default: 18." << endl;
        } else {
            cout << this->hint_ << ": DBSCAN Minimum point in each cluster is set to: " << this->dbMinPointsCluster_ << endl;
        }
        
        // 获取DBSCAN算法的搜索范围
        if (not this->nh_.getParam(this->ns_ + "/dbscan_search_range_epsilon", this->dbEpsilon_)) {
            this->dbEpsilon_ = 0.3; // 如果参数未找到，设置默认值为0.3
            cout << this->hint_ << ": No DBSCAN epsilon parameter. Use default: 0.3." << endl;
        } else {
            cout << this->hint_ << ": DBSCAN epsilon is set to: " << this->dbEpsilon_ << endl;
        }
        
        // 获取用于过滤的边界框IOU阈值
        if (not this->nh_.getParam(this->ns_ + "/filtering_BBox_IOU_threshold", this->boxIOUThresh_)) {
            this->boxIOUThresh_ = 0.5; // 如果参数未找到，设置默认值为0.5
            cout << this->hint_ << ": No threshold for bounding box IOU filtering parameter found. Use default: 0.5." << endl;
        } else {
            cout << this->hint_ << ": The threshold for bounding box IOU filtering is set to: " << this->boxIOUThresh_ << endl;
        }
        
        // 获取YOLO覆盖距离的阈值
        if (not this->nh_.getParam(this->ns_ + "/yolo_overwrite_distance", this->yoloOverwriteDistance_)) {
            this->yoloOverwriteDistance_ = 3.5; // 如果参数未找到，设置默认值为3.5
            cout << this->hint_ << ": No threshold for YOLO overwrite distance. Use default: 3.5m." << endl;
        } else {
            cout << this->hint_ << ": The YOLO overwrite distance is set to: " << this->yoloOverwriteDistance_ << endl;
        }
        
        // 获取跟踪历史的大小
        if (not this->nh_.getParam(this->ns_ + "/history_size", this->histSize_)) {
            this->histSize_ = 5; // 如果参数未找到，设置默认值为5
            std::cout << this->hint_ << ": No tracking history size parameter found. Use default: 5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The history for tracking is set to: " << this->histSize_ << std::endl;
        }

        // 获取预测大小参数
        if (not this->nh_.getParam(this->ns_ + "/prediction_size", this->predSize_)) {
            this->predSize_ = 5;  // 如果未找到参数，设置默认预测大小为5
            std::cout << this->hint_ << ": No prediction size parameter found. Use default: 5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The prediction size is set to: " << this->predSize_ << std::endl;
        }
        
        // 获取时间间隔参数
        if (not this->nh_.getParam(this->ns_ + "/time_difference", this->dt_)) {
            this->dt_ = 0.033;  // 如果未找到参数，设置默认时间间隔为0.033秒(约30Hz)
            std::cout << this->hint_ << ": No time difference parameter found. Use default: 0.033." << std::endl;
        } else {
            std::cout << this->hint_ << ": The time difference for the system is set to: " << this->dt_ << std::endl;
        }
        
        // 获取数据关联相似度阈值
        if (not this->nh_.getParam(this->ns_ + "/similarity_threshold", this->simThresh_)) {
            this->simThresh_ = 0.9;  // 如果未找到参数，设置默认相似度阈值为0.9
            std::cout << this->hint_ << ": No similarity threshold parameter found. Use default: 0.9." << std::endl;
        } else {
            std::cout << this->hint_ << ": The similarity threshold for data association is set to: " << this->simThresh_ << std::endl;
        }
        
        // 获取重新跟踪的相似度阈值
        if (not this->nh_.getParam(this->ns_ + "/retrack_similarity_threshold", this->simThreshRetrack_)) {
            this->simThreshRetrack_ = 0.5;  // 如果未找到参数，设置默认重新跟踪阈值为0.5
            std::cout << this->hint_ << ": No similarity threshold parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The similarity threshold for data association is set to: " << this->simThreshRetrack_ << std::endl;
        }
        
        // 获取帧跳过参数
        if (not this->nh_.getParam(this->ns_ + "/frame_skip", this->skipFrame_)) {
            this->skipFrame_ = 5;  // 如果未找到参数，设置默认跳过帧数为5
            std::cout << this->hint_ << ": No skip frame parameter found. Use default: 5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The frames skiped in classification when comparing two point cloud is set to: " << this->skipFrame_ << std::endl;
        }
        
        // 获取动态分类的速度阈值
        if (not this->nh_.getParam(this->ns_ + "/dynamic_velocity_threshold", this->dynaVelThresh_)) {
            this->dynaVelThresh_ = 0.35;  // 如果未找到参数，设置默认速度阈值为0.35 m/s
            std::cout << this->hint_ << ": No dynamic velocity threshold parameter found. Use default: 0.35." << std::endl;
        } else {
            std::cout << this->hint_ << ": The velocity threshold for dynamic classification is set to: " << this->dynaVelThresh_ << std::endl;
        }
        
        // 获取动态分类的投票阈值
        if (not this->nh_.getParam(this->ns_ + "/dynamic_voting_threshold", this->dynaVoteThresh_)) {
            this->dynaVoteThresh_ = 0.8;  // 如果未找到参数，设置默认投票阈值为0.8
            std::cout << this->hint_ << ": No dynamic velocity threshold parameter found. Use default: 0.8." << std::endl;
        } else {
            std::cout << this->hint_ << ": The voting threshold for dynamic classification is set to: " << this->dynaVoteThresh_ << std::endl;
        }
        
        // 获取跳过点的最大比例(由于点超出先前FOV而被跳过)
        if (not this->nh_.getParam(this->ns_ + "/maximum_skip_ratio", this->maxSkipRatio_)) {
            this->maxSkipRatio_ = 0.5;  // 如果未找到参数，设置默认最大跳过比例为0.5
            std::cout << this->hint_ << ": No maximum_skip_ratio parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The the upper limit of points skipping in classification is set to: " << this->maxSkipRatio_ << std::endl;
        }
        
        // 获取用于固定边界框大小的历史阈值
        if (not this->nh_.getParam(this->ns_ + "/fix_size_history_threshold", this->fixSizeHistThresh_)) {
            this->fixSizeHistThresh_ = 10;  // 如果未找到参数，设置默认历史阈值为10
            std::cout << this->hint_ << ": No history threshold for fixing size parameter found. Use default: 10." << std::endl;
        } else {
            std::cout << this->hint_ << ": History threshold for fixing size parameter is set to: " << this->fixSizeHistThresh_ << std::endl;
        }
        
        // 获取用于固定边界框大小的维度阈值
        if (not this->nh_.getParam(this->ns_ + "/fix_size_dimension_threshold", this->fixSizeDimThresh_)) {
            this->fixSizeDimThresh_ = 0.4;  // 如果未找到参数，设置默认维度阈值为0.4
            std::cout << this->hint_ << ": No dimension threshold for fixing size parameter found. Use default: 0.4." << std::endl;
        } else {
            std::cout << this->hint_ << ": Dimension threshold for fixing size parameter is set to: " << this->fixSizeDimThresh_ << std::endl;
        }
        
        // 获取卡尔曼滤波器的协方差参数
        if (not this->nh_.getParam(this->ns_ + "/e_p", this->eP_)) {
            this->eP_ = 0.5;  // 如果未找到参数，设置默认协方差为0.5
            std::cout << this->hint_ << ": No covariance parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The covariance for kalman filter is set to: " << this->eP_ << std::endl;
        }

        // 获取卡尔曼滤波器中位置预测的噪声参数
        if (not this->nh_.getParam(this->ns_ + "/e_q_pos", this->eQPos_)) {
            this->eQPos_ = 0.5; // 如果未找到参数，则将位置预测噪声设置为默认值0.5
            std::cout << this->hint_ << ": No motion model uncertainty matrix for position parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The noise for prediction for position in Kalman Filter is set to: " << this->eQPos_ << std::endl;
        }
        
        // 获取卡尔曼滤波器中速度预测的噪声参数
        if (not this->nh_.getParam(this->ns_ + "/e_q_vel", this->eQVel_)) {
            this->eQVel_ = 0.5; // 如果未找到参数，则将速度预测噪声设置为默认值0.5
            std::cout << this->hint_ << ": No motion model uncertainty matrix for velocity parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The noise for prediction for velocity in Kalman Filter is set to: " << this->eQVel_ << std::endl;
        }
        
        // 获取卡尔曼滤波器中加速度预测的噪声参数
        if (not this->nh_.getParam(this->ns_ + "/e_q_acc", this->eQAcc_)) {
            this->eQAcc_ = 0.5; // 如果未找到参数，则将加速度预测噪声设置为默认值0.5
            std::cout << this->hint_ << ": No motion model uncertainty matrix for acceleration parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The noise for prediction for acceleration in Kalman Filter is set to: " << this->eQAcc_ << std::endl;
        }
        
        // 获取卡尔曼滤波器中位置测量的噪声参数
        if (not this->nh_.getParam(this->ns_ + "/e_r_pos", this->eRPos_)) {
            this->eRPos_ = 0.5; // 如果未找到参数，则将位置测量噪声设置为默认值0.5
            std::cout << this->hint_ << ": No measurement uncertainty matrix for position parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The noise for measurement for position in Kalman Filter is set to: " << this->eRPos_ << std::endl;
        }
        
        // 获取卡尔曼滤波器中速度测量的噪声参数
        if (not this->nh_.getParam(this->ns_ + "/e_r_vel", this->eRVel_)) {
            this->eRVel_ = 0.5; // 如果未找到参数，则将速度测量噪声设置为默认值0.5
            std::cout << this->hint_ << ": No measurement uncertainty matrix for velocity parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The noise for measurement for velocity in Kalman Filter is set to: " << this->eRVel_ << std::endl;
        }
        
        // 获取卡尔曼滤波器中加速度测量的噪声参数
        if (not this->nh_.getParam(this->ns_ + "/e_r_acc", this->eRAcc_)) {
            this->eRAcc_ = 0.5; // 如果未找到参数，则将加速度测量噪声设置为默认值0.5
            std::cout << this->hint_ << ": No measurement uncertainty matrix for acceleration parameter found. Use default: 0.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The noise for measurement for acceleration in Kalman Filter is set to: " << this->eRAcc_ << std::endl;
        }
        
        // 获取卡尔曼滤波器中用于观测的帧数
        if (not this->nh_.getParam(this->ns_ + "/kalman_filter_averaging_frames", this->kfAvgFrames_)) {
            this->kfAvgFrames_ = 10; // 如果未找到参数，则将观测帧数设置为默认值10
            std::cout << this->hint_ << ": No number of frames used in KF for observation parameter found. Use default: 10." << std::endl;
        } else {
            std::cout << this->hint_ << ": Number of frames used in KF for observation is set to: " << this->kfAvgFrames_ << std::endl;
        }
        
        // 获取强制动态检测的历史帧数
        if (not this->nh_.getParam(this->ns_ + "/frames_force_dynamic", this->forceDynaFrames_)) {
            this->forceDynaFrames_ = 20; // 如果未找到参数，则将强制动态检测帧数设置为默认值20
            std::cout << this->hint_ << ": No range of searching dynamic obstacles in box history found. Use default: 20." << std::endl;
        } else {
            std::cout << this->hint_ << ": Range of searching dynamic obstacles in box history is set to: " << this->forceDynaFrames_ << std::endl;
        }
        
        // 获取强制动态检测的检查范围
        if (not this->nh_.getParam(this->ns_ + "/frames_force_dynamic_check_range", this->forceDynaCheckRange_)) {
            this->forceDynaCheckRange_ = 30; // 如果未找到参数，则将检查范围设置为默认值30
            std::cout << this->hint_ << ": No threshold for forcing dynamic obstacles found. Use default: 30." << std::endl;
        } else {
            std::cout << this->hint_ << ": Threshold for forcing dynamic obstacles is set to: " << this->forceDynaCheckRange_ << std::endl;
        }
        
        // 获取动态一致性检查的阈值
        if (not this->nh_.getParam(this->ns_ + "/dynamic_consistency_threshold", this->dynamicConsistThresh_)) {
            this->dynamicConsistThresh_ = 3; // 如果未找到参数，则将动态一致性检查阈值设置为默认值3
            std::cout << this->hint_ << ": No threshold for dynamic-consistency check found. Use default: 3." << std::endl;
        } else {
            std::cout << this->hint_ << ": Threshold for dynamic consistency check is set to: " << this->dynamicConsistThresh_ << std::endl;
        }

        // 检查历史长度是否足够执行强制动态检测
        if (this->histSize_ < this->forceDynaCheckRange_ + 1) {
            ROS_ERROR("history length is too short to perform force-dynamic"); // 如果历史长度不足，输出错误信息
        }
        
        // 获取是否约束目标对象大小的参数
        if (not this->nh_.getParam(this->ns_ + "/constrain_size", this->constrainSize_)) {
            this->constrainSize_ = false; // 如果未找到参数，默认不约束目标对象大小
            std::cout << this->hint_ << ": No target object constrain size param found. Use default: false." << std::endl;
        } else {
            std::cout << this->hint_ << ": Target object constrain is set to: " << this->constrainSize_ << std::endl;
        }
        
        // 获取目标对象的大小参数
        std::vector<double> targetObjectSizeTemp; // 定义一个临时向量用于存储目标对象大小
        if (not this->nh_.getParam(this->ns_ + "/target_object_size", targetObjectSizeTemp)) {
            std::cout << this->hint_ << ": No target object size found. Do not apply target object size." << std::endl; // 如果未找到参数，输出提示信息
        } else {
            // 遍历目标对象大小参数，每三个值表示一个目标对象的尺寸
            for (size_t i = 0; i < targetObjectSizeTemp.size(); i += 3) {
                Eigen::Vector3d targetSize(targetObjectSizeTemp[i + 0], targetObjectSizeTemp[i + 1], targetObjectSizeTemp[i + 2]); // 创建一个三维向量表示目标对象大小
                this->targetObjectSize_.push_back(targetSize); // 将目标对象大小添加到成员变量中
                std::cout << this->hint_ << ": target object size is set to: [" << targetObjectSizeTemp[i + 0] << ", " << targetObjectSizeTemp[i + 1] << ", " << targetObjectSizeTemp[i + 2] << "]." << std::endl;
            }
        }
    }

     void dynamicDetector::registerPub() {
        image_transport::ImageTransport it(this->nh_); // 创建图像传输对象，用于发布图像消息
    
        // 发布UV检测器深度图
        this->uvDepthMapPub_ = it.advertise(this->ns_ + "/detected_depth_map", 1);
    
        // 发布UV检测器U深度图
        this->uDepthMapPub_ = it.advertise(this->ns_ + "/detected_u_depth_map", 1);
    
        // 发布UV检测器鸟瞰图
        this->uvBirdViewPub_ = it.advertise(this->ns_ + "/bird_view", 1);
    
        // 发布带有YOLO 2D边界框的对齐深度图
        this->detectedAlignedDepthImgPub_ = it.advertise(this->ns_ + "/detected_aligned_depth_map_yolo", 1);
    
        // 发布带有2D边界框的彩色图像
        this->detectedColorImgPub_ = it.advertise(this->ns_ + "/detected_color_image", 1);
    
        // 发布UV检测器的边界框
        this->uvBBoxesPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/uv_bboxes", 10);
    
        // 发布动态点云
        this->dynamicPointsPub_ = this->nh_.advertise<sensor_msgs::PointCloud2>(this->ns_ + "/dynamic_point_cloud", 10);
    
        // 发布过滤后的点云
        this->filteredPointsPub_ = this->nh_.advertise<sensor_msgs::PointCloud2>(this->ns_ + "/filtered_depth_cloud", 10);
    
        // 发布DBSCAN算法生成的边界框
        this->dbBBoxesPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/dbscan_bboxes", 10);
    
        // 发布YOLO生成的3D边界框
        this->yoloBBoxesPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/yolo_3d_bboxes", 10);
    
        // 发布过滤后的边界框
        this->filteredBBoxesPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/filtered_bboxes", 10);
    
        // 发布跟踪的边界框
        this->trackedBBoxesPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/tracked_bboxes", 10);
    
        // 发布动态边界框
        this->dynamicBBoxesPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/dynamic_bboxes", 10);
    
        // 发布历史轨迹
        this->historyTrajPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/history_trajectories", 10);
    
        // 发布速度可视化信息
        this->velVisPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/velocity_visualizaton", 10);
    }

     void dynamicDetector::registerCallback(){
        // 创建深度图像的订阅器，订阅深度图像消息
        this->depthSub_.reset(new message_filters::Subscriber<sensor_msgs::Image>(this->nh_, this->depthTopicName_, 50));
    
        // 根据定位模式选择订阅器和回调函数
        if (this->localizationMode_ == 0){
            // 如果定位模式为0，创建位姿订阅器并同步深度图像和位姿消息
            this->poseSub_.reset(new message_filters::Subscriber<geometry_msgs::PoseStamped>(this->nh_, this->poseTopicName_, 25));
            this->depthPoseSync_.reset(new message_filters::Synchronizer<depthPoseSync>(depthPoseSync(100), *this->depthSub_, *this->poseSub_));
            this->depthPoseSync_->registerCallback(boost::bind(&dynamicDetector::depthPoseCB, this, _1, _2));
        }
        else if (this->localizationMode_ == 1){
            // 如果定位模式为1，创建里程计订阅器并同步深度图像和里程计消息
            this->odomSub_.reset(new message_filters::Subscriber<nav_msgs::Odometry>(this->nh_, this->odomTopicName_, 25));
            this->depthOdomSync_.reset(new message_filters::Synchronizer<depthOdomSync>(depthOdomSync(100), *this->depthSub_, *this->odomSub_));
            this->depthOdomSync_->registerCallback(boost::bind(&dynamicDetector::depthOdomCB, this, _1, _2));
        }
        else{
            // 如果定位模式无效，输出错误信息并退出程序
            ROS_ERROR("[dynamicDetector]: Invalid localization mode!");
            exit(0);
        }
    
        // 订阅对齐深度图像的消息
        this->alignedDepthSub_ = this->nh_.subscribe(this->alignedDepthTopicName_, 10, &dynamicDetector::alignedDepthCB, this);
    
        // 订阅彩色图像的消息
        this->colorImgSub_ = this->nh_.subscribe(this->colorImgTopicName_, 10, &dynamicDetector::colorImgCB, this);
    
        // 订阅YOLO检测结果的消息
        this->yoloDetectionSub_ = this->nh_.subscribe("yolo_detector/detected_bounding_boxes", 10, &dynamicDetector::yoloDetectionCB, this);
    
        // ——————————————————————————————————————主循环函数————————————————————————————————————————————————
        // 创建检测定时器，定时调用检测回调函数
        this->detectionTimer_ = this->nh_.createTimer(ros::Duration(this->dt_), &dynamicDetector::detectionCB, this);
    
        // 创建跟踪定时器，定时调用跟踪回调函数
        this->trackingTimer_ = this->nh_.createTimer(ros::Duration(this->dt_), &dynamicDetector::trackingCB, this);
    
        // 创建分类定时器，定时调用分类回调函数
        this->classificationTimer_ = this->nh_.createTimer(ros::Duration(this->dt_), &dynamicDetector::classificationCB, this);
    
        // 创建可视化定时器，定时调用可视化回调函数
        this->visTimer_ = this->nh_.createTimer(ros::Duration(this->dt_), &dynamicDetector::visCB, this);
    }

    bool dynamicDetector::getDynamicObstacles(onboard_detector::GetDynamicObstacles::Request& req, 
                                              onboard_detector::GetDynamicObstacles::Response& res) {
        // 获取当前机器人位置
        Eigen::Vector3d currPos = Eigen::Vector3d(req.current_position.x, req.current_position.y, req.current_position.z);
    
        // 定义一个向量，用于存储障碍物及其距离
        std::vector<std::pair<double, onboardDetector::box3D>> obstaclesWithDistances;
    
        // 遍历所有动态障碍物，计算它们与机器人之间的距离
        for (const onboardDetector::box3D& bbox : this->dynamicBBoxes_) {
            Eigen::Vector3d obsPos(bbox.x, bbox.y, bbox.z); // 获取障碍物的位置
            Eigen::Vector3d diff = currPos - obsPos; // 计算机器人和障碍物之间的位移向量
            diff(2) = 0.; // 忽略z轴的高度差
            double distance = diff.norm(); // 计算欧几里得距离
            if (distance <= req.range) { // 如果距离在请求的范围内
                obstaclesWithDistances.push_back(std::make_pair(distance, bbox)); // 将障碍物及其距离存储到向量中
            }
        }
    
        // 按距离升序对障碍物进行排序
        std::sort(obstaclesWithDistances.begin(), obstaclesWithDistances.end(), 
                  [](const std::pair<double, onboardDetector::box3D>& a, const std::pair<double, onboardDetector::box3D>& b) {
                      return a.first < b.first;
                  });
    
        // 将排序后的障碍物信息填充到响应中
        for (const auto& item : obstaclesWithDistances) {
            const onboardDetector::box3D& bbox = item.second;
    
            geometry_msgs::Vector3 pos; // 定义位置向量
            geometry_msgs::Vector3 vel; // 定义速度向量
            geometry_msgs::Vector3 size; // 定义尺寸向量
    
            pos.x = bbox.x; // 设置障碍物的x坐标
            pos.y = bbox.y; // 设置障碍物的y坐标
            pos.z = bbox.z; // 设置障碍物的z坐标
    
            vel.x = bbox.Vx; // 设置障碍物的x方向速度
            vel.y = bbox.Vy; // 设置障碍物的y方向速度
            vel.z = 0.; // z方向速度默认为0
    
            size.x = bbox.x_width; // 设置障碍物的x方向宽度
            size.y = bbox.y_width; // 设置障碍物的y方向宽度
            size.z = bbox.z_width; // 设置障碍物的z方向宽度
    
            res.position.push_back(pos); // 将位置添加到响应中
            res.velocity.push_back(vel); // 将速度添加到响应中
            res.size.push_back(size); // 将尺寸添加到响应中
        }
    
        return true; // 返回成功
    }
    
    void dynamicDetector::depthPoseCB(const sensor_msgs::ImageConstPtr& img, const geometry_msgs::PoseStampedConstPtr& pose) {
        // 存储当前深度图像
        cv_bridge::CvImagePtr imgPtr = cv_bridge::toCvCopy(img, img->encoding); // 将ROS图像消息转换为OpenCV格式
        if (img->encoding == sensor_msgs::image_encodings::TYPE_32FC1) { // 如果图像编码为32位浮点型
            (imgPtr->image).convertTo(imgPtr->image, CV_16UC1, this->depthScale_); // 转换为16位无符号整型，并应用深度比例因子
        }
        imgPtr->image.copyTo(this->depthImage_); // 将深度图像复制到成员变量中
    
        // 存储当前相机的位置和方向
        Eigen::Matrix4d camPoseMatrix, camPoseColorMatrix; // 定义4x4变换矩阵
        this->getCameraPose(pose, camPoseMatrix, camPoseColorMatrix); // 获取相机的位姿矩阵
    
        this->position_(0) = camPoseMatrix(0, 3); // 提取相机的x坐标
        this->position_(1) = camPoseMatrix(1, 3); // 提取相机的y坐标
        this->position_(2) = camPoseMatrix(2, 3); // 提取相机的z坐标
        this->orientation_ = camPoseMatrix.block<3, 3>(0, 0); // 提取相机的旋转矩阵
    
        this->positionColor_(0) = camPoseColorMatrix(0, 3); // 提取彩色相机的x坐标
        this->positionColor_(1) = camPoseColorMatrix(1, 3); // 提取彩色相机的y坐标
        this->positionColor_(2) = camPoseColorMatrix(2, 3); // 提取彩色相机的z坐标
        this->orientationColor_ = camPoseColorMatrix.block<3, 3>(0, 0); // 提取彩色相机的旋转矩阵
    }
    
    void dynamicDetector::depthOdomCB(const sensor_msgs::ImageConstPtr& img, const nav_msgs::OdometryConstPtr& odom) {
        // 存储当前深度图像
        cv_bridge::CvImagePtr imgPtr = cv_bridge::toCvCopy(img, img->encoding); // 将ROS图像消息转换为OpenCV格式
        if (img->encoding == sensor_msgs::image_encodings::TYPE_32FC1) { // 如果图像编码为32位浮点型
            (imgPtr->image).convertTo(imgPtr->image, CV_16UC1, this->depthScale_); // 转换为16位无符号整型，并应用深度比例因子
        }
        imgPtr->image.copyTo(this->depthImage_); // 将深度图像复制到成员变量中
    
        // 存储当前相机的位置和方向
        Eigen::Matrix4d camPoseMatrix, camPoseColorMatrix; // 定义4x4变换矩阵
        this->getCameraPose(odom, camPoseMatrix, camPoseColorMatrix); // 获取相机的位姿矩阵
    
        this->position_(0) = camPoseMatrix(0, 3); // 提取相机的x坐标
        this->position_(1) = camPoseMatrix(1, 3); // 提取相机的y坐标
        this->position_(2) = camPoseMatrix(2, 3); // 提取相机的z坐标
        this->orientation_ = camPoseMatrix.block<3, 3>(0, 0); // 提取相机的旋转矩阵
    
        this->positionColor_(0) = camPoseColorMatrix(0, 3); // 提取彩色相机的x坐标
        this->positionColor_(1) = camPoseColorMatrix(1, 3); // 提取彩色相机的y坐标
        this->positionColor_(2) = camPoseColorMatrix(2, 3); // 提取彩色相机的z坐标
        this->orientationColor_ = camPoseColorMatrix.block<3, 3>(0, 0); // 提取彩色相机的旋转矩阵
    }

    void dynamicDetector::alignedDepthCB(const sensor_msgs::ImageConstPtr& img){
        cv_bridge::CvImagePtr imgPtr = cv_bridge::toCvCopy(img, img->encoding); // 将ROS图像消息转换为OpenCV格式
        if (img->encoding == sensor_msgs::image_encodings::TYPE_32FC1){
            (imgPtr->image).convertTo(imgPtr->image, CV_16UC1, this->depthScale_); // 将32位浮点型图像转换为16位无符号整型，并应用深度比例因子
        }
        imgPtr->image.copyTo(this->alignedDepthImage_); // 将对齐的深度图像复制到成员变量中
    
        cv::Mat depthNormalized; // 定义一个矩阵用于存储归一化的深度图像
        imgPtr->image.copyTo(depthNormalized); // 复制深度图像
        double min, max; // 定义变量存储深度图像的最小值和最大值
        cv::minMaxIdx(depthNormalized, &min, &max); // 获取深度图像的最小值和最大值
        cv::convertScaleAbs(depthNormalized, depthNormalized, 255. / max); // 将深度图像缩放到0-255范围
        depthNormalized.convertTo(depthNormalized, CV_8UC1); // 将深度图像转换为8位无符号整型
        cv::applyColorMap(depthNormalized, depthNormalized, cv::COLORMAP_BONE); // 应用骨骼颜色映射
        this->detectedAlignedDepthImg_ = depthNormalized; // 将处理后的深度图像存储到成员变量中
    }
    
    void dynamicDetector::yoloDetectionCB(const vision_msgs::Detection2DArrayConstPtr& detections){
        this->yoloDetectionResults_ = *detections; // 将YOLO检测结果存储到成员变量中
    }
    
    void dynamicDetector::colorImgCB(const sensor_msgs::ImageConstPtr& img){
        cv_bridge::CvImagePtr imgPtr = cv_bridge::toCvCopy(img, img->encoding); // 将ROS图像消息转换为OpenCV格式
        imgPtr->image.copyTo(this->detectedColorImage_); // 将彩色图像复制到成员变量中
    }
    
    void dynamicDetector::detectionCB(const ros::TimerEvent&){
        // 检测线程
        this->dbscanDetect(); // 调用DBSCAN算法进行点云聚类检测
        this->uvDetect(); // 调用UV检测器进行检测
        this->yoloDetectionTo3D(); // 将YOLO检测结果转换为3D边界框
        this->filterBBoxes(); // 过滤边界框
        this->newDetectFlag_ = true; // 标记为已获取新检测结果
    }
    
    void dynamicDetector::trackingCB(const ros::TimerEvent&){
        // 数据关联线程
        std::vector<int> bestMatch; // 存储每个当前检测与之前障碍物的最佳匹配索引
        std::vector<int> boxOOR; // 存储历史中的边界框是否在当前时间步被检测到
        this->boxAssociation(bestMatch, boxOOR); // 执行边界框关联
        // 卡尔曼滤波器跟踪
        if (bestMatch.size() or boxOOR.size()){
            this->kalmanFilterAndUpdateHist(bestMatch, boxOOR); // 使用卡尔曼滤波器更新历史
        }
        else {
            this->boxHist_.clear(); // 如果没有匹配，清空边界框历史
            this->pcHist_.clear(); // 清空点云历史
        }
    }

    void dynamicDetector::classificationCB(const ros::TimerEvent&){
        // 分类线程，用于识别动态障碍物
        std::vector<onboardDetector::box3D> dynamicBBoxesTemp; // 临时存储动态障碍物的边界框
    
        // 遍历所有点云/边界框历史记录
        for (size_t i = 0; i < this->pcHist_.size(); ++i) {
            // CASE 0: 如果是预测的动态障碍物
            if (this->boxHist_[i][0].is_estimated) {
                onboardDetector::box3D estimatedBBox; // 定义一个估计的边界框
                this->getEstimateBox(this->boxHist_[i], estimatedBBox); // 获取估计的边界框
                if (this->constrainSize_) { // 如果启用了目标大小约束
                    bool findMatch = false; // 标记是否找到匹配的目标大小
                    for (Eigen::Vector3d targetSize : this->targetObjectSize_) {
                        double xdiff = std::abs(this->boxHist_[i][0].x_width - targetSize(0)); // 计算x方向的宽度差
                        double ydiff = std::abs(this->boxHist_[i][0].y_width - targetSize(1)); // 计算y方向的宽度差
                        double zdiff = std::abs(this->boxHist_[i][0].z_width - targetSize(2)); // 计算z方向的宽度差
                        if (xdiff < 0.8 && ydiff < 0.8 && zdiff < 1.0) { // 如果差值在阈值范围内
                            findMatch = true; // 标记为找到匹配
                        }
                    }
                    if (findMatch) {
                        dynamicBBoxesTemp.push_back(estimatedBBox); // 将估计的边界框添加到动态障碍物列表中
                    }
                } else {
                    dynamicBBoxesTemp.push_back(estimatedBBox); // 如果未启用约束，直接添加估计的边界框
                }
                continue; // 跳过后续处理
            }
    
            // CASE I: 如果YOLO识别为动态障碍物
            if (this->boxHist_[i][0].is_human) {
                dynamicBBoxesTemp.push_back(this->boxHist_[i][0]); // 将识别为动态的边界框添加到动态障碍物列表中
                continue; // 跳过后续处理
            }
    
            // CASE II: 如果历史长度不足以运行分类
            int curFrameGap;
            if (int(this->pcHist_[i].size()) < this->skipFrame_ + 1) {
                curFrameGap = this->pcHist_[i].size() - 1; // 如果历史长度不足，使用当前可用的最大帧间隔
            } else {
                curFrameGap = this->skipFrame_; // 否则使用预定义的跳过帧数
            }
    
            // CASE III: 如果障碍物在多个时间步中被分类为动态，则即使静止仍然会被分类为动态，这个很重要
            // 如果过去30帧中有20帧被标记为动态，即使当前静止也会被强制视为动态
            int dynaFrames = 0; // 记录被分类为动态的时间步数
            if (int(this->boxHist_[i].size()) > this->forceDynaCheckRange_) {
                for (int j = 1; j < this->forceDynaCheckRange_ + 1; ++j) {
                    if (this->boxHist_[i][j].is_dynamic) {
                        ++dynaFrames; // 增加动态时间步计数
                    }
                }
            }
            if (dynaFrames >= this->forceDynaFrames_) {
                this->boxHist_[i][0].is_dynamic = true; // 将当前边界框标记为动态
                dynamicBBoxesTemp.push_back(this->boxHist_[i][0]); // 添加到动态障碍物列表中
                continue; // 跳过后续处理
            }
    
            // 计算当前点云和前一帧点云的速度
            std::vector<Eigen::Vector3d> currPc = this->pcHist_[i][0]; // 当前点云
            std::vector<Eigen::Vector3d> prevPc = this->pcHist_[i][curFrameGap]; // 前一帧点云
            Eigen::Vector3d Vcur(0., 0., 0.); // 单点速度
            Eigen::Vector3d Vbox(0., 0., 0.); // 边界框速度
            Eigen::Vector3d Vkf(0., 0., 0.); // 卡尔曼滤波器估计的速度
            int numPoints = currPc.size(); // 当前点云中的点数
            int votes = 0; // 投票计数
    
            // 计算边界框速度
            Vbox(0) = (this->boxHist_[i][0].x - this->boxHist_[i][curFrameGap].x) / (this->dt_ * curFrameGap);
            Vbox(1) = (this->boxHist_[i][0].y - this->boxHist_[i][curFrameGap].y) / (this->dt_ * curFrameGap);
            Vbox(2) = (this->boxHist_[i][0].z - this->boxHist_[i][curFrameGap].z) / (this->dt_ * curFrameGap);
            Vkf(0) = this->boxHist_[i][0].Vx; // 卡尔曼滤波器估计的x方向速度
            Vkf(1) = this->boxHist_[i][0].Vy; // 卡尔曼滤波器估计的y方向速度
    
            // 查找最近邻点
            int numSkip = 0; // 跳过的点计数
            for (size_t j = 0; j < currPc.size(); ++j) {
                // 如果点在前一帧中不可见，则跳过
                if (!this->isInFov(this->positionHist_[curFrameGap], this->orientationHist_[curFrameGap], currPc[j])) {
                    ++numSkip;
                    --numPoints;
                    continue;
                }
    
                double minDist = 2; // 最近邻距离的初始值
                Eigen::Vector3d nearestVect; // 最近邻向量
                for (size_t k = 0; k < prevPc.size(); k++) { // 遍历前一帧点云
                    double dist = (currPc[j] - prevPc[k]).norm(); // 计算距离
                    if (abs(dist) < minDist) {
                        minDist = dist; // 更新最近邻距离
                        nearestVect = currPc[j] - prevPc[k]; // 更新最近邻向量
                    }
                }
                Vcur = nearestVect / (this->dt_ * curFrameGap); // 计算当前点的速度
                Vcur(2) = 0; // 忽略z方向速度
                double velSim = Vcur.dot(Vbox) / (Vcur.norm() * Vbox.norm()); // 计算速度相似度
    
                if (velSim < 0) {
                    ++numSkip; // 如果速度方向相反，跳过该点
                    --numPoints;
                } else {
                    if (Vcur.norm() > this->dynaVelThresh_) {
                        ++votes; // 如果速度超过阈值，增加投票计数
                    }
                }
            }
    
            // 更新动态边界框
            double voteRatio = (numPoints > 0) ? double(votes) / double(numPoints) : 0; // 计算投票比例
            double velNorm = Vkf.norm(); // 卡尔曼滤波器估计的速度模长
    
            // 根据投票比例和速度阈值更新动态标记
            if (voteRatio >= this->dynaVoteThresh_ && velNorm >= this->dynaVelThresh_ && double(numSkip) / double(numPoints) < this->maxSkipRatio_) {
                this->boxHist_[i][0].is_dynamic_candidate = true; // 标记为动态候选
                int dynaConsistCount = 0; // 动态一致性计数
                if (int(this->boxHist_[i].size()) >= this->dynamicConsistThresh_) {
                    for (int j = 0; j < this->dynamicConsistThresh_; ++j) {
                        if (this->boxHist_[i][j].is_dynamic_candidate) {
                            ++dynaConsistCount; // 增加一致性计数
                        }
                    }
                }
                if (dynaConsistCount == this->dynamicConsistThresh_) {
                    this->boxHist_[i][0].is_dynamic = true; // 标记为动态
                    dynamicBBoxesTemp.push_back(this->boxHist_[i][0]); // 添加到动态障碍物列表中
                }
            }
        }
    
        // 根据目标大小过滤动态障碍物
        if (this->constrainSize_) {
            std::vector<onboardDetector::box3D> dynamicBBoxesBeforeConstrain = dynamicBBoxesTemp; // 保存过滤前的动态障碍物
            dynamicBBoxesTemp.clear(); // 清空临时动态障碍物列表
    
            for (onboardDetector::box3D ob : dynamicBBoxesBeforeConstrain) {
                if (!ob.is_estimated) { // 如果不是估计的边界框
                    bool findMatch = false; // 标记是否找到匹配的目标大小
                    for (Eigen::Vector3d targetSize : this->targetObjectSize_) {
                        double xdiff = std::abs(ob.x_width - targetSize(0)); // 计算x方向的宽度差
                        double ydiff = std::abs(ob.y_width - targetSize(1)); // 计算y方向的宽度差
                        double zdiff = std::abs(ob.z_width - targetSize(2)); // 计算z方向的宽度差
                        if (xdiff < 0.8 && ydiff < 0.8 && zdiff < 1.0) { // 如果差值在阈值范围内
                            findMatch = true; // 标记为找到匹配
                        }
                    }
                    if (findMatch) {
                        dynamicBBoxesTemp.push_back(ob); // 添加到动态障碍物列表中
                    }
                } else {
                    dynamicBBoxesTemp.push_back(ob); // 如果是估计的边界框，直接添加
                }
            }
        }
    
        this->dynamicBBoxes_ = dynamicBBoxesTemp; // 更新动态障碍物成员变量
    }

    void dynamicDetector::visCB(const ros::TimerEvent&){
        this->publishUVImages(); // 发布UV图像
        this->publish3dBox(this->uvBBoxes_, this->uvBBoxesPub_, 0, 1, 0); // 发布UV检测器生成的3D边界框
        std::vector<Eigen::Vector3d> dynamicPoints; // 定义动态点云的存储容器
        this->getDynamicPc(dynamicPoints); // 获取动态点云
        this->publishPoints(dynamicPoints, this->dynamicPointsPub_); // 发布动态点云
        this->publishPoints(this->filteredPoints_, this->filteredPointsPub_); // 发布过滤后的点云
        this->publish3dBox(this->dbBBoxes_, this->dbBBoxesPub_, 1, 0, 0); // 发布DBSCAN生成的3D边界框
        this->publishYoloImages(); // 发布YOLO检测结果图像
        this->publishColorImages(); // 发布彩色图像
        this->publish3dBox(this->yoloBBoxes_, this->yoloBBoxesPub_, 1, 0, 1); // 发布YOLO生成的3D边界框
        this->publish3dBox(this->filteredBBoxes_, this->filteredBBoxesPub_, 0, 1, 1); // 发布过滤后的3D边界框
        this->publish3dBox(this->trackedBBoxes_, this->trackedBBoxesPub_, 1, 1, 0); // 发布跟踪的3D边界框
        this->publish3dBox(this->dynamicBBoxes_, this->dynamicBBoxesPub_, 0, 0, 1); // 发布动态3D边界框
        this->publishHistoryTraj(); // 发布历史轨迹
        this->publishVelVis(); // 发布速度可视化信息
    }
    
    void dynamicDetector::uvDetect(){
        // 初始化UV检测器
        if (this->uvDetector_ == NULL){
            this->uvDetector_.reset(new UVdetector()); // 创建UV检测器对象
            this->uvDetector_->fx = this->fx_; // 设置相机内参fx
            this->uvDetector_->fy = this->fy_; // 设置相机内参fy
            this->uvDetector_->px = this->cx_; // 设置相机主点cx
            this->uvDetector_->py = this->cy_; // 设置相机主点cy
            this->uvDetector_->depthScale_ = this->depthScale_; // 设置深度比例因子
            this->uvDetector_->max_dist = this->raycastMaxLength_ * 1000; // 设置最大检测距离
        }
    
        // 从深度图像中检测
        if (not this->depthImage_.empty()){
            this->uvDetector_->depth = this->depthImage_; // 设置深度图像
            this->uvDetector_->detect(); // 执行UV检测
            this->uvDetector_->extract_3Dbox(); // 提取3D边界框
    
            this->uvDetector_->display_U_map(); // 显示U深度图
            this->uvDetector_->display_bird_view(); // 显示鸟瞰图
            this->uvDetector_->display_depth(); // 显示深度图
    
            // 将边界框转换到世界坐标系
            std::vector<onboardDetector::box3D> uvBBoxes; // 定义存储UV边界框的容器
            this->transformUVBBoxes(uvBBoxes); // 转换UV边界框到世界坐标系
            this->uvBBoxes_ = uvBBoxes; // 更新成员变量
        }
    }
    
    void dynamicDetector::dbscanDetect(){
        this->projectDepthImage(); // 将深度图像数据有效地转换为三维点云数据
        this->updatePoseHist(); // 更新相机位姿历史
        this->filterPoints(this->projPoints_, this->filteredPoints_); // 过滤点云
        this->clusterPointsAndBBoxes(this->filteredPoints_, this->dbBBoxes_, this->pcClusters_, this->pcClusterCenters_, this->pcClusterStds_); // 聚类点云并生成边界框
    }
    
    void dynamicDetector::yoloDetectionTo3D(){
        std::vector<onboardDetector::box3D> yoloBBoxesTemp; // 定义临时存储YOLO边界框的容器
        for (size_t i = 0; i < this->yoloDetectionResults_.detections.size(); ++i){
            onboardDetector::box3D bbox3D; // 定义3D边界框
            cv::Rect bboxVis; // 定义可视化边界框
            this->getYolo3DBBox(this->yoloDetectionResults_.detections[i], bbox3D, bboxVis); // 将YOLO检测结果转换为3D边界框
            cv::rectangle(this->detectedAlignedDepthImg_, bboxVis, cv::Scalar(0, 255, 0), 5, 8, 0); // 在对齐深度图像上绘制边界框
            yoloBBoxesTemp.push_back(bbox3D); // 将3D边界框添加到临时容器中
        }
        this->yoloBBoxes_ = yoloBBoxesTemp; // 更新成员变量
    }

    // 动态检测器的边界框过滤方法
    void dynamicDetector::filterBBoxes(){
        // 临时存储过滤后的3D边界框、点云簇、簇中心和标准差
        std::vector<onboardDetector::box3D> filteredBBoxesTemp;
        std::vector<std::vector<Eigen::Vector3d>> filteredPcClustersTemp;
        std::vector<Eigen::Vector3d> filteredPcClusterCentersTemp;
        std::vector<Eigen::Vector3d> filteredPcClusterStdsTemp; 

        // 第一阶段：UV检测框与DBSCAN检测框的IOU匹配融合
        // 遍历所有UV检测框，寻找与DBSCAN检测框的最佳匹配
        for (size_t i=0 ; i<this->uvBBoxes_.size(); ++i){
            onboardDetector::box3D uvBBox = this->uvBBoxes_[i];
            double bestIOUForUVBBox, bestIOUForDBBBox;
            
            // 获取当前UV框在DBSCAN框中的最佳匹配索引和IOU值
            int bestMatchForUVBBox = this->getBestOverlapBBox(uvBBox, this->dbBBoxes_, bestIOUForUVBBox);
            if (bestMatchForUVBBox == -1) continue; // 无匹配则跳过
            
            // 获取匹配的DBSCAN框及其关联点云信息
            onboardDetector::box3D matchedDBBBox = this->dbBBoxes_[bestMatchForUVBBox]; 
            std::vector<Eigen::Vector3d> matchedPcCluster = this->pcClusters_[bestMatchForUVBBox];
            Eigen::Vector3d matchedPcClusterCenter = this->pcClusterCenters_[bestMatchForUVBBox];
            Eigen::Vector3d matchedPcClusterStd = this->pcClusterStds_[bestMatchForUVBBox];
            
            // 验证DBSCAN框在UV框中的反向匹配
            int bestMatchForDBBBox = this->getBestOverlapBBox(matchedDBBBox, this->uvBBoxes_, bestIOUForDBBBox);

            // 双向匹配验证：互为最佳匹配且IOU超过阈值
            if (bestMatchForDBBBox == int(i) && bestIOUForUVBBox > this->boxIOUThresh_ && bestIOUForDBBBox > this->boxIOUThresh_){
                onboardDetector::box3D bbox;
                
                // 保守融合策略：取两个框的坐标极值生成新边界框
                double xmax = std::max(uvBBox.x+uvBBox.x_width/2, matchedDBBBox.x+matchedDBBBox.x_width/2);
                double xmin = std::min(uvBBox.x-uvBBox.x_width/2, matchedDBBBox.x-matchedDBBBox.x_width/2);
                double ymax = std::max(uvBBox.y+uvBBox.y_width/2, matchedDBBBox.y+matchedDBBBox.y_width/2);
                double ymin = std::min(uvBBox.y-uvBBox.y_width/2, matchedDBBBox.y-matchedDBBBox.y_width/2);
                double zmax = std::max(uvBBox.z+uvBBox.z_width/2, matchedDBBBox.z+matchedDBBBox.z_width/2);
                double zmin = std::min(uvBBox.z-uvBBox.z_width/2, matchedDBBBox.z-matchedDBBBox.z_width/2);
                
                // 计算融合后边界框的中心点和尺寸
                bbox.x = (xmin+xmax)/2;
                bbox.y = (ymin+ymax)/2;
                bbox.z = (zmin+zmax)/2;
                bbox.x_width = xmax-xmin;
                bbox.y_width = ymax-ymin;
                bbox.z_width = zmax-zmin;
                bbox.Vx = 0;  // 初始化速度
                bbox.Vy = 0;

                // 将融合结果存入临时容器
                filteredBBoxesTemp.push_back(bbox);
                filteredPcClustersTemp.push_back(matchedPcCluster);      
                filteredPcClusterCentersTemp.push_back(matchedPcClusterCenter);
                filteredPcClusterStdsTemp.push_back(matchedPcClusterStd);
            }
        }

        // 第二阶段：使用YOLO检测结果进行动态目标标记
        if (this->yoloDetectionResults_.detections.size() != 0){
            vision_msgs::Detection2DArray filteredDetectionResults;  // 存储投影后的2D检测框
            
            // 将过滤后的3D框投影到彩色相机平面
            for (int j=0; j<int(filteredBBoxesTemp.size()); ++j){
                onboardDetector::box3D bbox = filteredBBoxesTemp[j];

                // 1. 将边界框从世界坐标系转换到相机坐标系
                Eigen::Vector3d centerWorld (bbox.x, bbox.y, bbox.z);
                Eigen::Vector3d sizeWorld (bbox.x_width, bbox.y_width, bbox.z_width);
                Eigen::Vector3d centerCam, sizeCam;
                this->transformBBox(centerWorld, sizeWorld, 
                                -this->orientationColor_.inverse() * this->positionColor_, 
                                this->orientationColor_.inverse(), 
                                centerCam, sizeCam);

                // 2. 计算转换后边界框的角点
                Eigen::Vector3d topleft (centerCam(0)-sizeCam(0)/2, centerCam(1)-sizeCam(1)/2, centerCam(2));
                Eigen::Vector3d bottomright (centerCam(0)+sizeCam(0)/2, centerCam(1)+sizeCam(1)/2, centerCam(2));

                // 3. 投影到图像平面并创建2D检测框
                int tlX = (this->fxC_ * topleft(0) + this->cxC_ * topleft(2)) / topleft(2);
                int tlY = (this->fyC_ * topleft(1) + this->cyC_ * topleft(2)) / topleft(2);
                int brX = (this->fxC_ * bottomright(0) + this->cxC_ * bottomright(2)) / bottomright(2);
                int brY = (this->fyC_ * bottomright(1) + this->cyC_ * bottomright(2)) / bottomright(2);

                vision_msgs::Detection2D result;
                result.bbox.center.x = tlX;
                result.bbox.center.y = tlY;
                result.bbox.size_x = brX - tlX;
                result.bbox.size_y = brY - tlY;
                filteredDetectionResults.detections.push_back(result);

                // 在检测图像上绘制绿色边界框
                cv::Rect bboxVis(tlX, tlY, brX-tlX, brY-tlY);
                cv::rectangle(this->detectedColorImage_, bboxVis, cv::Scalar(0, 255, 0), 5, 8, 0);
            }

            // 处理每个YOLO检测结果
            for (int i=0; i<int(this->yoloDetectionResults_.detections.size()); ++i){
                // 解析YOLO框坐标
                int tlXTarget = int(this->yoloDetectionResults_.detections[i].bbox.center.x);
                int tlYTarget = int(this->yoloDetectionResults_.detections[i].bbox.center.y);
                int brXTarget = tlXTarget + int(this->yoloDetectionResults_.detections[i].bbox.size_x);
                int brYTarget = tlYTarget + int(this->yoloDetectionResults_.detections[i].bbox.size_y);

                // 在图像上绘制蓝色YOLO边界框并添加标签
                cv::Rect yoloBox(tlXTarget, tlYTarget, brXTarget-tlXTarget, brYTarget-tlYTarget);
                cv::rectangle(this->detectedColorImage_, yoloBox, cv::Scalar(255, 0, 0), 5, 8, 0);
                cv::putText(this->detectedColorImage_, "dynamic", 
                        cv::Point(yoloBox.x, yoloBox.y-10), 
                        cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(255, 0, 0), 2);

                // 寻找最佳匹配的投影框
                double bestIOU = 0.0;
                int bestIdx = -1;
                for (int j=0; j<int(filteredBBoxesTemp.size()); ++j){
                    // 获取投影框坐标
                    int tlX = int(filteredDetectionResults.detections[j].bbox.center.x);
                    int tlY = int(filteredDetectionResults.detections[j].bbox.center.y);
                    int brX = tlX + int(filteredDetectionResults.detections[j].bbox.size_x);
                    int brY = tlY + int(filteredDetectionResults.detections[j].bbox.size_y);
                    
                    // 计算IOU
                    double xOverlap = std::max(0, std::min(brX, brXTarget) - std::max(tlX, tlXTarget));
                    double yOverlap = std::max(0, std::min(brY, brYTarget) - std::max(tlY, tlYTarget));
                    double intersection = xOverlap * yOverlap;
                    double unionArea = (brX-tlX)*(brY-tlY) + (brXTarget-tlXTarget)*(brYTarget-tlYTarget) - intersection;
                    double IOU = (unionArea > 0) ? (intersection/unionArea) : 0;

                    // 更新最佳匹配
                    if (IOU > bestIOU){
                        bestIOU = IOU;
                        bestIdx = j;
                    }
                }

                // 如果IOU超过阈值则标记为动态目标
                if (bestIOU > 0.5){
                    filteredBBoxesTemp[bestIdx].is_dynamic = true;
                    filteredBBoxesTemp[bestIdx].is_human = true;
                }
            }
        }

        // 最终更新类成员变量
        this->filteredBBoxes_ = filteredBBoxesTemp;
        this->filteredPcClusters_ = filteredPcClustersTemp;
        this->filteredPcClusterCenters_ = filteredPcClusterCentersTemp;
        this->filteredPcClusterStds_ = filteredPcClusterStdsTemp;
    }

    void dynamicDetector::transformUVBBoxes(std::vector<onboardDetector::box3D>& bboxes){
        // 清空输入的边界框容器
        bboxes.clear();
        
        // 遍历所有UV检测器检测到的3D边界框
        for(size_t i = 0; i < this->uvDetector_->box3Ds.size(); ++i){
            onboardDetector::box3D bbox; // 创建一个新的边界框对象
            
            // 获取UV检测器中边界框的位置和尺寸信息
            double x = this->uvDetector_->box3Ds[i].x; // 获取x坐标
            double y = this->uvDetector_->box3Ds[i].y; // 获取y坐标
            double z = this->uvDetector_->box3Ds[i].z; // 获取z坐标
            double xWidth = this->uvDetector_->box3Ds[i].x_width; // 获取x方向宽度
            double yWidth = this->uvDetector_->box3Ds[i].y_width; // 获取y方向宽度
            double zWidth = this->uvDetector_->box3Ds[i].z_width; // 获取z方向宽度
    
            // 创建用于坐标变换的向量
            Eigen::Vector3d center (x, y, z); // 边界框中心点
            Eigen::Vector3d size (xWidth, yWidth, zWidth); // 边界框尺寸
            Eigen::Vector3d newCenter, newSize; // 用于存储变换后的中心点和尺寸
    
            // 将边界框从相机坐标系转换到世界坐标系
            this->transformBBox(center, size, this->position_, this->orientation_, newCenter, newSize);
    
            // 将变换后的边界框信息赋值给新的边界框对象
            bbox.x = newCenter(0); // 设置变换后的x坐标
            bbox.y = newCenter(1); // 设置变换后的y坐标
            bbox.z = newCenter(2); // 设置变换后的z坐标
            bbox.x_width = newSize(0); // 设置变换后的x方向宽度
            bbox.y_width = newSize(1); // 设置变换后的y方向宽度
            bbox.z_width = newSize(2); // 设置变换后的z方向宽度
            
            // 将转换后的边界框添加到结果容器中
            bboxes.push_back(bbox);            
        }        
    }
    
    // 将点从相机坐标系转换到地图坐标系，并存储深度值
    void dynamicDetector::projectDepthImage(){
        this->projPointsNum_ = 0; // 初始化投影点数量为0
    
        // 获取深度图像的尺寸信息
        int cols = this->depthImage_.cols; // 图像列数
        int rows = this->depthImage_.rows; // 图像行数
        uint16_t* rowPtr; // 用于访问图像行的指针
    
        // 声明用于存储点的向量和深度值
        Eigen::Vector3d currPointCam, currPointMap; // 当前点在相机和地图坐标系中的位置
        double depth; // 深度值
        
        // 计算用于坐标变换的常量
        const double inv_factor = 1.0 / this->depthScale_; // 深度比例的倒数
        const double inv_fx = 1.0 / this->fx_; // 相机焦距fx的倒数
        const double inv_fy = 1.0 / this->fy_; // 相机焦距fy的倒数
    
        // 遍历深度图像中的每个像素（考虑边界和跳过像素）
        for (int v=this->depthFilterMargin_; v<rows-this->depthFilterMargin_; v=v+this->skipPixel_){ // 逐行遍历
            rowPtr = this->depthImage_.ptr<uint16_t>(v) + this->depthFilterMargin_; // 获取当前行的指针
            for (int u=this->depthFilterMargin_; u<cols-this->depthFilterMargin_; u=u+this->skipPixel_){ // 逐列遍历
                depth = (*rowPtr) * inv_factor; // 计算实际深度值
                
                // 处理特殊的深度值情况
                if (*rowPtr == 0) {
                    depth = this->raycastMaxLength_ + 0.1; // 如果深度为0，设置为最大射线长度加0.1
                } else if (depth < this->depthMinValue_) {
                    continue; // 如果深度小于最小值，跳过该点
                } else if (depth > this->depthMaxValue_) {
                    depth = this->raycastMaxLength_ + 0.1; // 如果深度大于最大值，设置为最大射线长度加0.1
                }
                rowPtr = rowPtr + this->skipPixel_; // 移动到下一个要处理的像素
    
                // 计算点在相机坐标系中的3D坐标
                currPointCam(0) = (u - this->cx_) * depth * inv_fx; // 计算x坐标
                currPointCam(1) = (v - this->cy_) * depth * inv_fy; // 计算y坐标
                currPointCam(2) = depth; // z坐标就是深度值                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
                
                // 将点从相机坐标系转换到地图坐标系
                currPointMap = this->orientation_ * currPointCam + this->position_;
    
                // 存储转换后的点和其深度值
                this->projPoints_[this->projPointsNum_] = currPointMap; // 存储转换后的点
                this->pointsDepth_[this->projPointsNum_] = depth; // 存储深度值
                this->projPointsNum_ = this->projPointsNum_ + 1; // 更新点数计数器
            }
        } 
    }
    
    void dynamicDetector::filterPoints(const std::vector<Eigen::Vector3d>& points, std::vector<Eigen::Vector3d>& filteredPoints){
        // 目前只使用体素滤波（未来可能会添加更多滤波方法）
        std::vector<Eigen::Vector3d> voxelFilteredPoints; // 用于存储体素滤波后的点
        this->voxelFilter(points, voxelFilteredPoints); // 执行体素滤波，对点云进行降采样，减少数据量
        filteredPoints = voxelFilteredPoints; // 将滤波结果赋值给输出参数
    }


    void dynamicDetector::clusterPointsAndBBoxes(const std::vector<Eigen::Vector3d>& points, 
        std::vector<onboardDetector::box3D>& bboxes, 
        std::vector<std::vector<Eigen::Vector3d>>& pcClusters, 
        std::vector<Eigen::Vector3d>& pcClusterCenters, 
        std::vector<Eigen::Vector3d>& pcClusterStds) {
        
        // 将Eigen::Vector3d类型的点云转换为DBSCAN算法可以处理的格式
        std::vector<onboardDetector::Point> pointsDB;
        this->eigenToDBPointVec(points, pointsDB, points.size());
    
        // 创建新的DBSCAN聚类器实例
        this->dbCluster_.reset(new DBSCAN(this->dbMinPointsCluster_, this->dbEpsilon_, pointsDB));
    
        // 执行DBSCAN聚类
        this->dbCluster_->run();
    
        // 统计聚类的数量
        int clusterNum = 0;
        for (size_t i = 0; i < this->dbCluster_->m_points.size(); ++i) {
            onboardDetector::Point pDB = this->dbCluster_->m_points[i];
            if (pDB.clusterID > clusterNum) {
                clusterNum = pDB.clusterID;
            }
        }
    
        // 清空并重新调整点云簇的大小
        pcClusters.clear();
        pcClusters.resize(clusterNum);
        
        // 将聚类结果按簇组织
        for (size_t i = 0; i < this->dbCluster_->m_points.size(); ++i) {
            onboardDetector::Point pDB = this->dbCluster_->m_points[i];
            if (pDB.clusterID > 0) {
                Eigen::Vector3d p = this->dbPointToEigen(pDB);
                pcClusters[pDB.clusterID-1].push_back(p);
            }            
        }
    
        // 计算每个簇的中心点和标准差
        for (size_t i = 0; i < pcClusters.size(); ++i) {
            Eigen::Vector3d pcClusterCenter(0., 0., 0.);
            Eigen::Vector3d pcClusterStd(0., 0., 0.);
            this->calcPcFeat(pcClusters[i], pcClusterCenter, pcClusterStd);
            pcClusterCenters.push_back(pcClusterCenter);
            pcClusterStds.push_back(pcClusterStd);
        }
    
        // 根据聚类结果计算边界框
        bboxes.clear();
        for (size_t i = 0; i < pcClusters.size(); ++i) {
            onboardDetector::box3D box;
    
            // 初始化边界框的最小最大值
            double xmin = pcClusters[i][0](0);
            double ymin = pcClusters[i][0](1);
            double zmin = pcClusters[i][0](2);
            double xmax = pcClusters[i][0](0);
            double ymax = pcClusters[i][0](1);
            double zmax = pcClusters[i][0](2);
    
            // 遍历簇中的所有点，找到x、y、z方向的最大最小值
            for (size_t j = 0; j < pcClusters[i].size(); ++j) {
                xmin = (pcClusters[i][j](0) < xmin) ? pcClusters[i][j](0) : xmin;
                ymin = (pcClusters[i][j](1) < ymin) ? pcClusters[i][j](1) : ymin;
                zmin = (pcClusters[i][j](2) < zmin) ? pcClusters[i][j](2) : zmin;
                xmax = (pcClusters[i][j](0) > xmax) ? pcClusters[i][j](0) : xmax;
                ymax = (pcClusters[i][j](1) > ymax) ? pcClusters[i][j](1) : ymax;
                zmax = (pcClusters[i][j](2) > zmax) ? pcClusters[i][j](2) : zmax;
            }
    
            // 设置边界框的ID
            box.id = i;
    
            // 计算边界框的中心点位置
            box.x = (xmax + xmin) / 2.0;
            box.y = (ymax + ymin) / 2.0;
            box.z = (zmax + zmin) / 2.0;
    
            // 计算边界框的尺寸（确保最小尺寸不小于0.1）
            box.x_width = (xmax - xmin) > 0.1 ? (xmax - xmin) : 0.1;
            box.y_width = (ymax - ymin) > 0.1 ? (ymax - ymin) : 0.1;
            box.z_width = (zmax - zmin);
    
            // 将计算好的边界框添加到结果中
            bboxes.push_back(box);
        }
    }

    void dynamicDetector::voxelFilter(const std::vector<Eigen::Vector3d>& points, std::vector<Eigen::Vector3d>& filteredPoints){
        const double res = 0.1; // 体素滤波的分辨率
        int xVoxels = ceil(2*this->localSensorRange_(0)/res); // x方向体素数量
        int yVoxels = ceil(2*this->localSensorRange_(1)/res); // y方向体素数量
        int zVoxels = ceil(2*this->localSensorRange_(2)/res); // z方向体素数量
        int totalVoxels = xVoxels * yVoxels * zVoxels; // 总体素数量
        // std::vector<bool> voxelOccupancyVec (totalVoxels, false);
        std::vector<int> voxelOccupancyVec (totalVoxels, 0); // 记录每个体素被占据的点数
    
        // 清空输出点云
        filteredPoints.clear();
        
        // 遍历所有点
        for (int i=0; i<this->projPointsNum_; ++i){
            Eigen::Vector3d p = points[i]; // 当前点
    
            // 判断点是否在滤波范围内，且高于地面且深度小于最大射线长度
            if (this->isInFilterRange(p) and p(2) >= this->groundHeight_ and this->pointsDepth_[i] <= this->raycastMaxLength_){
                int pID = this->posToAddress(p, res); // 计算点所在体素的索引
    
                voxelOccupancyVec[pID] +=1; // 当前体素点数加一
    
                // 只有体素内点数达到阈值才保留该点
                if (voxelOccupancyVec[pID] == this->voxelOccThresh_){
                    filteredPoints.push_back(p); // 添加到输出点云
                }
            }
        }  
    }
    
    void dynamicDetector::calcPcFeat(const std::vector<Eigen::Vector3d>& pcCluster, Eigen::Vector3d& pcClusterCenter, Eigen::Vector3d& pcClusterStd){
        int numPoints = pcCluster.size(); // 点的数量
        
        // 计算点云簇的中心
        for (int i=0 ; i<numPoints ; i++){
            pcClusterCenter(0) += pcCluster[i](0)/numPoints; // x方向均值
            pcClusterCenter(1) += pcCluster[i](1)/numPoints; // y方向均值
            pcClusterCenter(2) += pcCluster[i](2)/numPoints; // z方向均值
        }
    
        // 计算点云簇的标准差
        for (int i=0 ; i<numPoints ; i++){
            pcClusterStd(0) += std::pow(pcCluster[i](0) - pcClusterCenter(0),2); // x方向方差
            pcClusterStd(1) += std::pow(pcCluster[i](1) - pcClusterCenter(1),2); // y方向方差
            pcClusterStd(2) += std::pow(pcCluster[i](2) - pcClusterCenter(2),2); // z方向方差
        }        
    
        // 标准差开方
        pcClusterStd(0) = std::sqrt(pcClusterStd(0)/numPoints);
        pcClusterStd(1) = std::sqrt(pcClusterStd(1)/numPoints);
        pcClusterStd(2) = std::sqrt(pcClusterStd(2)/numPoints);
    }
    
    double dynamicDetector::calBoxIOU(const onboardDetector::box3D& box1, const onboardDetector::box3D& box2){
        double box1Volume = box1.x_width * box1.y_width * box1.z_width; // 计算第一个边界框体积
        double box2Volume = box2.x_width * box2.y_width * box2.z_width; // 计算第二个边界框体积
    
        // 计算两个边界框在各方向的重叠长度
        double l1Y = box1.y+box1.y_width/2-(box2.y-box2.y_width/2);
        double l2Y = box2.y+box2.y_width/2-(box1.y-box1.y_width/2);
        double l1X = box1.x+box1.x_width/2-(box2.x-box2.x_width/2);
        double l2X = box2.x+box2.x_width/2-(box1.x-box1.x_width/2);
        double l1Z = box1.z+box1.z_width/2-(box2.z-box2.z_width/2);
        double l2Z = box2.z+box2.z_width/2-(box1.z-box1.z_width/2);
        double overlapX = std::min( l1X , l2X ); // x方向重叠
        double overlapY = std::min( l1Y , l2Y ); // y方向重叠
        double overlapZ = std::min( l1Z , l2Z ); // z方向重叠
       
        // 修正重叠长度，防止超出实际尺寸
        if (std::max(l1X, l2X)<=std::max(box1.x_width,box2.x_width)){ 
            overlapX = std::min(box1.x_width, box2.x_width);
        }
        if (std::max(l1Y, l2Y)<=std::max(box1.y_width,box2.y_width)){ 
            overlapY = std::min(box1.y_width, box2.y_width);
        }
        if (std::max(l1Z, l2Z)<=std::max(box1.z_width,box2.z_width)){ 
            overlapZ = std::min(box1.z_width, box2.z_width);
        }
    
        double overlapVolume = overlapX * overlapY *  overlapZ; // 计算重叠体积
        double IOU = overlapVolume / (box1Volume+box2Volume-overlapVolume); // 计算IOU
        
        // 如果有任一方向无重叠，则IOU为0
        if (overlapX<=0 || overlapY<=0 ||overlapZ<=0){
            IOU = 0;
        }
        return IOU; // 返回IOU
    }

    void dynamicDetector::getYolo3DBBox(const vision_msgs::Detection2D& detection, onboardDetector::box3D& bbox3D, cv::Rect& bboxVis){
        // 如果对齐深度图像为空则直接返回
        if (this->alignedDepthImage_.empty()){
            return;
        }
    
        const Eigen::Vector3d humanSize (0.5, 0.5, 1.8); // 预定义人体尺寸
    
        // 1. 获取2D检测框信息
        int topX = int(detection.bbox.center.x); // 获取检测框左上角x坐标
        int topY = int(detection.bbox.center.y); // 获取检测框左上角y坐标
        int xWidth = int(detection.bbox.size_x); // 获取检测框宽度
        int yWidth = int(detection.bbox.size_y); // 获取检测框高度
        bboxVis.x = topX; // 设置可视化框x
        bboxVis.y = topY; // 设置可视化框y
        bboxVis.height = yWidth; // 设置可视化框高度
        bboxVis.width = xWidth; // 设置可视化框宽度
    
        // 2. 估算深度厚度（双重中位数绝对偏差MAD）
        uint16_t* rowPtr; // 行指针
        double depth; // 深度值
        const double inv_factor = 1.0 / this->depthScale_; // 深度比例倒数
        int vMin = std::min(topY, this->depthFilterMargin_); // 计算区域最小行
        int uMin = std::min(topX, this->depthFilterMargin_); // 计算区域最小列
        int vMax = std::min(topY+yWidth, this->imgRows_-this->depthFilterMargin_); // 区域最大行
        int uMax = std::min(topX+xWidth, this->imgCols_-this->depthFilterMargin_); // 区域最大列
        std::vector<double> depthValues; // 存储深度值
    
        // 遍历检测框区域，收集深度值
        for (int v=vMin; v<vMax; ++v){ // 行遍历
            rowPtr = this->alignedDepthImage_.ptr<uint16_t>(v);
            for (int u=uMin; u<uMax; ++u){ // 列遍历
                depth = (*rowPtr) * inv_factor; // 计算实际深度
                if (depth >= this->depthMinValue_ and depth <= this->depthMaxValue_){
                    depthValues.push_back(depth); // 合理深度加入
                }
                ++rowPtr; // 指针后移
            }
        }
        if (depthValues.size() == 0){ // 如果没有有效深度值
            return;
        }
    
        // 计算深度中位数和MAD
        double depthMedian, MAD;
        this->calculateMAD(depthValues, depthMedian, MAD);
    
        double depthMin = 10.0; double depthMax = -10.0; // 初始化最小最大深度
        // 再次遍历区域，找出中位数±1.5*MAD范围内的最小最大深度
        for (int v=vMin; v<vMax; ++v){
            rowPtr = this->alignedDepthImage_.ptr<uint16_t>(v);
            for (int u=uMin; u<uMax; ++u){
                depth = (*rowPtr) * inv_factor;
                if (depth >= this->depthMinValue_ and depth <= this->depthMaxValue_){
                    if ((depth < depthMin) and (depth >= depthMedian - 1.5 * MAD)){
                        depthMin = depth;
                    }
                    if ((depth > depthMax) and (depth <= depthMedian + 1.5 * MAD)){
                        depthMax = depth;
                    }
                }
                ++rowPtr;
            }
        }
        if (depthMin == 10.0 or depthMax == -10.0){ // 如果没有有效深度
            return;
        }
    
        // 3. 将2D检测框投影到相机坐标系下的3D点
        Eigen::Vector3d pUL, pBR, center; // 左上、右下、中心点
        pUL(0) = (topX - this->cxC_) * depthMedian / this->fxC_; // 左上角x
        pUL(1) = (topY - this->cyC_) * depthMedian / this->fyC_; // 左上角y
        pUL(2) = depthMedian; // 左上角z
    
        pBR(0) = (topX + xWidth - this->cxC_) * depthMedian / this->fxC_; // 右下角x
        pBR(1) = (topY + yWidth- this->cyC_) * depthMedian / this->fyC_; // 右下角y
        pBR(2) = depthMedian; // 右下角z
    
        center(0) = (pUL(0) + pBR(0))/2.0; // 中心x
        center(1) = (pUL(1) + pBR(1))/2.0; // 中心y
        center(2) = depthMedian; // 中心z
    
        double xWidth3D = std::abs(pBR(0) - pUL(0)); // 3D宽度
        double yWidth3D = std::abs(pBR(1) - pUL(1)); // 3D高度
        double zWidth3D = depthMax - depthMin; // 3D厚度
        if ((zWidth3D/humanSize(2)>=2.0) or (zWidth3D/humanSize(2) <= 0.5)){ // 如果厚度异常则用预定义值
            zWidth3D = humanSize(2);
        }       
        Eigen::Vector3d size (xWidth3D, yWidth3D, zWidth3D); // 3D尺寸
    
        // 4. 将3D点从相机坐标系变换到世界坐标系
        Eigen::Vector3d newCenter, newSize;
        this->transformBBox(center, size, this->positionColor_, this->orientationColor_, newCenter, newSize);
        bbox3D.x = newCenter(0); // 世界坐标x
        bbox3D.y = newCenter(1); // 世界坐标y
        bbox3D.z = newCenter(2); // 世界坐标z
    
        bbox3D.x_width = newSize(0); // 世界坐标宽度
        bbox3D.y_width = newSize(1); // 世界坐标高度
        bbox3D.z_width = newSize(2); // 世界坐标厚度
    
        // 5. 检查边界框尺寸，若与预定义尺寸差异过大则重置
        if ((bbox3D.x_width/humanSize(0)>=2.0) or (bbox3D.x_width/humanSize(0)<=0.5)){
            bbox3D.x_width = humanSize(0);
        }
        if ((bbox3D.y_width/humanSize(1)>=2.0) or (bbox3D.y_width/humanSize(1)<=0.5)){
            bbox3D.y_width = humanSize(1);
        }
        if ((bbox3D.z_width/humanSize(2)>=2.0) or (bbox3D.z_width/humanSize(2)<=0.5)){
            bbox3D.z = humanSize(2)/2.;
            bbox3D.z_width = humanSize(2);
        }
    }
    
    void dynamicDetector::calculateMAD(std::vector<double>& depthValues, double& depthMedian, double& MAD){
        std::sort(depthValues.begin(), depthValues.end()); // 对深度值排序
        int medianIdx = int(depthValues.size()/2); // 取中位数下标
        depthMedian = depthValues[medianIdx]; // 得到中位数
    
        std::vector<double> deviations; // 存储偏差
        for (size_t i=0; i<depthValues.size(); ++i){
            deviations.push_back(std::abs(depthValues[i] - depthMedian)); // 计算每个值与中位数的偏差
        }
        std::sort(deviations.begin(), deviations.end()); // 对偏差排序
        MAD = deviations[int(deviations.size()/2)]; // 取偏差的中位数
    }


    void dynamicDetector::boxAssociation(std::vector<int>& bestMatch, std::vector<int> &boxOOR){
        int numObjs = int(this->filteredBBoxes_.size()); // 获取当前检测到的边界框数量
    
        // 如果历史为空，初始化历史
        if (this->boxHist_.size() == 0){
            this->boxHist_.resize(numObjs); // 初始化边界框历史
            this->pcHist_.resize(numObjs); // 初始化点云历史
            bestMatch.resize(this->filteredBBoxes_.size(), -1); // 首次检测全部无匹配
            for (int i=0 ; i<numObjs ; ++i){
                // 初始化每个目标的历史和卡尔曼滤波器
                this->boxHist_[i].push_back(this->filteredBBoxes_[i]); // 添加当前边界框到历史
                this->pcHist_[i].push_back(this->filteredPcClusters_[i]); // 添加当前点云到历史
                MatrixXd states, A, B, H, P, Q, R; // 定义卡尔曼滤波器相关矩阵
                this->kalmanFilterMatrixAcc(this->filteredBBoxes_[i], states, A, B, H, P, Q, R); // 初始化卡尔曼滤波器矩阵
                onboardDetector::kalman_filter newFilter; // 创建卡尔曼滤波器对象
                newFilter.setup(states, A, B, H, P, Q, R); // 设置卡尔曼滤波器
                this->filters_.push_back(newFilter); // 添加到滤波器列表
            }
        }
        else{
            // 只有有新检测结果时才进行关联
            if (this->newDetectFlag_){
                this->boxAssociationHelper(bestMatch, boxOOR); // 调用辅助函数进行关联
            }
        }
    
        this->newDetectFlag_ = false; // 标记本次检测已关联
    }
    
    void dynamicDetector::boxAssociationHelper(std::vector<int>& bestMatch, std::vector<int> &boxOOR){
        int numObjs = int(this->filteredBBoxes_.size()); // 当前检测到的目标数量
        std::vector<onboardDetector::box3D> propedBoxes; // 预测的边界框
        std::vector<Eigen::VectorXd> propedBoxesFeat; // 预测框特征
        std::vector<Eigen::VectorXd> currBoxesFeat; // 当前框特征
        bestMatch.resize(numObjs); // 调整bestMatch大小
        std::deque<std::deque<onboardDetector::box3D>> boxHistTemp; // 临时历史
    
        // 线性预测：对历史边界框进行线性外推
        this->linearProp(propedBoxes);
    
        // 生成特征
        this->genFeat(propedBoxes, numObjs, propedBoxesFeat, currBoxesFeat);
    
        // 计算关联，寻找最佳匹配
        this->findBestMatch(propedBoxesFeat, currBoxesFeat, propedBoxes, bestMatch);
        if (this->boxHist_.size()){
            this->getBoxOutofRange(boxOOR, bestMatch); // 获取超出范围的框
            int numOORBox = 0; // 统计超出范围的框数量
            for (int i=0; i<int(boxOOR.size());i++){
                if (boxOOR[i]){
                    numOORBox++;
                }
            }
            if (numOORBox){
                this->findBestMatchEstimate(propedBoxesFeat, currBoxesFeat, propedBoxes, bestMatch, boxOOR); // 对超出范围的框进行估计匹配
            }
        }
    }
    
    void dynamicDetector::genFeat(const std::vector<onboardDetector::box3D>& propedBoxes, int numObjs, std::vector<Eigen::VectorXd>& propedBoxesFeat, std::vector<Eigen::VectorXd>& currBoxesFeat){
        propedBoxesFeat.resize(propedBoxes.size()); // 调整预测框特征向量大小
        currBoxesFeat.resize(numObjs); // 调整当前框特征向量大小
        this->genFeatHelper(propedBoxesFeat, propedBoxes); // 生成预测框特征
        this->genFeatHelper(currBoxesFeat, this->filteredBBoxes_); // 生成当前框特征
    }
    
    void dynamicDetector::genFeatHelper(std::vector<Eigen::VectorXd>& features, const std::vector<onboardDetector::box3D>& boxes){
        Eigen::VectorXd featureWeights(10); // 特征权重（3位置+3尺寸+1点数+3点云std）
        featureWeights << 2, 2, 2, 1, 1, 1, 0.5, 0.5, 0.5, 0.5;
        for (size_t i=0 ; i<boxes.size() ; i++){
            Eigen::VectorXd feature(10); // 单个目标的特征
            features[i] = feature;
            features[i](0) = (boxes[i].x - this->position_(0)) * featureWeights(0); // x位置
            features[i](1) = (boxes[i].y - this->position_(1)) * featureWeights(1); // y位置
            features[i](2) = (boxes[i].z - this->position_(2)) * featureWeights(2); // z位置
            features[i](3) = boxes[i].x_width * featureWeights(3); // x宽度
            features[i](4) = boxes[i].y_width * featureWeights(4); // y宽度
            features[i](5) = boxes[i].z_width * featureWeights(5); // z宽度
            features[i](6) = this->filteredPcClusters_[i].size() * featureWeights(6); // 点云数量
            features[i](7) = this->filteredPcClusterStds_[i](0) * featureWeights(7); // x方向std
            features[i](8) = this->filteredPcClusterStds_[i](1) * featureWeights(8); // y方向std
            features[i](9) = this->filteredPcClusterStds_[i](2) * featureWeights(9); // z方向std
        }
    }
    
    void dynamicDetector::linearProp(std::vector<onboardDetector::box3D>& propedBoxes){
        onboardDetector::box3D propedBox; // 临时变量
        for (size_t i=0 ; i<this->boxHist_.size() ; i++){
            propedBox = this->boxHist_[i][0]; // 取历史最新的边界框
            propedBox.is_estimated = this->boxHist_[i][0].is_estimated; // 保留估计标记
            propedBox.x += propedBox.Vx*this->dt_; // 线性外推x
            propedBox.y += propedBox.Vy*this->dt_; // 线性外推y
            propedBoxes.push_back(propedBox); // 添加到预测框列表
        }
    }

    void dynamicDetector::findBestMatch(const std::vector<Eigen::VectorXd>& propedBoxesFeat, const std::vector<Eigen::VectorXd>& currBoxesFeat, const std::vector<onboardDetector::box3D>& propedBoxes, std::vector<int>& bestMatch){
        int numObjs = this->filteredBBoxes_.size(); // 获取当前检测到的目标数量
        std::vector<double> bestSims; // 定义用于存储最佳相似度的向量
        bestSims.resize(numObjs); // 调整大小为目标数量
    
        // 遍历每一个当前检测到的目标
        for (int i=0 ; i<numObjs ; i++){
            double bestSim = -1.; // 初始化最佳相似度为-1
            int bestMatchInd = -1; // 初始化最佳匹配索引为-1
            // 遍历每一个预测框
            for (size_t j=0 ; j<propedBoxes.size() ; j++){
                double sim = propedBoxesFeat[j].dot(currBoxesFeat[i])/(propedBoxesFeat[j].norm()*currBoxesFeat[i].norm()); // 计算特征余弦相似度
                if (sim >= bestSim){
                    bestSim = sim; // 更新最佳相似度
                    bestSims[i] = sim; // 记录最佳相似度
                    bestMatchInd = j; // 记录最佳匹配索引
                }
            }
    
            double iou = this->calBoxIOU(this->filteredBBoxes_[i], propedBoxes[bestMatchInd]); // 计算IOU
            if(!(bestSims[i]>this->simThresh_ && iou)){ // 如果相似度或IOU不满足阈值
                bestSims[i] = 0; // 相似度置零
                bestMatch[i] = -1; // 匹配索引置为-1
            }
            else {
                bestMatch[i] = bestMatchInd; // 否则记录最佳匹配索引
            }
        }
    }
    
    void dynamicDetector::findBestMatchEstimate(const std::vector<Eigen::VectorXd>& propedBoxesFeat, const std::vector<Eigen::VectorXd>& currBoxesFeat, const std::vector<onboardDetector::box3D>& propedBoxes, std::vector<int>& bestMatch, std::vector<int>& boxOOR){
        int numObjs = int(this->filteredBBoxes_.size()); // 获取当前检测到的目标数量
        std::vector<double> bestSims; // 定义用于存储最佳相似度的向量
        bestSims.resize(numObjs); // 调整大小为目标数量
    
        // 遍历每一个当前检测到的目标
        for (int i=0 ; i<numObjs ; i++){
            if (bestMatch[i] < 0){ // 只处理未匹配的目标
                double bestSim = -1.; // 初始化最佳相似度为-1
                int bestMatchInd = -1; // 初始化最佳匹配索引为-1
                // 遍历每一个预测框
                for (size_t j=0 ; j<propedBoxes.size() ; j++){
                    if (propedBoxes[j].is_estimated and boxOOR[j]){ // 只考虑估计框且超出范围
                        double sim = propedBoxesFeat[j].dot(currBoxesFeat[i])/(propedBoxesFeat[j].norm()*currBoxesFeat[i].norm()); // 计算特征余弦相似度
                        if (sim >= bestSim){
                            bestSim = sim; // 更新最佳相似度
                            bestSims[i] = sim; // 记录最佳相似度
                            bestMatchInd = j; // 记录最佳匹配索引
                        }
                    }
                }
                double iou = this->calBoxIOU(this->filteredBBoxes_[i], propedBoxes[bestMatchInd]); // 计算IOU
                if(!(bestSims[i]>this->simThreshRetrack_ && iou)){ // 如果相似度或IOU不满足阈值
                    bestSims[i] = 0; // 相似度置零
                    bestMatch[i] = -1; // 匹配索引置为-1
                }
                else {
                    boxOOR[bestMatchInd] = 0; // 匹配后将boxOOR置0
                    bestMatch[i] = bestMatchInd; // 记录最佳匹配索引
                    // cout<<"retrack"<<endl;
                }
            }
        }
    }    
    
    void dynamicDetector::getBoxOutofRange(std::vector<int>& boxOOR, const std::vector<int>&bestMatch){
        if (int(this->boxHist_.size())>0){ // 如果历史不为空
            boxOOR.resize(this->boxHist_.size(), 1); // 初始化boxOOR为1
            for (int i=0; i<int(bestMatch.size()); i++){
                if (bestMatch[i]>=0){
                    boxOOR[bestMatch[i]] = 0; // 匹配到的框置0
                }
            }
            for (int i=0; i<int(boxOOR.size()); i++){
                if (boxOOR[i] and this->boxHist_[i][0].is_dynamic){
                    // cout<<"dynamic obstacle out of range"<<endl;
                }
                else{
                    boxOOR[i] = 0; // 非动态或未超出范围的框置0
                }
            }
        }     
    }
    
    int dynamicDetector::getEstimateFrameNum(const std::deque<onboardDetector::box3D> &boxHist){
        int frameNum = 0; // 计数变量
        if (boxHist.size()){
            for (int i=0; i<int(boxHist.size()); i++){
                if (boxHist[i].is_estimated){
                    frameNum++; // 统计连续的估计帧数
                }
                else{
                    break; // 遇到非估计帧则停止
                }
            }
        }
        return frameNum; // 返回估计帧数
    }
    
    void dynamicDetector::getEstimateBox(const std::deque<onboardDetector::box3D> &boxHist, onboardDetector::box3D &estimatedBBox){
        onboardDetector::box3D lastDetect; lastDetect.x = 0; lastDetect.y = 0; // 初始化最后一次检测到的框
        for (int i=0; i<int(boxHist.size()); i++){
            if (not boxHist[i].is_estimated){
                lastDetect = boxHist[i]; // 找到最近一次真实检测到的框
                break;
            }
        }
        estimatedBBox.x = boxHist[0].x - (boxHist[0].x-lastDetect.x)/2; // 估计x坐标
        estimatedBBox.y = boxHist[0].y - (boxHist[0].y-lastDetect.y)/2; // 估计y坐标
        estimatedBBox.z = boxHist[0].z; // z坐标直接取最新值
        estimatedBBox.x_width = std::min(std::abs(boxHist[0].x-lastDetect.x) + boxHist[0].x_width, 1.5 * boxHist[0].x_width); // 估计x宽度
        estimatedBBox.y_width = std::min(std::abs(boxHist[0].y-lastDetect.y) + boxHist[0].y_width, 1.5 * boxHist[0].y_width); // 估计y宽度
        estimatedBBox.z_width = boxHist[0].z_width; // z宽度直接取最新值
        estimatedBBox.is_estimated = true; // 标记为估计框
    }

    void dynamicDetector::kalmanFilterAndUpdateHist(const std::vector<int>& bestMatch, const std::vector<int> &boxOOR){
        std::vector<std::deque<onboardDetector::box3D>> boxHistTemp; // 临时存储边界框历史
        std::vector<std::deque<std::vector<Eigen::Vector3d>>> pcHistTemp; // 临时存储点云历史
        std::vector<onboardDetector::kalman_filter> filtersTemp; // 临时存储卡尔曼滤波器
        std::deque<onboardDetector::box3D> newSingleBoxHist; // 新目标的边界框历史
        std::deque<std::vector<Eigen::Vector3d>> newSinglePcHist; // 新目标的点云历史
        onboardDetector::kalman_filter newFilter; // 新目标的卡尔曼滤波器
        std::vector<onboardDetector::box3D> trackedBBoxesTemp; // 临时存储跟踪边界框
    
        newSingleBoxHist.resize(0); // 清空新目标边界框历史
        newSinglePcHist.resize(0); // 清空新目标点云历史
        int numObjs = this->filteredBBoxes_.size(); // 获取当前检测到的目标数量
    
        for (int i=0 ; i<numObjs ; i++){
            onboardDetector::box3D newEstimatedBBox; // 用于存储卡尔曼滤波后的边界框
    
            // 如果有匹配历史，则继承历史
            if (bestMatch[i]>=0){
                boxHistTemp.push_back(this->boxHist_[bestMatch[i]]); // 继承边界框历史
                pcHistTemp.push_back(this->pcHist_[bestMatch[i]]); // 继承点云历史
                filtersTemp.push_back(this->filters_[bestMatch[i]]); // 继承卡尔曼滤波器
    
                // 获取当前检测到的边界框
                onboardDetector::box3D currDetectedBBox = this->filteredBBoxes_[i];
    
                Eigen::MatrixXd Z; // 观测向量
                this->getKalmanObservationAcc(currDetectedBBox, bestMatch[i], Z); // 获取观测
                filtersTemp.back().estimate(Z, MatrixXd::Zero(6,1)); // 卡尔曼滤波器估计
    
                // 更新估计边界框的状态
                newEstimatedBBox.x = filtersTemp.back().output(0);
                newEstimatedBBox.y = filtersTemp.back().output(1);
                newEstimatedBBox.z = currDetectedBBox.z;
                newEstimatedBBox.Vx = filtersTemp.back().output(2);
                newEstimatedBBox.Vy = filtersTemp.back().output(3);
                newEstimatedBBox.Ax = filtersTemp.back().output(4);
                newEstimatedBBox.Ay = filtersTemp.back().output(5);   
    
                newEstimatedBBox.x_width = currDetectedBBox.x_width;
                newEstimatedBBox.y_width = currDetectedBBox.y_width;
                newEstimatedBBox.z_width = currDetectedBBox.z_width;
                newEstimatedBBox.is_dynamic = currDetectedBBox.is_dynamic;
                newEstimatedBBox.is_human = currDetectedBBox.is_human;
                newEstimatedBBox.is_estimated = false; // 标记为非估计框
            }
            else{
                boxHistTemp.push_back(newSingleBoxHist); // 新目标历史
                pcHistTemp.push_back(newSinglePcHist); // 新目标点云历史
    
                // 为新目标创建卡尔曼滤波器
                onboardDetector::box3D currDetectedBBox = this->filteredBBoxes_[i];
                MatrixXd states, A, B, H, P, Q, R;    
                this->kalmanFilterMatrixAcc(currDetectedBBox, states, A, B, H, P, Q, R); // 初始化卡尔曼滤波器矩阵
                newFilter.setup(states, A, B, H, P, Q, R); // 设置卡尔曼滤波器
                filtersTemp.push_back(newFilter); // 添加到滤波器列表
                newEstimatedBBox = currDetectedBBox; // 直接用当前检测框
            }
    
            // 如果历史长度超过限制，弹出最旧数据
            if (int(boxHistTemp[i].size()) == this->histSize_){
                boxHistTemp[i].pop_back();
                pcHistTemp[i].pop_back();
            }
    
            // 将新数据插入历史
            boxHistTemp[i].push_front(newEstimatedBBox); 
            pcHistTemp[i].push_front(this->filteredPcClusters_[i]);
    
            // 更新跟踪边界框
            trackedBBoxesTemp.push_back(newEstimatedBBox);
        }
        // 处理超出范围但仍需预测的目标
        if (boxOOR.size()){
            for (int i=0; i<int(boxOOR.size()); i++){
                onboardDetector::box3D newEstimatedBBox; // 用于存储卡尔曼滤波后的边界框 
                if (boxOOR[i] and this->getEstimateFrameNum(this->boxHist_[i]) < min(this->predSize_,this->histSize_-1)){
                    onboardDetector::box3D currDetectedBBox;
                    currDetectedBBox = this->boxHist_[i][0];
                    currDetectedBBox.x += this->dt_* currDetectedBBox.Vx; // 线性预测x
                    currDetectedBBox.y += this->dt_* currDetectedBBox.Vy; // 线性预测y
                    currDetectedBBox.Vx += this->dt_* currDetectedBBox.Ax; // 线性预测vx
                    currDetectedBBox.Vy += this->dt_* currDetectedBBox.Ay; // 线性预测vy
    
                    boxHistTemp.push_back(this->boxHist_[i]); // 继承历史
                    pcHistTemp.push_back(this->pcHist_[i]);
                    filtersTemp.push_back(this->filters_[i]);
    
                    Eigen::MatrixXd Z;
                    this->getKalmanObservationAcc(currDetectedBBox, i, Z);
                    //this->getKalmanObservationVel(currDetectedBBox, i, Z); // 获取观测
                    filtersTemp.back().estimate(Z, MatrixXd::Zero(6,1));
                    //filtersTemp.back().estimate(Z, MatrixXd::Zero(4,1)); // 卡尔曼滤波器估计
    
                    // 更新估计边界框的状态
                    newEstimatedBBox.x = filtersTemp.back().output(0);
                    newEstimatedBBox.y = filtersTemp.back().output(1);
                    newEstimatedBBox.z = currDetectedBBox.z;
                    newEstimatedBBox.Vx = filtersTemp.back().output(2);
                    newEstimatedBBox.Vy = filtersTemp.back().output(3);
                    newEstimatedBBox.Ax = filtersTemp.back().output(4); // 这两行对应注释
                    newEstimatedBBox.Ay = filtersTemp.back().output(5); //
    
                    newEstimatedBBox.x_width = currDetectedBBox.x_width;
                    newEstimatedBBox.y_width = currDetectedBBox.y_width;
                    newEstimatedBBox.z_width = currDetectedBBox.z_width;
                    newEstimatedBBox.is_dynamic = true; // 标记为动态
                    newEstimatedBBox.is_human = currDetectedBBox.is_human;
                    newEstimatedBBox.is_estimated = true; // 标记为估计框
                    newEstimatedBBox.is_dynamic_candidate = true; // 标记为动态候选
    
                    // 如果历史长度超过限制，弹出最旧数据
                    if (int(boxHistTemp.back().size()) == this->histSize_){
                        boxHistTemp.back().pop_back();
                        pcHistTemp.back().pop_back();
                    }
    
                    // 插入新数据
                    boxHistTemp.back().push_front(newEstimatedBBox);
                    pcHistTemp.back().push_front(this->pcHist_[i][0]);
                    trackedBBoxesTemp.push_back(newEstimatedBBox);
                }
            }
        }
    
        // 固定边界框尺寸，防止抖动
        if (boxHistTemp.size()){
            for (size_t i=0; i<trackedBBoxesTemp.size(); ++i){ 
                if (not boxHistTemp[i][0].is_estimated){
                    if (int(boxHistTemp[i].size()) >= this->fixSizeHistThresh_){
                        if ((abs(trackedBBoxesTemp[i].x_width-boxHistTemp[i][1].x_width)/boxHistTemp[i][1].x_width) <= this->fixSizeDimThresh_ &&
                            (abs(trackedBBoxesTemp[i].y_width-boxHistTemp[i][1].y_width)/boxHistTemp[i][1].y_width) <= this->fixSizeDimThresh_&&
                            (abs(trackedBBoxesTemp[i].z_width-boxHistTemp[i][1].z_width)/boxHistTemp[i][1].z_width) <= this->fixSizeDimThresh_){
                            trackedBBoxesTemp[i].x_width = boxHistTemp[i][1].x_width;
                            trackedBBoxesTemp[i].y_width = boxHistTemp[i][1].y_width;
                            trackedBBoxesTemp[i].z_width = boxHistTemp[i][1].z_width;
                            boxHistTemp[i][0].x_width = trackedBBoxesTemp[i].x_width;
                            boxHistTemp[i][0].y_width = trackedBBoxesTemp[i].y_width;
                            boxHistTemp[i][0].z_width = trackedBBoxesTemp[i].z_width;
                        }
                    }
                }
            }
        }
        
        // 更新历史成员变量
        this->boxHist_ = boxHistTemp;
        this->pcHist_ = pcHistTemp;
        this->filters_ = filtersTemp;
    
        // 更新跟踪边界框
        this->trackedBBoxes_=  trackedBBoxesTemp;
    }

    void dynamicDetector::kalmanFilterMatrixVel(const onboardDetector::box3D& currDetectedBBox, MatrixXd& states, MatrixXd& A, MatrixXd& B, MatrixXd& H, MatrixXd& P, MatrixXd& Q, MatrixXd& R){
        states.resize(4,1); // 状态向量大小为4×1
        states(0) = currDetectedBBox.x; // 初始化x位置
        states(1) = currDetectedBBox.y; // 初始化y位置
        // 初始化速度为0
        states(2) = 0.;
        states(3) = 0.;
    
        MatrixXd ATemp;
        ATemp.resize(4, 4); // 临时A矩阵大小为4×4
        ATemp <<  0, 0, 1, 0,
                  0, 0, 0, 1,
                  0, 0, 0, 0,
                  0 ,0, 0, 0;
        A = MatrixXd::Identity(4,4) + this->dt_*ATemp; // 状态转移矩阵
        B = MatrixXd::Zero(4, 4); // 控制输入矩阵为零
        H = MatrixXd::Identity(4, 4); // 观测矩阵为单位阵
        P = MatrixXd::Identity(4, 4) * this->eP_; // 协方差初始化
        Q = MatrixXd::Identity(4, 4); // 过程噪声协方差
        Q(0,0) *= this->eQPos_; Q(1,1) *= this->eQPos_; Q(2,2) *= this->eQVel_; Q(3,3) *= this->eQVel_; // 设置过程噪声
        R = MatrixXd::Identity(4, 4); // 观测噪声协方差
        R(0,0) *= this->eRPos_; R(1,1) *= this->eRPos_; R(2,2) *= this->eRVel_; R(3,3) *= this->eRVel_; // 设置观测噪声
    }
    
    void dynamicDetector::kalmanFilterMatrixAcc(const onboardDetector::box3D& currDetectedBBox, MatrixXd& states, MatrixXd& A, MatrixXd& B, MatrixXd& H, MatrixXd& P, MatrixXd& Q, MatrixXd& R){
        states.resize(6,1); // 状态向量大小为6×1
        states(0) = currDetectedBBox.x; // 初始化x位置
        states(1) = currDetectedBBox.y; // 初始化y位置
        // 初始化速度和加速度为0
        states(2) = 0.;
        states(3) = 0.;
        states(4) = 0.;
        states(5) = 0.;
    
        MatrixXd ATemp;
        ATemp.resize(6, 6); // 临时A矩阵大小为6×6
    
        // 状态转移矩阵，包含位置、速度、加速度
        ATemp <<  1, 0, this->dt_, 0, 0.5*pow(this->dt_, 2), 0,
                  0, 1, 0, this->dt_, 0, 0.5*pow(this->dt_, 2),
                  0, 0, 1, 0, this->dt_, 0,
                  0 ,0, 0, 1, 0, this->dt_,
                  0, 0, 0, 0, 1, 0,
                  0, 0, 0, 0, 0, 1;
        A = ATemp; // 状态转移矩阵赋值
        B = MatrixXd::Zero(6, 6); // 控制输入矩阵为零
        H = MatrixXd::Identity(6, 6); // 观测矩阵为单位阵
        P = MatrixXd::Identity(6, 6) * this->eP_; // 协方差初始化
        Q = MatrixXd::Identity(6, 6); // 过程噪声协方差
        Q(0,0) *= this->eQPos_; Q(1,1) *= this->eQPos_; Q(2,2) *= this->eQVel_; Q(3,3) *= this->eQVel_; Q(4,4) *= this->eQAcc_; Q(5,5) *= this->eQAcc_; // 设置过程噪声
        R = MatrixXd::Identity(6, 6); // 观测噪声协方差
        R(0,0) *= this->eRPos_; R(1,1) *= this->eRPos_; R(2,2) *= this->eRVel_; R(3,3) *= this->eRVel_; R(4,4) *= this->eRAcc_; R(5,5) *= this->eRAcc_; // 设置观测噪声
    }
    
    void dynamicDetector::getKalmanObservationVel(const onboardDetector::box3D& currDetectedBBox, int bestMatchIdx, MatrixXd& Z){
        Z.resize(4,1); // 观测向量大小为4×1
        Z(0) = currDetectedBBox.x; // 当前x位置
        Z(1) = currDetectedBBox.y; // 当前y位置
    
        // 使用前k帧估算速度
        int k = this->kfAvgFrames_; // 平均帧数
        int historySize = this->boxHist_[bestMatchIdx].size(); // 历史长度
        if (historySize < k){
            k = historySize; // 若历史不足k帧，取实际长度
        }
        onboardDetector::box3D prevMatchBBox = this->boxHist_[bestMatchIdx][k-1]; // 取前k帧的边界框
    
        Z(2) = (currDetectedBBox.x-prevMatchBBox.x)/(this->dt_*k); // 估算x方向速度
        Z(3) = (currDetectedBBox.y-prevMatchBBox.y)/(this->dt_*k); // 估算y方向速度
    }
    
    void dynamicDetector::getKalmanObservationAcc(const onboardDetector::box3D& currDetectedBBox, int bestMatchIdx, MatrixXd& Z){
        Z.resize(6, 1); // 观测向量大小为6×1
        Z(0) = currDetectedBBox.x; // 当前x位置
        Z(1) = currDetectedBBox.y; // 当前y位置
    
        // 使用前k帧估算速度
        int k = this->kfAvgFrames_; // 平均帧数
        int historySize = this->boxHist_[bestMatchIdx].size(); // 历史长度
        if (historySize < k){
            k = historySize; // 若历史不足k帧，取实际长度
        }
        onboardDetector::box3D prevMatchBBox = this->boxHist_[bestMatchIdx][k-1]; // 取前k帧的边界框
    
        Z(2) = (currDetectedBBox.x - prevMatchBBox.x)/(this->dt_*k); // 估算x方向速度
        Z(3) = (currDetectedBBox.y - prevMatchBBox.y)/(this->dt_*k); // 估算y方向速度
        Z(4) = (Z(2) - prevMatchBBox.Vx)/(this->dt_*k); // 估算x方向加速度
        Z(5) = (Z(3) - prevMatchBBox.Vy)/(this->dt_*k); // 估算y方向加速度
    }
    
    void dynamicDetector::getDynamicPc(std::vector<Eigen::Vector3d>& dynamicPc){
        Eigen::Vector3d curPoint; // 当前点
        for (size_t i=0 ; i<this->filteredPoints_.size() ; ++i){
            curPoint = this->filteredPoints_[i]; // 取当前点
            for (size_t j=0; j<this->dynamicBBoxes_.size() ; ++j){
                // 判断点是否在动态障碍物边界框内
                if (abs(curPoint(0)-this->dynamicBBoxes_[j].x)<=this->dynamicBBoxes_[j].x_width/2 and 
                    abs(curPoint(1)-this->dynamicBBoxes_[j].y)<=this->dynamicBBoxes_[j].y_width/2 and 
                    abs(curPoint(2)-this->dynamicBBoxes_[j].z)<=this->dynamicBBoxes_[j].z_width/2) {
                        dynamicPc.push_back(curPoint); // 添加到动态点云
                        break; // 跳出内层循环
                    }
            }
        }
    } 
    
    void dynamicDetector::publishUVImages(){
        sensor_msgs::ImagePtr depthBoxMsg = cv_bridge::CvImage(std_msgs::Header(), "bgr8", this->uvDetector_->depth_show).toImageMsg(); // 转换深度图像为ROS消息
        sensor_msgs::ImagePtr UmapBoxMsg = cv_bridge::CvImage(std_msgs::Header(), "bgr8", this->uvDetector_->U_map_show).toImageMsg(); // 转换U深度图为ROS消息
        sensor_msgs::ImagePtr birdBoxMsg = cv_bridge::CvImage(std_msgs::Header(), "bgr8", this->uvDetector_->bird_view).toImageMsg();  // 转换鸟瞰图为ROS消息
        this->uvDepthMapPub_.publish(depthBoxMsg); // 发布深度图像
        this->uDepthMapPub_.publish(UmapBoxMsg); // 发布U深度图
        this->uvBirdViewPub_.publish(birdBoxMsg); // 发布鸟瞰图
    }
    
    void dynamicDetector::publishYoloImages(){
        sensor_msgs::ImagePtr detectedAlignedImgMsg = cv_bridge::CvImage(std_msgs::Header(), "bgr8", this->detectedAlignedDepthImg_).toImageMsg(); // 转换对齐深度图像为ROS消息
        this->detectedAlignedDepthImgPub_.publish(detectedAlignedImgMsg); // 发布对齐深度图像
    }

    void dynamicDetector::publishColorImages(){
        sensor_msgs::ImagePtr detectedColorImgMsg = cv_bridge::CvImage(std_msgs::Header(), "rgb8", this->detectedColorImage_).toImageMsg();
        this->detectedColorImgPub_.publish(detectedColorImgMsg);
    }

    void dynamicDetector::publishPoints(const std::vector<Eigen::Vector3d>& points, const ros::Publisher& publisher){
        pcl::PointXYZ pt;
        pcl::PointCloud<pcl::PointXYZ> cloud;        
        for (size_t i=0; i<points.size(); ++i){
            pt.x = points[i](0);
            pt.y = points[i](1);
            pt.z = points[i](2);
            cloud.push_back(pt);
        }    
        cloud.width = cloud.points.size();
        cloud.height = 1;
        cloud.is_dense = true;
        cloud.header.frame_id = "map";

        sensor_msgs::PointCloud2 cloudMsg;
        pcl::toROSMsg(cloud, cloudMsg);
        publisher.publish(cloudMsg);
    }


    void dynamicDetector::publish3dBox(const std::vector<box3D>& boxes, const ros::Publisher& publisher, double r, double g, double b) {
        // visualization using bounding boxes 
        visualization_msgs::Marker line;
        visualization_msgs::MarkerArray lines;
        line.header.frame_id = "map";
        line.type = visualization_msgs::Marker::LINE_LIST;
        line.action = visualization_msgs::Marker::ADD;
        line.ns = "box3D";  
        line.scale.x = 0.06;
        line.color.r = r;
        line.color.g = g;
        line.color.b = b;
        line.color.a = 1.0;
        line.lifetime = ros::Duration(0.1);
        
        for(size_t i = 0; i < boxes.size(); i++){
            // for estimated bbox, using a different color
            if (boxes[i].is_estimated){
                 line.color.r = 0.8;
                 line.color.g = 0.2;
                 line.color.b = 0.0;
                 line.color.a = 1.0;
            }

            // visualization msgs
            line.text = " Vx " + std::to_string(boxes[i].Vx) + " Vy " + std::to_string(boxes[i].Vy);
            double x = boxes[i].x; 
            double y = boxes[i].y; 
            double z = (boxes[i].z+boxes[i].z_width/2)/2; 

            // double x_width = std::max(boxes[i].x_width,boxes[i].y_width);
            // double y_width = std::max(boxes[i].x_width,boxes[i].y_width);
            double x_width = boxes[i].x_width;
            double y_width = boxes[i].y_width;
            double z_width = 2*z;

            // double z = 
            
            vector<geometry_msgs::Point> verts;
            geometry_msgs::Point p;
            // vertice 0
            p.x = x-x_width / 2.; p.y = y-y_width / 2.; p.z = z-z_width / 2.;
            verts.push_back(p);

            // vertice 1
            p.x = x-x_width / 2.; p.y = y+y_width / 2.; p.z = z-z_width / 2.;
            verts.push_back(p);

            // vertice 2
            p.x = x+x_width / 2.; p.y = y+y_width / 2.; p.z = z-z_width / 2.;
            verts.push_back(p);

            // vertice 3
            p.x = x+x_width / 2.; p.y = y-y_width / 2.; p.z = z-z_width / 2.;
            verts.push_back(p);

            // vertice 4
            p.x = x-x_width / 2.; p.y = y-y_width / 2.; p.z = z+z_width / 2.;
            verts.push_back(p);

            // vertice 5
            p.x = x-x_width / 2.; p.y = y+y_width / 2.; p.z = z+z_width / 2.;
            verts.push_back(p);

            // vertice 6
            p.x = x+x_width / 2.; p.y = y+y_width / 2.; p.z = z+z_width / 2.;
            verts.push_back(p);

            // vertice 7
            p.x = x+x_width / 2.; p.y = y-y_width / 2.; p.z = z+z_width / 2.;
            verts.push_back(p);
            
            int vert_idx[12][2] = {
                {0,1},
                {1,2},
                {2,3},
                {0,3},
                {0,4},
                {1,5},
                {3,7},
                {2,6},
                {4,5},
                {5,6},
                {4,7},
                {6,7}
            };
            
            for (size_t i=0;i<12;i++){
                line.points.push_back(verts[vert_idx[i][0]]);
                line.points.push_back(verts[vert_idx[i][1]]);
            }
            
            lines.markers.push_back(line);
            
            line.id++;
        }
        // publish
        publisher.publish(lines);
    }

    void dynamicDetector::publishHistoryTraj(){
        visualization_msgs::MarkerArray trajMsg;
        int countMarker = 0;
        for (size_t i=0; i<this->boxHist_.size(); ++i){
            visualization_msgs::Marker traj;
            traj.header.frame_id = "map";
            traj.header.stamp = ros::Time::now();
            traj.ns = "dynamic_detector";
            traj.id = countMarker;
            traj.type = visualization_msgs::Marker::LINE_LIST;
            traj.scale.x = 0.03;
            traj.scale.y = 0.03;
            traj.scale.z = 0.03;
            traj.color.a = 1.0; // Don't forget to set the alpha!
            traj.color.r = 0.0;
            traj.color.g = 1.0;
            traj.color.b = 0.0;
            for (size_t j=0; j<this->boxHist_[i].size()-1; ++j){
                geometry_msgs::Point p1, p2;
                onboardDetector::box3D box1 = this->boxHist_[i][j];
                onboardDetector::box3D box2 = this->boxHist_[i][j+1];
                p1.x = box1.x; p1.y = box1.y; p1.z = box1.z;
                p2.x = box2.x; p2.y = box2.y; p2.z = box2.z;
                traj.points.push_back(p1);
                traj.points.push_back(p2);
            }

            ++countMarker;
            trajMsg.markers.push_back(traj);
        }
        this->historyTrajPub_.publish(trajMsg);
    }

    void dynamicDetector::publishVelVis(){ // publish velocities for all tracked objects
        visualization_msgs::MarkerArray velVisMsg;
        int countMarker = 0;
        for (size_t i=0; i<this->trackedBBoxes_.size(); ++i){
            visualization_msgs::Marker velMarker;
            velMarker.header.frame_id = "map";
            velMarker.header.stamp = ros::Time::now();
            velMarker.ns = "dynamic_detector";
            velMarker.id =  countMarker;
            velMarker.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
            velMarker.pose.position.x = this->trackedBBoxes_[i].x;
            velMarker.pose.position.y = this->trackedBBoxes_[i].y;
            velMarker.pose.position.z = this->trackedBBoxes_[i].z + this->trackedBBoxes_[i].z_width/2. + 0.3;
            velMarker.scale.x = 0.15;
            velMarker.scale.y = 0.15;
            velMarker.scale.z = 0.15;
            velMarker.color.a = 1.0;
            velMarker.color.r = 1.0;
            velMarker.color.g = 0.0;
            velMarker.color.b = 0.0;
            velMarker.lifetime = ros::Duration(0.1);
            double vx = this->trackedBBoxes_[i].Vx;
            double vy = this->trackedBBoxes_[i].Vy;
            double vNorm = sqrt(vx*vx+vy*vy);
            std::string velText = "Vx=" + std::to_string(vx) + ", Vy=" + std::to_string(vy) + ", |V|=" + std::to_string(vNorm);
            velMarker.text = velText;
            velVisMsg.markers.push_back(velMarker);
            ++countMarker;
        }
        this->velVisPub_.publish(velVisMsg);
    }


    void dynamicDetector::transformBBox(const Eigen::Vector3d& center, const Eigen::Vector3d& size, const Eigen::Vector3d& position, const Eigen::Matrix3d& orientation,
                                               Eigen::Vector3d& newCenter, Eigen::Vector3d& newSize){
        double x = center(0); 
        double y = center(1);
        double z = center(2);
        double xWidth = size(0);
        double yWidth = size(1);
        double zWidth = size(2);

        // get 8 bouding boxes coordinates in the camera frame
        Eigen::Vector3d p1 (x+xWidth/2.0, y+yWidth/2.0, z+zWidth/2.0);
        Eigen::Vector3d p2 (x+xWidth/2.0, y+yWidth/2.0, z-zWidth/2.0);
        Eigen::Vector3d p3 (x+xWidth/2.0, y-yWidth/2.0, z+zWidth/2.0);
        Eigen::Vector3d p4 (x+xWidth/2.0, y-yWidth/2.0, z-zWidth/2.0);
        Eigen::Vector3d p5 (x-xWidth/2.0, y+yWidth/2.0, z+zWidth/2.0);
        Eigen::Vector3d p6 (x-xWidth/2.0, y+yWidth/2.0, z-zWidth/2.0);
        Eigen::Vector3d p7 (x-xWidth/2.0, y-yWidth/2.0, z+zWidth/2.0);
        Eigen::Vector3d p8 (x-xWidth/2.0, y-yWidth/2.0, z-zWidth/2.0);

        // transform 8 points to the map coordinate frame
        Eigen::Vector3d p1m = orientation * p1 + position;
        Eigen::Vector3d p2m = orientation * p2 + position;
        Eigen::Vector3d p3m = orientation * p3 + position;
        Eigen::Vector3d p4m = orientation * p4 + position;
        Eigen::Vector3d p5m = orientation * p5 + position;
        Eigen::Vector3d p6m = orientation * p6 + position;
        Eigen::Vector3d p7m = orientation * p7 + position;
        Eigen::Vector3d p8m = orientation * p8 + position;
        std::vector<Eigen::Vector3d> pointsMap {p1m, p2m, p3m, p4m, p5m, p6m, p7m, p8m};

        // find max min in x, y, z directions
        double xmin=p1m(0); double xmax=p1m(0); 
        double ymin=p1m(1); double ymax=p1m(1);
        double zmin=p1m(2); double zmax=p1m(2);
        for (Eigen::Vector3d pm : pointsMap){
            if (pm(0) < xmin){xmin = pm(0);}
            if (pm(0) > xmax){xmax = pm(0);}
            if (pm(1) < ymin){ymin = pm(1);}
            if (pm(1) > ymax){ymax = pm(1);}
            if (pm(2) < zmin){zmin = pm(2);}
            if (pm(2) > zmax){zmax = pm(2);}
        }
        newCenter(0) = (xmin + xmax)/2.0;
        newCenter(1) = (ymin + ymax)/2.0;
        newCenter(2) = (zmin + zmax)/2.0;
        newSize(0) = xmax - xmin;
        newSize(1) = ymax - ymin;
        newSize(2) = zmax - zmin;
    }

    bool dynamicDetector::isInFov(const Eigen::Vector3d& position, const Eigen::Matrix3d& orientation, Eigen::Vector3d& point){
        Eigen::Vector3d worldRay = point - position;
        Eigen::Vector3d camUnitX(1,0,0);
        Eigen::Vector3d camUnitY(0,1,0);
        Eigen::Vector3d camUnitZ(0,0,1);
        Eigen::Vector3d camRay;
        Eigen::Vector3d displacement; 
    
        // z is in depth direction in camera coord
        camRay = orientation.inverse()*worldRay;
        double camRayX = abs(camRay.dot(camUnitX));
        double camRayY = abs(camRay.dot(camUnitY));
        double camRayZ = abs(camRay.dot(camUnitZ));

        double htan = camRayX/camRayZ;
        double vtan = camRayY/camRayZ;
        
        double pi = 3.1415926;
        return htan<tan(42*pi/180) && vtan<tan(28*pi/180) && camRayZ<this->depthMaxValue_;
    }
    
    int dynamicDetector::getBestOverlapBBox(const onboardDetector::box3D& currBBox, const std::vector<onboardDetector::box3D>& targetBBoxes, double& bestIOU){
        bestIOU = 0.0;
        int bestIOUIdx = -1; // no match
        for (size_t i=0; i<targetBBoxes.size(); ++i){
            onboardDetector::box3D targetBBox = targetBBoxes[i];
            double IOU = this->calBoxIOU(currBBox, targetBBox);
            if (IOU > bestIOU){
                bestIOU = IOU;
                bestIOUIdx = i;
            }
        }
        return bestIOUIdx;
    }

    // user functions
    void dynamicDetector::getDynamicObstacles(std::vector<onboardDetector::box3D>& incomeDynamicBBoxes, const Eigen::Vector3d &robotSize){
        incomeDynamicBBoxes.clear();
        for (int i=0; i<int(this->dynamicBBoxes_.size()); i++){
            onboardDetector::box3D box = this->dynamicBBoxes_[i];
            box.x_width += robotSize(0);
            box.y_width += robotSize(1);
            box.z_width += robotSize(2);
            incomeDynamicBBoxes.push_back(box);
        }
    }

    void dynamicDetector::getDynamicObstaclesHist(std::vector<std::vector<Eigen::Vector3d>>& posHist, std::vector<std::vector<Eigen::Vector3d>>& velHist, std::vector<std::vector<Eigen::Vector3d>>& accHist, std::vector<std::vector<Eigen::Vector3d>>& sizeHist, const Eigen::Vector3d &robotSize){
		posHist.clear();
        velHist.clear();
        sizeHist.clear();
        accHist.clear();

        if (this->boxHist_.size()){
            for (size_t i=0 ; i<this->boxHist_.size() ; ++i){
                if (this->boxHist_[i][0].is_dynamic or this->boxHist_[i][0].is_human){   
                    bool findMatch = false;     
                    if (this->constrainSize_){
                        for (Eigen::Vector3d targetSize : this->targetObjectSize_){
                            double xdiff = std::abs(this->boxHist_[i][0].x_width - targetSize(0));
                            double ydiff = std::abs(this->boxHist_[i][0].y_width - targetSize(1));
                            double zdiff = std::abs(this->boxHist_[i][0].z_width - targetSize(2)); 
                            if (xdiff < 0.8 and ydiff < 0.8 and zdiff < 1.0){
                                findMatch = true;
                            }
                        }
                    }
                    else{
                        findMatch = true;
                    }
                    if (findMatch){
                        std::vector<Eigen::Vector3d> obPosHist, obVelHist, obSizeHist, obAccHist;
                        for (size_t j=0; j<this->boxHist_[i].size() ; ++j){
                            Eigen::Vector3d pos(this->boxHist_[i][j].x, this->boxHist_[i][j].y, this->boxHist_[i][j].z);
                            Eigen::Vector3d vel(this->boxHist_[i][j].Vx, this->boxHist_[i][j].Vy, 0);
                            Eigen::Vector3d size(this->boxHist_[i][j].x_width, this->boxHist_[i][j].y_width, this->boxHist_[i][j].z_width);
                            Eigen::Vector3d acc(this->boxHist_[i][j].Ax, this->boxHist_[i][j].Ay, 0);
                            size += robotSize;
                            obPosHist.push_back(pos);
                            obVelHist.push_back(vel);
                            obSizeHist.push_back(size);
                            obAccHist.push_back(acc);
                        }
                        posHist.push_back(obPosHist);
                        velHist.push_back(obVelHist);
                        sizeHist.push_back(obSizeHist);
                        accHist.push_back(obAccHist);
                    }
                }
            }
        }
	}

    void dynamicDetector::updatePoseHist(){
        if (int(this->positionHist_.size()) == this->skipFrame_){
            this->positionHist_.pop_back();
        }
        else{
            this->positionHist_.push_front(this->position_);
        }
        if (int(this->orientationHist_.size()) == this->skipFrame_){
            this->orientationHist_.pop_back();
        }
        else{
            this->orientationHist_.push_front(this->orientation_);
        }
    }
}