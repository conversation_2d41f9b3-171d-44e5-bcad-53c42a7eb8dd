# SFC随机种子控制说明

## 问题背景

您观察到同样的SFC程序每次运行时轨迹不一样，这确实与随机种子有关。主要的随机性来源是：

### 1. **RRT*路径规划算法的随机性**
SFC使用OMPL库中的RRT*算法进行初始路径规划：

```cpp
// 在 plan_path.hpp 中
auto planner(std::make_shared<ompl::geometric::RRTstar>(si));
```

**RRT***是一个基于随机采样的算法：
- 每次运行都会随机采样新的点
- 构建的搜索树结构不同
- 最终找到的路径也不同

### 2. **随机性的影响**
- **初始路径不同**：RRT*每次生成不同的初始路径
- **优化结果不同**：基于不同初始路径的SFC优化结果也会不同
- **轨迹变化**：最终的轨迹在相同起终点下可能有显著差异

## 解决方案：随机种子控制

### 1. **配置参数**
在`sfc.yaml`中添加了随机种子控制：

```yaml
sfc_cover_opt:
  random_seed: 42       # 随机种子 (-1: 使用时间种子, >=0: 固定种子)
```

### 2. **种子设置选项**

#### A. **固定种子** (推荐用于调试和对比)
```yaml
random_seed: 42  # 或任何非负整数
```
- **效果**：每次运行产生相同的轨迹
- **用途**：调试、性能对比、结果复现

#### B. **时间种子** (默认行为)
```yaml
random_seed: -1
```
- **效果**：每次运行产生不同的轨迹
- **用途**：探索不同的路径选项

### 3. **实现细节**

#### A. **OMPL随机种子设置**
```cpp
// 在 plan_path.hpp 中
if (seed >= 0) {
    ompl::RNG::setSeed(seed);
    std::cout << "[planPath] Using fixed random seed: " << seed << std::endl;
} else {
    unsigned int time_seed = static_cast<unsigned int>(std::time(nullptr));
    ompl::RNG::setSeed(time_seed);
    std::cout << "[planPath] Using time-based random seed: " << time_seed << std::endl;
}
```

#### B. **参数传递**
```cpp
// 在 sfc.cpp 中
plan_path::planPath<voxel_map::VoxelMap>(startGoal[0], startGoal[1],
                                        curMap.getOrigin(), curMap.getCorner(),
                                        &curMap, 0.1, 0.2,
                                        route, opt_convex_cover.random_seed);
```

## 使用示例

### 1. **调试和对比测试**
```yaml
# 使用固定种子确保结果可重复
sfc_cover_opt:
  random_seed: 42
  debug: true
```

运行多次会得到完全相同的轨迹，便于：
- 调试算法问题
- 对比不同参数设置的效果
- 验证代码修改的影响

### 2. **探索不同路径**
```yaml
# 使用时间种子探索不同可能的路径
sfc_cover_opt:
  random_seed: -1
```

每次运行会得到不同的轨迹，可以：
- 找到更优的路径选择
- 测试算法的鲁棒性
- 评估路径质量的分布

### 3. **批量测试**
```bash
# 测试不同种子的效果
for seed in 1 2 3 4 5; do
    rosparam set /sfc_cover_opt/random_seed $seed
    rosrun opt_sfc sfc
    # 记录结果...
done
```

## 日志输出

启用随机种子控制后，您会在终端看到：

```
[planPath] Using fixed random seed: 42
```
或
```
[planPath] Using time-based random seed: 1642751234
```

这有助于：
- 确认种子设置是否生效
- 记录用于复现结果的种子值
- 调试随机性相关问题

## 性能影响

- **固定种子**：无额外性能开销
- **时间种子**：极小的时间获取开销（微秒级）
- **路径质量**：不同种子可能产生质量差异较大的路径

## 最佳实践

### 1. **开发阶段**
```yaml
random_seed: 42  # 使用固定种子便于调试
debug: true
```

### 2. **测试阶段**
```yaml
random_seed: -1  # 使用随机种子测试鲁棒性
```

### 3. **生产环境**
根据应用需求选择：
- **需要一致性**：使用固定种子
- **需要多样性**：使用时间种子

## 其他随机性来源

除了RRT*算法，系统中可能还有其他随机性来源：

1. **优化算法**：某些优化器可能使用随机初始化
2. **数值计算**：浮点运算的微小差异
3. **系统调度**：多线程执行顺序的差异

当前的随机种子控制主要针对RRT*算法，这是最主要的随机性来源。

## 故障排除

### 问题：设置固定种子后轨迹仍然不同
**可能原因**：
1. 其他未控制的随机性来源
2. 系统时间或环境差异
3. 多线程竞争条件

**解决方案**：
1. 检查日志确认种子设置生效
2. 确保运行环境一致
3. 禁用并行处理（如果有）

### 问题：无法找到路径
**可能原因**：
某些种子可能导致RRT*无法在给定时间内找到路径

**解决方案**：
1. 增加规划超时时间
2. 尝试不同的种子值
3. 检查起终点和障碍物设置

通过合理使用随机种子控制，您可以在需要时获得一致的结果，同时保留探索不同路径的能力！
