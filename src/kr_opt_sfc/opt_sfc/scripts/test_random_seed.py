#!/usr/bin/env python3

import rospy
import rosparam
from opt_sfc.msg import TrajectoryTarget
from geometry_msgs.msg import Point, Vector3
from std_msgs.msg import Header
import time

def test_random_seed_effects():
    """测试随机种子对轨迹的影响"""
    rospy.init_node('test_random_seed', anonymous=True)
    
    # 创建发布器
    pub = rospy.Publisher('/sfc/trajectory_target', TrajectoryTarget, queue_size=1)
    
    # 等待发布器准备就绪
    rospy.sleep(2.0)
    
    print("=== SFC随机种子效果测试 ===")
    
    # 测试轨迹参数（固定起终点）
    start_pos = Point(x=0.0, y=0.0, z=1.0)
    goal_pos = Point(x=10.0, y=10.0, z=2.0)
    zero_vel = Vector3(x=0.0, y=0.0, z=0.0)
    zero_acc = Vector3(x=0.0, y=0.0, z=0.0)
    
    def create_trajectory_msg():
        """创建轨迹消息"""
        msg = TrajectoryTarget()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.header.frame_id = "odom"
        msg.start_position = start_pos
        msg.start_velocity = zero_vel
        msg.start_acceleration = zero_acc
        msg.goal_position = goal_pos
        msg.goal_velocity = zero_vel
        msg.goal_acceleration = zero_acc
        msg.enable_visualization = True
        return msg
    
    # 测试1: 固定种子测试
    print("\n--- 测试1: 固定种子一致性测试 ---")
    print("使用固定种子42，连续运行3次，观察轨迹是否一致")
    
    fixed_seed = 42
    for i in range(3):
        print(f"\n第{i+1}次运行 - 固定种子: {fixed_seed}")
        rospy.set_param('/sfc_cover_opt/random_seed', fixed_seed)
        
        msg = create_trajectory_msg()
        print(f"发布轨迹目标...")
        pub.publish(msg)
        
        print("等待规划完成...")
        rospy.sleep(8.0)
        print(f"第{i+1}次规划完成")
    
    print("\n观察结果：使用相同种子的轨迹应该完全一致")
    
    # 测试2: 不同固定种子测试
    print("\n--- 测试2: 不同固定种子对比测试 ---")
    print("使用不同固定种子，观察轨迹差异")
    
    seeds = [1, 10, 100]
    for seed in seeds:
        print(f"\n使用固定种子: {seed}")
        rospy.set_param('/sfc_cover_opt/random_seed', seed)
        
        msg = create_trajectory_msg()
        print(f"发布轨迹目标...")
        pub.publish(msg)
        
        print("等待规划完成...")
        rospy.sleep(8.0)
        print(f"种子{seed}的规划完成")
    
    print("\n观察结果：不同种子应该产生不同的轨迹")
    
    # 测试3: 时间种子随机性测试
    print("\n--- 测试3: 时间种子随机性测试 ---")
    print("使用时间种子(-1)，连续运行3次，观察轨迹变化")
    
    for i in range(3):
        print(f"\n第{i+1}次运行 - 时间种子")
        rospy.set_param('/sfc_cover_opt/random_seed', -1)
        
        msg = create_trajectory_msg()
        print(f"发布轨迹目标...")
        pub.publish(msg)
        
        print("等待规划完成...")
        rospy.sleep(8.0)
        print(f"第{i+1}次随机规划完成")
        
        # 短暂延时确保时间种子不同
        time.sleep(1)
    
    print("\n观察结果：使用时间种子的轨迹应该各不相同")
    
    # 测试4: 复杂场景测试
    print("\n--- 测试4: 复杂场景种子效果测试 ---")
    print("使用更复杂的起终点配置测试种子效果")
    
    # 更复杂的轨迹
    complex_start = Point(x=2.0, y=1.0, z=1.5)
    complex_goal = Point(x=8.0, y=12.0, z=3.0)
    
    test_seeds = [42, 123, -1]
    seed_names = ["固定种子42", "固定种子123", "时间种子"]
    
    for seed, name in zip(test_seeds, seed_names):
        print(f"\n复杂场景 - {name}")
        rospy.set_param('/sfc_cover_opt/random_seed', seed)
        
        msg = TrajectoryTarget()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.header.frame_id = "odom"
        msg.start_position = complex_start
        msg.start_velocity = zero_vel
        msg.start_acceleration = zero_acc
        msg.goal_position = complex_goal
        msg.goal_velocity = zero_vel
        msg.goal_acceleration = zero_acc
        msg.enable_visualization = True
        
        print(f"发布复杂轨迹目标...")
        pub.publish(msg)
        
        print("等待规划完成...")
        rospy.sleep(10.0)
        print(f"{name}的复杂场景规划完成")
    
    print("\n=== 随机种子测试完成 ===")
    print("总结:")
    print("1. 固定种子确保轨迹可重复")
    print("2. 不同固定种子产生不同轨迹")
    print("3. 时间种子每次都不同")
    print("4. 在RViz中观察轨迹的可视化差异")
    print("5. 查看终端输出的种子信息")

def print_current_seed():
    """打印当前的随机种子设置"""
    try:
        seed = rospy.get_param('/sfc_cover_opt/random_seed', 'Not set')
        obj_type = rospy.get_param('/sfc_cover_opt/obj_type', 'Not set')
        debug = rospy.get_param('/sfc_cover_opt/debug', 'Not set')
        
        print(f"当前SFC随机种子配置:")
        print(f"  random_seed: {seed}")
        print(f"  obj_type: {obj_type}")
        print(f"  debug: {debug}")
    except Exception as e:
        print(f"无法获取参数: {e}")

def test_seed_reproducibility():
    """测试种子的可重复性"""
    print("\n=== 种子可重复性验证 ===")
    
    pub = rospy.Publisher('/sfc/trajectory_target', TrajectoryTarget, queue_size=1)
    rospy.sleep(1.0)
    
    # 使用固定种子运行两次
    test_seed = 999
    start_pos = Point(x=1.0, y=1.0, z=1.0)
    goal_pos = Point(x=9.0, y=9.0, z=2.0)
    zero_vel = Vector3(x=0.0, y=0.0, z=0.0)
    zero_acc = Vector3(x=0.0, y=0.0, z=0.0)
    
    for run in [1, 2]:
        print(f"\n可重复性测试 - 第{run}次运行")
        print(f"使用种子: {test_seed}")
        
        rospy.set_param('/sfc_cover_opt/random_seed', test_seed)
        
        msg = TrajectoryTarget()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.header.frame_id = "odom"
        msg.start_position = start_pos
        msg.start_velocity = zero_vel
        msg.start_acceleration = zero_acc
        msg.goal_position = goal_pos
        msg.goal_velocity = zero_vel
        msg.goal_acceleration = zero_acc
        msg.enable_visualization = True
        
        pub.publish(msg)
        rospy.sleep(8.0)
        
        print(f"第{run}次运行完成")
    
    print("\n验证：两次运行的轨迹应该完全相同")

if __name__ == '__main__':
    try:
        print("SFC随机种子效果测试脚本")
        print_current_seed()
        
        choice = input("\n选择测试类型:\n1. 完整测试\n2. 可重复性验证\n请输入选择 (1/2): ")
        
        if choice == '1':
            test_random_seed_effects()
        elif choice == '2':
            test_seed_reproducibility()
        else:
            print("无效选择，运行完整测试")
            test_random_seed_effects()
            
    except rospy.ROSInterruptException:
        print("测试被中断")
    except KeyboardInterrupt:
        print("用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
