<?xml version="1.0" encoding="utf-8"?>
<launch>
  <!-- SFC (Safe Flight Corridor) 轨迹规划器启动文件 -->

  <!-- 地图参数配置 -->
  <arg name="map_frame_id" default="odom"/>           <!-- 地图坐标系 -->
  <arg name="map_size_x" default="18"/>               <!-- 地图X方向尺寸 (m) -->
  <arg name="map_size_y" default="9"/>                <!-- 地图Y方向尺寸 (m) -->
  <arg name="map_size_z" default="3"/>                <!-- 地图Z方向尺寸 (m) -->
  <arg name="map_x_origin" default="-9.0"/>           <!-- 地图X方向原点 (m) -->
  <arg name="map_y_origin" default="-4.5"/>           <!-- 地图Y方向原点 (m) -->
  <arg name="map_z_origin" default="-0.1"/>           <!-- 地图Z方向原点 (m) -->

  <!-- 地图文件和话题配置 -->
  <arg name="pcd_file_path" default="$(find planner)/cfg/saved_map/sim_map.pcd"/>  <!-- PCD地图文件路径 -->
  <arg name="cloud" default="/structure_map/global_gridmap"/>                      <!-- 点云话题名称 -->

  <!-- 地图读取节点：从PCD文件读取静态地图并发布为点云 -->
  <node pkg="param_env" name="read_grid_map" type="read_grid_map" output="screen">
    <!-- 话题重映射 -->
    <remap from="~global_gridmap" to="$(arg cloud)"/>

    <!-- 地图尺寸和原点参数 -->
    <param name="map/x_size"     value="$(arg map_size_x)" />
    <param name="map/y_size"     value="$(arg map_size_y)" />
    <param name="map/z_size"     value="$(arg map_size_z)" />
    <param name="map/x_origin"   value="$(arg map_x_origin)"/>
    <param name="map/y_origin"   value="$(arg map_y_origin)"/>
    <param name="map/z_origin"   value="$(arg map_z_origin)"/>

    <!-- 地图处理参数 -->
    <param name="map/resolution"      value="0.1"/>        <!-- 体素分辨率 (m) -->
    <param name="map/frame_id"        value="$(arg map_frame_id)" />
    <param name="map/inflate_radius"  value="0.1"/>        <!-- 障碍物膨胀半径 (m) -->
    <param name="map/mode"            value="4" />         <!-- 地图模式：4=读取PCD文件 -->
    <param name="file_path"           value="$(arg pcd_file_path)"/>
    <param name="map/auto_change"           value="false"/>
    <param name="map/publish_grid_centers"  value="true"/>
  </node>

  <!-- SFC轨迹规划节点 -->
  <node pkg="opt_sfc" type="sfc" name="sfc_node" output="screen">
    <!-- 加载SFC配置文件 -->
    <rosparam file="$(find opt_sfc)/config/sfc.yaml" command="load" />

    <!-- 坐标系和地图话题配置 -->
    <param name="frame_id"    value="$(arg map_frame_id)" />
    <param name="MapTopic"    value="$(arg cloud)" />

    <!-- 地图参数（与地图读取节点保持一致） -->
    <param name="map/x_size"     value="$(arg map_size_x)" />
    <param name="map/y_size"     value="$(arg map_size_y)" />
    <param name="map/z_size"     value="$(arg map_size_z)" />
    <param name="map/x_origin"   value="$(arg map_x_origin)"/>
    <param name="map/y_origin"   value="$(arg map_y_origin)"/>
    <param name="map/z_origin"   value="$(arg map_z_origin)"/>
    <param name="map/resolution"  value="0.1"/>
    <param name="map/inflate_radius" value="0.1"/>
  </node>

</launch>
