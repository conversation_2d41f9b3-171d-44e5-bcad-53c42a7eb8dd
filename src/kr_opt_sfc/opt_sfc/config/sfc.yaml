# SFC (Safe Flight Corridor) 轨迹规划器配置文件
TargetTopic: '/sfc/trajectory_target'  # 接收轨迹目标的话题名称
InflateRange: 0.1  # 障碍物膨胀半径 (m)，用于安全距离保证

# SFC 凸覆盖优化器参数
sfc_cover_opt:
  max_iter: 5           # 最大外层迭代次数
  max_inner_iter: 1     # 最大内层迭代次数
  max_time: 1.0         # 最大优化时间 (s)
  progress: 4.0         # 进度权重参数
  debug: false          # 是否启用调试输出
  parallel: true        # 是否启用并行计算
  smooth_mu: 1.0e-2     # 平滑性权重参数
  epsilon: 1.0e-5       # 收敛精度
  rho: 1.0e+5           # 惩罚参数
  damping: 0.95         # 阻尼系数
  obj_type: 1           # 目标函数类型 (0: 最小化距离, 1: 最小化能量)
  random_seed: -1       # 随机种子 (-1: 使用时间种子, >=0: 固定种子)
