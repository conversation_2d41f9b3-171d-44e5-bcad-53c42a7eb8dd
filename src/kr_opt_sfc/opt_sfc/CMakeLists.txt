cmake_minimum_required(VERSION 3.10)
project(opt_sfc)

set(CMAKE_CXX_FLAGS "-std=c++14")
set(CMAKE_BUILD_TYPE "Release")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -Wall -fPIC")


find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  geometry_msgs
  sensor_msgs
  visualization_msgs
  nav_msgs
  gcopter
  message_generation
)

find_package(Eigen3 REQUIRED)
find_package(ompl REQUIRED)

## Generate messages in the 'msg' folder
add_message_files(
  FILES
  TrajectoryTarget.msg
)

## Generate added messages and services with any dependencies listed here
generate_messages(
  DEPENDENCIES
  std_msgs
  geometry_msgs
)

include_directories(
    ${catkin_INCLUDE_DIRS}
    ${OMPL_INCLUDE_DIRS}
    ${EIGEN3_INCLUDE_DIRS}
    include
)

catkin_package(
  CATKIN_DEPENDS
  geometry_msgs nav_msgs gcopter message_runtime)

add_executable(sfc
  src/sfc.cpp
)
target_link_libraries(sfc
  ${catkin_LIBRARIES}
  ${OMPL_LIBRARIES}
  )

set_property(TARGET sfc 
  PROPERTY CXX_STANDARD 14)
