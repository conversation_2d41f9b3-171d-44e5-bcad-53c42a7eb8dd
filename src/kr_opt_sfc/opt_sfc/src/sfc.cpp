#include "gcopter/flatness.hpp"
#include "gcopter/voxel_map.hpp"

#include <ros/console.h>
#include <sensor_msgs/PointCloud2.h>
#include <nav_msgs/Path.h>
#include <opt_sfc/TrajectoryTarget.h>

#include "sfc/sfc_cover_opt.hpp"
#include "sfc/plan_path.hpp"

#include <iostream>
#include <chrono>

class SFCServer
{
private:
    
    //ros related
    ros::NodeHandle nh;
    ros::Subscriber mapSub, targetSub;
    ros::Publisher trajectoryPub;  // 轨迹发布器

    std::string mapTopic, targetTopic;

    bool mapInitialized;
    voxel_map::VoxelMap voxelMap;
    sfc_vis visualizer;
    std::vector<Eigen::Vector3d> startGoal;
    
    // 新增：存储起点和终点的完整状态信息
    Eigen::Vector3d start_position, start_velocity, start_acceleration;
    Eigen::Vector3d goal_position, goal_velocity, goal_acceleration;
    bool trajectory_target_received;
    bool enable_visualization;

    // 规划计数器和第一次规划结果存储
    int planning_counter;
    std::vector<Eigen::Vector3d> first_route;
    std::vector<Eigen::MatrixX4d> first_hpolys;
    Trajectory<5> first_trajectory;
    std::vector<Eigen::Matrix3d> first_ellipsoid_Rs;
    std::vector<Eigen::Vector3d> first_ellipsoid_ds;
    std::vector<Eigen::Vector3d> first_ellipsoid_rs;

    double dilateRadius;
    double voxelWidth;
    std::vector<double> mapBound;
    double inflateRange;

    sco::OptConvexCover opt_convex_cover;


public:
    SFCServer(ros::NodeHandle &nh_, ros::NodeHandle &nh_private)
        : nh(nh_), mapInitialized(false), opt_convex_cover(nh_private),
          trajectory_target_received(false), enable_visualization(true), planning_counter(0)
    {
        nh_private.getParam("MapTopic", mapTopic);
        nh_private.getParam("TargetTopic", targetTopic);
        nh_private.getParam("InflateRange", inflateRange);

        visualizer = sfc_vis(nh_private, 0);
        opt_convex_cover.setVisualizer(visualizer);


        Eigen::Vector3d map_size;
        mapBound.resize(6);

        nh_private.param("map/x_size", map_size(0), 40.0);
        nh_private.param("map/y_size", map_size(1), 40.0);
        nh_private.param("map/z_size", map_size(2), 5.0);
        nh_private.param("map/x_origin", mapBound[0], -20.0);
        nh_private.param("map/y_origin", mapBound[2], -20.0);
        nh_private.param("map/z_origin", mapBound[4], 0.0);
        nh_private.param("map/resolution", voxelWidth, 0.1);
        nh_private.param("map/inflate_radius", dilateRadius, 0.1);

        mapBound[1] = mapBound[0] + map_size(0);
        mapBound[3] = mapBound[2] + map_size(1);
        mapBound[5] = mapBound[4] + map_size(2);

        // 初始化状态向量
        start_position = Eigen::Vector3d::Zero();
        start_velocity = Eigen::Vector3d::Zero(); 
        start_acceleration = Eigen::Vector3d::Zero();
        goal_position = Eigen::Vector3d::Zero();
        goal_velocity = Eigen::Vector3d::Zero();
        goal_acceleration = Eigen::Vector3d::Zero();

        mapSub = nh.subscribe(mapTopic, 1, &SFCServer::mapCallBack, this,
                              ros::TransportHints().tcpNoDelay());

        targetSub = nh.subscribe(targetTopic, 1, &SFCServer::trajectoryTargetCallBack, this,
                                    ros::TransportHints().tcpNoDelay());

        // 初始化轨迹发布器
        trajectoryPub = nh.advertise<nav_msgs::Path>("/sfc/optimized_trajectory", 1); 

        std::cout << "SFC Planner Server Initialized !!!\n";
    }

    // 椭球体可视化函数（从sfc_cover_opt.hpp移过来）
    inline void visualizeEllipsoids(const std::vector<Eigen::Matrix3d> &Rs,
                                   const std::vector<Eigen::Vector3d> &ds,
                                   const std::vector<Eigen::Vector3d> &rs)
    {
        visualizer.visualizeEllipsoids(Rs, ds, rs);
    }


    inline void mapCallBack(const sensor_msgs::PointCloud2::ConstPtr &msg)
    {
        const Eigen::Vector3i xyz((mapBound[1] - mapBound[0]) / voxelWidth,
                                (mapBound[3] - mapBound[2]) / voxelWidth,
                                (mapBound[5] - mapBound[4]) / voxelWidth);

        const Eigen::Vector3d offset(mapBound[0], mapBound[2], mapBound[4]);

        voxelMap = voxel_map::VoxelMap(xyz, offset, voxelWidth);

        size_t cur = 0;
        const size_t total = msg->data.size() / msg->point_step;
        float *fdata = (float *)(&msg->data[0]);
        for (size_t i = 0; i < total; i++)
        {
            cur = msg->point_step / sizeof(float) * i;

            if (std::isnan(fdata[cur + 0]) || std::isinf(fdata[cur + 0]) ||
                std::isnan(fdata[cur + 1]) || std::isinf(fdata[cur + 1]) ||
                std::isnan(fdata[cur + 2]) || std::isinf(fdata[cur + 2]))
            {
                continue;
            }
            voxelMap.setOccupied(Eigen::Vector3d(fdata[cur + 0],
                                                    fdata[cur + 1],
                                                    fdata[cur + 2]));
        }

        voxelMap.dilate(std::ceil(dilateRadius / voxelMap.getScale()));

        mapInitialized = true;
        ROS_INFO("Voxel map initialized successfully");
    }

    // 将Trajectory转换为nav_msgs::Path并发布
    template<int D>
    inline void publishTrajectory(const Trajectory<D> &traj, const std::string &frame_id = "odom")
    {
        if (traj.getPieceNum() == 0)
        {
            ROS_ERROR("Empty trajectory, cannot publish!");
            return;
        }

        double total_time = traj.getTotalDuration();
        if (total_time <= 0 || !std::isfinite(total_time))
        {
            ROS_ERROR("Invalid trajectory duration: %.3f, cannot publish!", total_time);
            return;
        }

        nav_msgs::Path path_msg;
        path_msg.header.stamp = ros::Time::now();
        path_msg.header.frame_id = frame_id;

        // 轨迹采样参数
        double dt = 0.05;  // 采样间隔，可以设置为参数

        // 在轨迹上采样点
        for (double t = 0.0; t <= total_time; t += dt)
        {
            if (t > total_time) t = total_time;  // 确保包含最后一个点

            geometry_msgs::PoseStamped pose;
            pose.header.stamp = ros::Time::now();
            pose.header.frame_id = frame_id;

            // 获取位置
            Eigen::Vector3d pos = traj.getPos(t);
            pose.pose.position.x = pos(0);
            pose.pose.position.y = pos(1);
            pose.pose.position.z = pos(2);

            // 设置默认朝向（单位四元数）
            pose.pose.orientation.x = 0.0;
            pose.pose.orientation.y = 0.0;
            pose.pose.orientation.z = 0.0;
            pose.pose.orientation.w = 1.0;

            path_msg.poses.push_back(pose);
        }

        // 发布轨迹
        trajectoryPub.publish(path_msg);
        
        ROS_INFO("Published optimized trajectory with %zu poses, duration: %.2f seconds", 
                 path_msg.poses.size(), total_time);
    }

    inline void plan(voxel_map::VoxelMap &curMap)
    {
        // 检查是否有有效的轨迹目标
        if (!trajectory_target_received || startGoal.size() != 2)
        {
            ROS_WARN("No valid trajectory target received!");
            return;
        }

        // 更新规划计数器
        planning_counter++;

        // 当规划次数超过2次时重置
        if (planning_counter > 2) {
            planning_counter = 1;
            // 清空第一次规划的存储数据
            first_route.clear();
            first_hpolys.clear();
            first_ellipsoid_Rs.clear();
            first_ellipsoid_ds.clear();
            first_ellipsoid_rs.clear();
        }

        ROS_INFO("Planning counter: %d, Enable visualization: %s",
                 planning_counter, enable_visualization ? "true" : "false");



        Eigen::MatrixXd iniState(3, 3), finState(3, 3);

        iniState << start_position, start_velocity, start_acceleration;
        finState << goal_position, goal_velocity, goal_acceleration;
        
        printf("\033[32m ======================== Planning Started ======================== \033[0m\n");
        std::cout << "Start: pos=" << start_position.transpose() 
                    << ", vel=" << start_velocity.transpose() 
                    << ", acc=" << start_acceleration.transpose() << std::endl;
        std::cout << "Goal:  pos=" << goal_position.transpose() 
                    << ", vel=" << goal_velocity.transpose() 
                    << ", acc=" << goal_acceleration.transpose() << std::endl;

        /***step one: generate a init path***/
        std::vector<Eigen::Vector3d> route;
        plan_path::planPath<voxel_map::VoxelMap>(startGoal[0],
                                                startGoal[1],
                                                curMap.getOrigin(),
                                                curMap.getCorner(),
                                                &curMap, 0.1, 0.2,
                                                route, opt_convex_cover.random_seed);
        
        if (route.size() <= 2)
        {
            ROS_WARN("No Path Found or Not enough points!!!\n");
            return;
        }
        // 存储第一次规划的路径
        if (planning_counter == 1) {
            first_route = route;
        }

        /***step two: corridor generation***/
        std::vector<Eigen::Vector3d> pc;
        curMap.getSurf(pc);

        std::vector<Eigen::MatrixX4d> hpolys;
        std::vector<Eigen::Matrix3d> Rs;
        std::vector<Eigen::Vector3d> ds;
        std::vector<Eigen::Vector3d> rs;
        auto t1 = std::chrono::high_resolution_clock::now();
        //ros::Time t = ros::Time::now();
        
        printf("\033[32m ================ opt_convex_cover: \033[0m");
        opt_convex_cover.convexCover(iniState, finState, pc,
                                    curMap.getOrigin(), 
                                    curMap.getCorner(),             
                                    inflateRange,
                                    route, 
                                    hpolys);

        auto t2 = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double, std::milli> fp_ms = t2 - t1;
        std::cout << "convexCover time: " << fp_ms.count() << std::endl;
        //std::cout << "ros time: " << (ros::Time::now() - t).toSec() << std::endl;


        if (hpolys.size() == 0)
        {
            ROS_WARN("No Corridor Found !!!\n");
            return;
        }
        // 存储第一次规划的走廊
        if (planning_counter == 1) {
            first_hpolys = hpolys;
        }

        Eigen::VectorXd evals(4);
        std::vector<Eigen::Vector3d> inner_pts;
        bool valid = sfc_utils::evaluatehPolys(hpolys, startGoal[0], startGoal[1], evals, inner_pts);

        std::cout << "Eval: " << evals.transpose() << std::endl;

        if (valid == false)
        {
            ROS_ERROR("No valid corridor found! Planning failed.");
            return;
        }
        else
        {
            // 获取并发布最终优化轨迹
            Trajectory<5> optimized_traj;
            opt_convex_cover.getOptimizedTraj(optimized_traj);

            // 获取椭球体数据
            std::vector<Eigen::Matrix3d> current_Rs;
            std::vector<Eigen::Vector3d> current_ds;
            std::vector<Eigen::Vector3d> current_rs;
            opt_convex_cover.getEllipsoids(current_Rs, current_ds, current_rs);

            // 存储第一次规划的轨迹和椭球体数据
            if (planning_counter == 1) {
                first_trajectory = optimized_traj;
                first_ellipsoid_Rs = current_Rs;
                first_ellipsoid_ds = current_ds;
                first_ellipsoid_rs = current_rs;
            }

            // ==================== 统一可视化控制逻辑 ====================
            if (enable_visualization) {
                if (planning_counter == 1) {
                    // 第一次规划：可视化第一段轨迹的完整信息
                    ROS_INFO("Visualizing first segment trajectory...");

                    visualizer.clearAll();
                    visualizer.visualizeStartGoal(startGoal[0], 0.2, 1, Eigen::Vector3d(0.9, 0.8, 0.2));
                    visualizer.visualizeStartGoal(startGoal[1], 0.2, 2);
                    visualizer.visualizePath(route);
                    visualizer.visualizePolytope(hpolys);

                    std::vector<Eigen::Vector3d> current_wps;
                    if (optimized_traj.getPieceNum() > 0) {
                        double dt = optimized_traj.getTotalDuration() / (optimized_traj.getPieceNum() * 10);
                        for (double t = 0; t <= optimized_traj.getTotalDuration(); t += dt) {
                            current_wps.push_back(optimized_traj.getPos(t));
                        }
                        visualizer.visualize(optimized_traj, current_wps);
                    }
                    visualizeEllipsoids(current_Rs, current_ds, current_rs);

                } else if (planning_counter == 2) {
                    // 第二次规划：合并两段轨迹信息一起可视化
                    ROS_INFO("Visualizing merged trajectory results...");

                    visualizer.clearAll();

                    // 合并所有数据
                    std::vector<Eigen::Vector3d> merged_route = first_route;
                    merged_route.insert(merged_route.end(), route.begin(), route.end());

                    std::vector<Eigen::MatrixX4d> merged_hpolys = first_hpolys;
                    merged_hpolys.insert(merged_hpolys.end(), hpolys.begin(), hpolys.end());

                    std::vector<Eigen::Matrix3d> merged_Rs = first_ellipsoid_Rs;
                    std::vector<Eigen::Vector3d> merged_ds = first_ellipsoid_ds;
                    std::vector<Eigen::Vector3d> merged_rs = first_ellipsoid_rs;
                    merged_Rs.insert(merged_Rs.end(), current_Rs.begin(), current_Rs.end());
                    merged_ds.insert(merged_ds.end(), current_ds.begin(), current_ds.end());
                    merged_rs.insert(merged_rs.end(), current_rs.begin(), current_rs.end());

                    // 可视化合并后的完整结果
                    visualizer.visualizeStartGoal(startGoal[0], 0.2, 1, Eigen::Vector3d(0.9, 0.8, 0.2));
                    visualizer.visualizeStartGoal(startGoal[1], 0.2, 2);
                    visualizer.visualizePath(merged_route);
                    visualizer.visualizePolytope(merged_hpolys);

                    // 合并轨迹路径点
                    std::vector<Eigen::Vector3d> merged_wps;
                    if (first_trajectory.getPieceNum() > 0) {
                        double dt = first_trajectory.getTotalDuration() / (first_trajectory.getPieceNum() * 5);
                        for (double t = 0; t <= first_trajectory.getTotalDuration(); t += dt) {
                            merged_wps.push_back(first_trajectory.getPos(t));
                        }
                    }
                    if (optimized_traj.getPieceNum() > 0) {
                        double dt = optimized_traj.getTotalDuration() / (optimized_traj.getPieceNum() * 5);
                        for (double t = 0; t <= optimized_traj.getTotalDuration(); t += dt) {
                            merged_wps.push_back(optimized_traj.getPos(t));
                        }
                    }
                    visualizer.visualizePath(merged_wps);
                    visualizeEllipsoids(merged_Rs, merged_ds, merged_rs);
                }
            } else {
                // enable_visualization为false时，不进行任何可视化
                ROS_INFO("Visualization disabled for this planning cycle.");
            }
            // ==================== 可视化控制逻辑结束 ====================

            if (optimized_traj.getPieceNum() > 0)
            {
                publishTrajectory(optimized_traj, "odom");
                ROS_INFO("Successfully published optimized trajectory!");
            }
            else
            {
                ROS_WARN("Optimized trajectory is empty, cannot publish!");
            }
        }
        trajectory_target_received = false;

        return;
    }

    inline void trajectoryTargetCallBack(const opt_sfc::TrajectoryTarget::ConstPtr &msg)
    {
        if (mapInitialized)
        {
            ROS_INFO("Received trajectory target with complete state information");
            
            // 解析起点信息
            start_position << msg->start_position.x, msg->start_position.y, msg->start_position.z;
            start_velocity << msg->start_velocity.x, msg->start_velocity.y, msg->start_velocity.z;
            start_acceleration << msg->start_acceleration.x, msg->start_acceleration.y, msg->start_acceleration.z;
            
            // 解析终点信息
            goal_position << msg->goal_position.x, msg->goal_position.y, msg->goal_position.z;
            goal_velocity << msg->goal_velocity.x, msg->goal_velocity.y, msg->goal_velocity.z;
            goal_acceleration << msg->goal_acceleration.x, msg->goal_acceleration.y, msg->goal_acceleration.z;

            // 解析可视化标志
            enable_visualization = msg->enable_visualization;
            
            // 打印接收到的信息
            std::cout << "Start: pos=" << start_position.transpose() 
                      << ", vel=" << start_velocity.transpose() 
                      << ", acc=" << start_acceleration.transpose() << std::endl;
            std::cout << "Goal: pos=" << goal_position.transpose() 
                      << ", vel=" << goal_velocity.transpose() 
                      << ", acc=" << goal_acceleration.transpose() << std::endl;
            
            // 检查起点和终点是否可行
            if (voxelMap.query(start_position) != 0)
            {
                ROS_ERROR("Start position (%.2f, %.2f, %.2f) is occupied!", 
                         start_position.x(), start_position.y(), start_position.z());
                return;
            }
            
            if (voxelMap.query(goal_position) != 0)
            {
                ROS_ERROR("Goal position (%.2f, %.2f, %.2f) is occupied!", 
                         goal_position.x(), goal_position.y(), goal_position.z());
                return;
            }
            
            // 更新startGoal向量以兼容现有代码
            startGoal.clear();
            startGoal.push_back(start_position);
            startGoal.push_back(goal_position);
            
            trajectory_target_received = true;
            
            // 直接进行规划
            plan(voxelMap);
        }
        return;
    }
    
};

int main(int argc, char **argv)
{
    ros::init(argc, argv, "planning_node");
    ros::NodeHandle nh_, nh_private("~");
    SFCServer planner_server_utils(nh_, nh_private);

    ros::Rate lr(1000);
    while (ros::ok())
    {
        ros::spinOnce();
        lr.sleep();
    }

    return 0;
}

