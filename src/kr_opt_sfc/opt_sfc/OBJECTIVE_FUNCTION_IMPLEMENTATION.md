# SFC目标函数类型实现说明

## 概述
已成功实现了SFC轨迹规划器的目标函数类型选择功能，支持最小化距离和最小化能量两种优化策略。

## 实现的功能

### 1. 目标函数类型配置
在`sfc.yaml`配置文件中：
```yaml
obj_type: 0           # 目标函数类型 (0: 最小化距离, 1: 最小化能量)
```

### 2. 核心实现位置

#### A. 距离优化函数 (`obj_type: 0`)
- **函数**: `costDistance` (第500-607行)
- **目标**: 最小化路径总距离
- **特点**: 
  - 计算路径点之间的欧几里得距离
  - 优化速度快
  - 适合需要最短路径的场景

#### B. 能量优化函数 (`obj_type: 1`)
- **函数**: `costDistanceContorl` (第789-835行)
- **目标**: 最小化轨迹能量（平滑性）
- **特点**:
  - 使用MINCO轨迹表示
  - 计算轨迹的能量（加速度的积分）
  - 生成更平滑的轨迹
  - 适合需要平滑飞行的场景

#### C. 混合优化策略
- **函数**: `getEnergyHeuristics` (第837-898行)
- **策略**: 先用距离优化获得初始解，再用能量优化精细化
- **优势**: 结合了两种方法的优点

### 3. 函数调用流程

```
sfc_cover_opt.hpp: findOptWps()
    ↓
根据 obj_type 选择:
    ↓
obj_type == 0: getDistHeuristics() → costDistance()
obj_type == 1: getEnergyHeuristics() → costDistanceContorl()
```

### 4. 修改的文件

#### A. `sfc_utils_opt.hpp`
- 添加了`obj_type`参数到`getDistHeuristics`函数
- 实现了`getEnergyHeuristics`函数
- 在`getDistHeuristics`中添加了目标函数选择逻辑

#### B. `sfc_cover_opt.hpp`
- 修改了`findOptWps`函数，根据`obj_type`选择不同的优化策略
- 添加了详细的日志输出

#### C. `sfc.yaml`
- 更新了`obj_type`参数的注释说明

## 使用方法

### 1. 最小化距离 (默认)
```yaml
sfc_cover_opt:
  obj_type: 0
```
- 适用场景：需要最短路径，对轨迹平滑性要求不高
- 优势：计算速度快，路径距离最短
- 劣势：轨迹可能不够平滑

### 2. 最小化能量
```yaml
sfc_cover_opt:
  obj_type: 1
```
- 适用场景：需要平滑轨迹，对飞行舒适性要求高
- 优势：轨迹平滑，加速度变化小
- 劣势：计算时间稍长，路径可能不是最短

## 技术细节

### 1. 能量计算
能量优化使用MINCO轨迹表示，计算公式为：
```
Energy = ∫ ||a(t)||² dt
```
其中a(t)是轨迹的加速度。

### 2. 优化算法
两种目标函数都使用L-BFGS优化算法：
- 梯度容忍度：1e-4
- 收敛阈值：1e-3
- 最大迭代次数：100
- 最大线搜索次数：50

### 3. 混合策略
`getEnergyHeuristics`函数采用两阶段优化：
1. 第一阶段：使用距离优化获得初始路径
2. 第二阶段：基于初始路径进行能量优化

## 性能对比

| 目标函数类型 | 计算速度 | 路径长度 | 轨迹平滑性 | 适用场景 |
|-------------|----------|----------|------------|----------|
| 距离优化(0) | 快       | 最短     | 一般       | 快速导航 |
| 能量优化(1) | 中等     | 较长     | 最佳       | 平滑飞行 |

## 调试信息
当设置`debug: true`时，会输出：
- 选择的目标函数类型
- 优化过程的详细信息
- 轨迹成本和能量信息

## 注意事项
1. 能量优化需要更多计算资源，在实时性要求高的场景下需要权衡
2. 两种优化方法都会考虑障碍物约束和飞行走廊限制
3. 可以根据具体应用场景选择合适的目标函数类型
