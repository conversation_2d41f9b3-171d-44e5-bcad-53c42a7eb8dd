# TrajectoryTarget.msg
# 包含起点和终点的位置、速度、加速度信息

# 标准消息头
std_msgs/Header header

# 起点信息
geometry_msgs/Point start_position     # 起点位置
geometry_msgs/Vector3 start_velocity   # 起点速度
geometry_msgs/Vector3 start_acceleration  # 起点加速度

# 终点信息
geometry_msgs/Point goal_position      # 终点位置
geometry_msgs/Vector3 goal_velocity    # 终点速度
geometry_msgs/Vector3 goal_acceleration   # 终点加速度

# 可视化控制标志
bool enable_visualization              # 是否启用可视化