odom_topic: "/CERLAB/quadcopter/odom"
local_region_min: [-5, -5, -2]
local_region_max: [5, 5, 2]
global_region_min: [-20, -20, 0]
global_region_max: [20, 20, 3]
local_sample_thresh: 50
global_sample_thresh: 50
frontier_sample_thresh: 100
dist_thresh: 0.8
safe_distance_xy: 0.3
safe_distance_z: 0.0
safe_distance_check_unknown: true # true will make node sampling conservative but safe 

# Camera Parameters
horizontal_FOV: 1.57
vertical_FOV: 1.57
dmin: 0
dmax: 1.0

nearest_neighbor_number: 15
frontier_nearest_neighbor_number: 15
max_connect_dist: 1.5
num_yaw_angles: 32

# Goal Candidate
min_voxel_thresh: 0.5
min_goal_candidates: 3
max_goal_candidates: 5

information_gain_update_distance: 1.0
yaw_penalty_weight: 1.0