# Robot:
# collision_box: [1.0, 1.0, 0.6]
# collision_box: [0.8, 0.8, 0.4]
collision_box: [0.4, 0.4, 0.2]


# Environment:
env_box: [-100, 100, -100, 100, 1.0, 1.0] # xmin, xmax, ymin, ymax, zmin, zmax


# Planner parameters:
timeout: 0.1 #s
rrt_incremental_distance: 1.0 #m
rrt_connect_goal_ratio: 0.2
goal_reach_distance: 0.4 #m
map_resolution: 0.2 # this should be at least bigger or equal to the true map resolution
ignore_unknown: true
max_shortcut_dist: 5.0


# Planner Visualization:
vis_RRT: False # does not support for RRT*
vis_path: True

# RRT* parameter:
neighborhood_radius: 1.0 #m
max_num_neighbors: 10
