<launch>
  <node pkg="mockamap" type="mockamap_node" name="mockamap_node" output="screen">
  <param name="seed" type="int" value="511"/>
  <param name="update_freq" type="double" value="1.0"/>

  <!--  box edge length, unit meter-->
  <param name="resolution" type="double" value="0.1"/>

  <!-- map size unit meter-->
  <param name="x_length" type="int" value="10"/>
  <param name="y_length" type="int" value="10"/>
  <param name="z_length" type="int" value="4"/>

  <param name="type" type="int" value="2"/>
  <param name="width_min"         type="double" value="0.6"/>
  <param name="width_max"         type="double" value="1.5"/>
  <param name="obstacle_number"   type="int"    value="50"/>

  </node>
  <!-- Launch RViz with the demo configuration -->
  <node name="rviz" pkg="rviz" type="rviz"
        args="-d $(find mockamap)/config/rviz.rviz"
        />
</launch>
