<?xml version="1.0" encoding="utf-8"?>
<launch>

   <node name="rviz" pkg="rviz" type="rviz" output="screen"
    args="-d $(find gcopter)/config/global_planning.rviz"> 
  </node>
  
  <node name="pos_vel_plot" pkg="rqt_plot" type="rqt_plot" 
    args="/visualizer/speed /visualizer/total_thrust /visualizer/tilt_angle /visualizer/body_rate">
  </node>

  <node pkg="mockamap" type="mockamap_node" name="mockamap_node" output="screen">
  <param name="seed" type="int" value="1024"/>
  <param name="update_freq" type="double" value="1.0"/>
  <param name="resolution" type="double" value="0.25"/>
  <param name="x_length" type="int" value="50"/>
  <param name="y_length" type="int" value="50"/>
  <param name="z_length" type="int" value="5"/>
  <param name="type" type="int" value="1"/>
  <param name="complexity"    type="double" value="0.025"/>
  <param name="fill"          type="double" value="0.3"/>
  <param name="fractal"       type="int"    value="1"/>
  <param name="attenuation"   type="double" value="0.1"/>
  <remap from="/mock_map" to="/voxel_map"/>
  </node>
  
  <node pkg="gcopter" type="global_planning" name="global_planning_node" output="screen">
  <rosparam file="$(find gcopter)/config/global_planning.yaml" command="load" />
  </node>

</launch>
