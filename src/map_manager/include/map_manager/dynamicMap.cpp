/*
	FILE: dynamicMap.cpp
	--------------------------------------
	function definition of dynamic map
*/

#include <ros/ros.h> // 引入 ROS 的核心头文件
#include <map_manager/dynamicMap.h> // 引入 dynamicMap 类的头文件

namespace mapManager{ // 定义 mapManager 命名空间

    dynamicMap::dynamicMap(){
        // dynamicMap 的默认构造函数
        this->ns_ = "dynamic_map"; // 设置命名空间为 "dynamic_map"
        this->hint_ = "[dynamicMap]"; // 设置提示信息
    }

    dynamicMap::dynamicMap(const ros::NodeHandle& nh, bool freeMap){
        // dynamicMap 的带参数构造函数
        this->ns_ = "dynamic_map"; // 设置命名空间为 "dynamic_map"
        this->hint_ = "[dynamicMap]"; // 设置提示信息
        this->initMap(nh, freeMap); // 初始化地图

    }

    void dynamicMap::initMap(const ros::Node<PERSON>and<PERSON>& nh, bool freeMap){
        // 初始化地图
        this->nh_ = nh; // 保存传入的 ROS 节点句柄
        this->initParam(); // 初始化参数
        this->initPrebuiltMap(); // 初始化预构建地图
        // ros::Duration(0.2).sleep(); // 睡眠 0.2 秒   
        // this->initSdfMap(); // 初始化 SDF 地图 
        // ros::Duration(0.2).sleep(); // 睡眠 0.2 秒
        //this->loadSDFFromFile(this->savedsdfDir_);
        //this->saveSDFToFile(this->savedsdfDir_); // 保存 SDF 地图到文件
        this->registerPub(); // 注册发布器
        this->registerCallback(); // 注册回调函数
        this->detector_.reset(new onboardDetector::dynamicDetector(this->nh_)); // 初始化动态检测器
        if (freeMap){ // 如果启用了 freeMap 功能
            this->freeMapTimer_ = this->nh_.createTimer(ros::Duration(0.033), &dynamicMap::freeMapCB, this); // 创建定时器，每 33ms 调用一次 freeMapCB
        }
    }

    void dynamicMap::freeMapCB(const ros::TimerEvent&){
        // 定时器回调函数，用于更新自由区域
        std::vector<std::pair<Eigen::Vector3d, Eigen::Vector3d>> freeRegions; // 定义一个存储自由区域的列表
        std::vector<onboardDetector::box3D> dynamicBBoxes; // 定义一个存储动态障碍物的列表
        this->detector_->getDynamicObstacles(dynamicBBoxes); // 获取动态障碍物信息
        for (onboardDetector::box3D ob : dynamicBBoxes){ // 遍历每个动态障碍物
            Eigen::Vector3d lowerBound(ob.x - ob.x_width / 2 - 0.3, ob.y - ob.y_width / 2 - 0.3, 0.0); // 计算自由区域的下界
            Eigen::Vector3d upperBound(ob.x + ob.x_width / 2 + 0.3, ob.y + ob.y_width / 2 + 0.3, ob.z + ob.z_width + 0.3); // 计算自由区域的上界
            freeRegions.push_back(std::make_pair(lowerBound, upperBound)); // 将自由区域添加到列表中
        }
        this->freeRegions(freeRegions); // 更新自由区域
        this->updateFreeRegions(freeRegions); // 将自由区域更新到地图中
    }

    void dynamicMap::getDynamicObstacles(std::vector<Eigen::Vector3d>& obstaclePos,
                                         std::vector<Eigen::Vector3d>& obstacleVel,
                                         std::vector<Eigen::Vector3d>& obstacleSize){
        // 获取动态障碍物的信息，包括位置、速度和大小
        std::vector<onboardDetector::box3D> dynamicBBoxes; // 定义一个存储动态障碍物的列表
        this->detector_->getDynamicObstacles(dynamicBBoxes, this->robotSize_); // 获取动态障碍物信息
        for (size_t i = 0; i < dynamicBBoxes.size(); ++i){ // 遍历每个动态障碍物
            Eigen::Vector3d pos(dynamicBBoxes[i].x, dynamicBBoxes[i].y, dynamicBBoxes[i].z); // 获取障碍物的位置
            Eigen::Vector3d vel(dynamicBBoxes[i].Vx, dynamicBBoxes[i].Vy, 0); // 获取障碍物的速度（假设 z 方向速度为 0）
            Eigen::Vector3d size(dynamicBBoxes[i].x_width, dynamicBBoxes[i].y_width, dynamicBBoxes[i].z_width); // 获取障碍物的尺寸
            obstaclePos.push_back(pos); // 将障碍物位置添加到列表中
            obstacleVel.push_back(vel); // 将障碍物速度添加到列表中
            obstacleSize.push_back(size); // 将障碍物大小添加到列表中
        }
    }

    std::shared_ptr<onboardDetector::dynamicDetector> dynamicMap::getDetector(){
        // 获取动态检测器的共享指针
        return this->detector_; // 返回动态检测器
    }

}
