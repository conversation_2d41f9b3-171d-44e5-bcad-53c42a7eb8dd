/*
	FILE: occupancyMap.cpp
	--------------------------------------
	function definition of occupancy map
*/
#include <map_manager/occupancyMap.h>
#include <memory> // 添加memory头文件以支持智能指针

namespace mapManager{
    occMap::occMap() {
        // 默认构造函数
        this->ns_ = "occupancy_map"; // 设置命名空间为 "occupancy_map"
        this->hint_ = "[OccMap]"; // 设置提示信息
    }

    occMap::occMap(const ros::NodeHandle& nh) : nh_(nh) {
        // 带参数的构造函数，初始化 ROS 节点句柄
        this->ns_ = "occupancy_map"; // 设置命名空间为 "occupancy_map"
        this->hint_ = "[OccMap]"; // 设置提示信息
        this->initParam(); // 初始化参数
        this->initPrebuiltMap(); // 初始化预构建地图
		this->registerPub(); // 注册发布器
        this->registerCallback(); // 注册回调函数
        //this->initSdfMap(); // 初始化 SDF 地图 
		//this->loadSDFFromFile(this->savedsdfDir_);
        //this->saveSDFToFile(this->savedsdfDir_); // 保存 SDF 地图到文件
    }

    void occMap::initMap(const ros::NodeHandle& nh) {
        // 初始化地图
        this->nh_ = nh; // 保存传入的 ROS 节点句柄
        this->initParam(); // 初始化参数
        this->initPrebuiltMap(); // 初始化预构建地图
        this->registerPub(); // 注册发布器
        this->registerCallback(); // 注册回调函数
        //this->initSdfMap(); // 初始化 SDF 地图 
		//this->loadSDFFromFile(this->savedsdfDir_);
        //this->saveSDFToFile(this->savedsdfDir_); // 保存 SDF 地图到文件
    }

	void occMap::initParam(){
		// 传感器输入模式
		if (not this->nh_.getParam(this->ns_ + "/sensor_input_mode", this->sensorInputMode_)){
			this->sensorInputMode_ = 0; // 表示深度图像
			cout << this->hint_ << ": No sensor input mode option. Use default: depth image" << endl;
		}
		else{ // 表示激光雷达
			cout << this->hint_ << ": Sensor input mode: depth image (0)/pointcloud (1). Your option: " << this->sensorInputMode_ << endl;
		}		

		// 判断是否设置定位模式
		if (not this->nh_.getParam(this->ns_ + "/localization_mode", this->localizationMode_)){
			this->localizationMode_ = 0;
			cout << this->hint_ << ": No localization mode option. Use default: pose" << endl;
		}
		else{
			cout << this->hint_ << ": Localizaiton mode: pose (0)/odom (1). Your option: " << this->localizationMode_ << endl;
		}

		// 深度图像话题名称
		if (not this->nh_.getParam(this->ns_ + "/depth_image_topic", this->depthTopicName_)){
			this->depthTopicName_ = "/camera/depth/image_raw";
			cout << this->hint_ << ": No depth image topic name. Use default: /camera/depth/image_raw" << endl;
		}
		else{
			cout << this->hint_ << ": Depth topic: " << this->depthTopicName_ << endl;
		}

		// 点云话题名称
		if (not this->nh_.getParam(this->ns_ + "/point_cloud_topic", this->pointcloudTopicName_)){
			this->pointcloudTopicName_ = "/camera/depth/points";
			cout << this->hint_ << ": No poincloud topic name. Use default: /camera/depth/points" << endl;
		}
		else{
			cout << this->hint_ << ": Pointcloud topic: " << this->pointcloudTopicName_ << endl;
		}

		// 设置定位模式
		if (this->localizationMode_ == 0){
			// 位置话题名称
			if (not this->nh_.getParam(this->ns_ + "/pose_topic", this->poseTopicName_)){
				this->poseTopicName_ = "/CERLAB/quadcopter/pose";
				cout << this->hint_ << ": No pose topic name. Use default: /CERLAB/quadcopter/pose" << endl;
			}
			else{
				cout << this->hint_ << ": Pose topic: " << this->poseTopicName_ << endl;
			}			
		}
		if (this->localizationMode_ == 1){
			// 里程计话题名称
			if (not this->nh_.getParam(this->ns_ + "/odom_topic", this->odomTopicName_)){
				this->odomTopicName_ = "/CERLAB/quadcopter/odom";
				cout << this->hint_ << ": No odom topic name. Use default: /CERLAB/quadcopter/odom" << endl;
			}
			else{
				cout << this->hint_ << ": Odom topic: " << this->odomTopicName_ << endl;
			}
		}

		// 无人机尺寸
		std::vector<double> robotSizeVec (3);
		if (not this->nh_.getParam(this->ns_ + "/robot_size", robotSizeVec)){
			robotSizeVec = std::vector<double>{0.5, 0.5, 0.3};
		}
		else{
			cout << this->hint_ << ": robot size: " << "[" << robotSizeVec[0]  << ", " << robotSizeVec[1] << ", "<< robotSizeVec[2] << "]" << endl;
		}
		this->robotSize_(0) = robotSizeVec[0]; 
		this->robotSize_(1) = robotSizeVec[1]; 
		this->robotSize_(2) = robotSizeVec[2];

		// 深度相机内参
		std::vector<double> depthIntrinsics (4);
		if (not this->nh_.getParam(this->ns_ + "/depth_intrinsics", depthIntrinsics)){
			cout << this->hint_ << ": Please check camera intrinsics!" << endl;
			exit(0);
		}
		else{
			this->fx_ = depthIntrinsics[0];
			this->fy_ = depthIntrinsics[1];
			this->cx_ = depthIntrinsics[2];
			this->cy_ = depthIntrinsics[3];
			cout << this->hint_ << ": fx, fy, cx, cy: " << "["  << this->fx_ << ", " << this->fy_  << ", " << this->cx_ << ", "<< this->cy_ << "]" << endl;
		}

		// 深度图像缩放因子
		if (not this->nh_.getParam(this->ns_ + "/depth_scale_factor", this->depthScale_)){
			this->depthScale_ = 1000.0;
			cout << this->hint_ << ": No depth scale factor. Use default: 1000." << endl;
		}
		else{
			cout << this->hint_ << ": Depth scale factor: " << this->depthScale_ << endl;
		}

		// 深度图像最小值
		if (not this->nh_.getParam(this->ns_ + "/depth_min_value", this->depthMinValue_)){
			this->depthMinValue_ = 0.2;
			cout << this->hint_ << ": No depth min value. Use default: 0.2 m." << endl;
		}
		else{
			cout << this->hint_ << ": Depth min value: " << this->depthMinValue_ << endl;
		}

		// 深度图像最大值
		if (not this->nh_.getParam(this->ns_ + "/depth_max_value", this->depthMaxValue_)){
			this->depthMaxValue_ = 5.0;
			cout << this->hint_ << ": No depth max value. Use default: 5.0 m." << endl;
		}
		else{
			cout << this->hint_ << ": Depth depth max value: " << this->depthMaxValue_ << endl;
		}

		// 深度图像过滤边界
		if (not this->nh_.getParam(this->ns_ + "/depth_filter_margin", this->depthFilterMargin_)){
			this->depthFilterMargin_ = 0;
			cout << this->hint_ << ": No depth filter margin. Use default: 0." << endl;
		}
		else{
			cout << this->hint_ << ": Depth filter margin: " << this->depthFilterMargin_ << endl;
		}

		// 深度图像跳过像素
		if (not this->nh_.getParam(this->ns_ + "/depth_skip_pixel", this->skipPixel_)){
			this->skipPixel_ = 1;
			cout << this->hint_ << ": No depth skip pixel. Use default: 1." << endl;
		}
		else{
			cout << this->hint_ << ": Depth skip pixel: " << this->skipPixel_ << endl;
		}

		// ------------------------------------------------------------------------------------
		// 深度图像列数
		if (not this->nh_.getParam(this->ns_ + "/image_cols", this->imgCols_)){
			this->imgCols_ = 640;
			cout << this->hint_ << ": No depth image columns. Use default: 640." << endl;
		}
		else{
			cout << this->hint_ << ": Depth image columns: " << this->imgCols_ << endl;
		}

		// 深度图像行数
		if (not this->nh_.getParam(this->ns_ + "/image_rows", this->imgRows_)){
			this->imgRows_ = 480;
			cout << this->hint_ << ": No depth image rows. Use default: 480." << endl;
		}
		else{
			cout << this->hint_ << ": Depth image rows: " << this->imgRows_ << endl;
		}
		this->projPoints_.resize(this->imgCols_ * this->imgRows_ / (this->skipPixel_ * this->skipPixel_));
		// ------------------------------------------------------------------------------------


		// 设置从机体坐标系到相机坐标系的变换矩阵
		std::vector<double> body2CamVec(16); // 定义一个大小为16的向量，用于存储变换矩阵的元素
		if (not this->nh_.getParam(this->ns_ + "/body_to_camera", body2CamVec)) {
			ROS_ERROR("[OccMap]: Please check body to camera matrix!"); // 如果未找到参数，输出错误信息
		} else {
			for (int i = 0; i < 4; ++i) { // 遍历矩阵的行
				for (int j = 0; j < 4; ++j) { // 遍历矩阵的列
					this->body2Cam_(i, j) = body2CamVec[i * 4 + j]; // 将向量中的值赋值到矩阵中
				}
			}
			// cout << this->hint_ << ": from body to camera: " << endl;
			// cout << this->body2Cam_ << endl; // 可选：打印变换矩阵
		}

		// 设置光线投射的最大长度
		if (not this->nh_.getParam(this->ns_ + "/raycast_max_length", this->raycastMaxLength_)) {
			this->raycastMaxLength_ = 5.0; // 如果未找到参数，设置默认值为5.0
			cout << this->hint_ << ": No raycast max length. Use default: 5.0." << endl;
		} else {
			cout << this->hint_ << ": Raycast max length: " << this->raycastMaxLength_ << endl; // 输出设置的值
		}

		// 设置命中概率（p_hit）
		double pHit;
		if (not this->nh_.getParam(this->ns_ + "/p_hit", pHit)) {
			pHit = 0.70; // 如果未找到参数，设置默认值为0.70
			cout << this->hint_ << ": No p hit. Use default: 0.70." << endl;
		} else {
			cout << this->hint_ << ": P hit: " << pHit << endl; // 输出设置的值
		}
		this->pHitLog_ = this->logit(pHit); // 将概率值转换为对数几率

		// 设置未命中概率（p_miss）
		double pMiss;
		if (not this->nh_.getParam(this->ns_ + "/p_miss", pMiss)) {
			pMiss = 0.35; // 如果未找到参数，设置默认值为0.35
			cout << this->hint_ << ": No p miss. Use default: 0.35." << endl;
		} else {
			cout << this->hint_ << ": P miss: " << pMiss << endl; // 输出设置的值
		}
		this->pMissLog_ = this->logit(pMiss); // 将概率值转换为对数几率

		// 设置最小概率（p_min）
		double pMin;
		if (not this->nh_.getParam(this->ns_ + "/p_min", pMin)) {
			pMin = 0.12; // 如果未找到参数，设置默认值为0.12
			cout << this->hint_ << ": No p min. Use default: 0.12." << endl;
		} else {
			cout << this->hint_ << ": P min: " << pMin << endl; // 输出设置的值
		}
		this->pMinLog_ = this->logit(pMin); // 将概率值转换为对数几率

		// 设置最大概率（p_max）
		double pMax;
		if (not this->nh_.getParam(this->ns_ + "/p_max", pMax)) {
			pMax = 0.97; // 如果未找到参数，设置默认值为0.97
			cout << this->hint_ << ": No p max. Use default: 0.97." << endl;
		} else {
			cout << this->hint_ << ": P max: " << pMax << endl; // 输出设置的值
		}
		this->pMaxLog_ = this->logit(pMax); // 将概率值转换为对数几率

		// 设置占用概率（p_occ）
		double pOcc;
		if (not this->nh_.getParam(this->ns_ + "/p_occ", pOcc)) {
			pOcc = 0.80; // 如果未找到参数，设置默认值为0.80
			cout << this->hint_ << ": No p occ. Use default: 0.80." << endl;
		} else {
			cout << this->hint_ << ": P occ: " << pOcc << endl; // 输出设置的值
		}
		this->pOccLog_ = this->logit(pOcc); // 将概率值转换为对数几率

		// 设置地图分辨率
		if (not this->nh_.getParam(this->ns_ + "/map_resolution", this->mapRes_)) {
			this->mapRes_ = 0.1; // 如果未找到参数，设置默认值为0.1
			cout << this->hint_ << ": No Volex map resolution. Use default: 0.1." << endl;
		} else {
			cout << this->hint_ << ": Volex Map resolution: " << this->mapRes_ << endl; // 输出设置的值
		}


		// 设置地面高度
		if (not this->nh_.getParam(this->ns_ + "/ground_height", this->groundHeight_)) {
			this->groundHeight_ = 0.0; // 如果未找到参数，设置默认值为0.0
			cout << this->hint_ << ": No ground height. Use default: 0.0." << endl;
		} else {
			cout << this->hint_ << ": Ground height: " << this->groundHeight_ << endl; // 输出设置的值
		}


		// 设置地图尺寸
		std::vector<double> mapSizeVec(3); // 定义一个大小为3的向量，用于存储地图尺寸
		if (not this->nh_.getParam(this->ns_ + "/map_size", mapSizeVec)) {
			// 如果未找到参数，设置默认地图尺寸为 [20, 20, 3]
			mapSizeVec[0] = 20; 
			mapSizeVec[1] = 20; 
			mapSizeVec[2] = 3;
			cout << this->hint_ << ": No map size. Use default: [20, 20, 3]." << endl;
		} else {
			// 如果找到参数，则使用提供的地图尺寸
			this->mapSize_(0) = mapSizeVec[0]; // 设置地图的 x 方向尺寸
			this->mapSize_(1) = mapSizeVec[1]; // 设置地图的 y 方向尺寸
			this->mapSize_(2) = mapSizeVec[2]; // 设置地图的 z 方向尺寸

			// 初始化地图的最小和最大边界
			this->mapSizeMin_(0) = -mapSizeVec[0] / 2; // x 方向最小边界
			this->mapSizeMax_(0) = mapSizeVec[0] / 2;  // x 方向最大边界
			this->mapSizeMin_(1) = -mapSizeVec[1] / 2; // y 方向最小边界
			this->mapSizeMax_(1) = mapSizeVec[1] / 2;  // y 方向最大边界
			this->mapSizeMin_(2) = this->groundHeight_; // z 方向最小边界
			this->mapSizeMax_(2) = this->groundHeight_ + mapSizeVec[2]; // z 方向最大边界

			// 初始化体素的最小和最大索引
			this->mapVoxelMin_(0) = 0; // x 方向最小体素索引
			this->mapVoxelMax_(0) = ceil(mapSizeVec[0] / this->mapRes_); // x 方向最大体素索引
			this->mapVoxelMin_(1) = 0; // y 方向最小体素索引
			this->mapVoxelMax_(1) = ceil(mapSizeVec[1] / this->mapRes_); // y 方向最大体素索引
			this->mapVoxelMin_(2) = 0; // z 方向最小体素索引
			this->mapVoxelMax_(2) = ceil(mapSizeVec[2] / this->mapRes_); // z 方向最大体素索引

			// 为地图变量分配内存
			int reservedSize = this->mapVoxelMax_(0) * this->mapVoxelMax_(1) * this->mapVoxelMax_(2); // 计算体素总数
			this->countHitMiss_.resize(reservedSize, 0); // 初始化命中和未命中计数
			this->countHit_.resize(reservedSize, 0); // 初始化命中计数
			this->occupancy_.resize(reservedSize, this->pMinLog_ - this->UNKNOWN_FLAG_); // 初始化占用概率
			this->occupancyInflated_.resize(reservedSize, false); // 初始化膨胀占用标志
			this->grid_map.resize(reservedSize, false); // 初始化地图占用标志
			this->flagTraverse_.resize(reservedSize, -1); // 初始化遍历标志
			this->flagRayend_.resize(reservedSize, -1); // 初始化射线终点标志

			// 输出地图尺寸信息
			cout << this->hint_ << ": Map size: " << "[" << mapSizeVec[0] << ", " << mapSizeVec[1] << ", " << mapSizeVec[2] << "]" << endl;
		}

		// 设置局部更新范围
		std::vector<double> localUpdateRangeVec; // 定义一个向量用于存储局部更新范围
		if (not this->nh_.getParam(this->ns_ + "/local_update_range", localUpdateRangeVec)) {
			// 如果未找到参数，设置默认局部更新范围为 [5.0, 5.0, 3.0]
			localUpdateRangeVec = std::vector<double>{5.0, 5.0, 3.0};
			cout << this->hint_ << ": No local update range. Use default: [5.0, 5.0, 3.0] m." << endl;
		} else {
			// 输出局部更新范围
			cout << this->hint_ << ": Local update range: " << "[" << localUpdateRangeVec[0] << ", " << localUpdateRangeVec[1] << ", " << localUpdateRangeVec[2] << "]" << endl;
		}
		// 设置局部更新范围的 x、y、z 方向值
		this->localUpdateRange_(0) = localUpdateRangeVec[0];
		this->localUpdateRange_(1) = localUpdateRangeVec[1];
		this->localUpdateRange_(2) = localUpdateRangeVec[2];

		// 设置局部边界膨胀因子
		if (not this->nh_.getParam(this->ns_ + "/local_bound_inflation", this->localBoundInflate_)) {
			this->localBoundInflate_ = 0.0; // 如果未找到参数，设置默认值为 0.0
			cout << this->hint_ << ": No local bound inflate. Use default: 0.0 m." << endl;
		} else {
			cout << this->hint_ << ": Local bound inflate: " << this->localBoundInflate_ << endl;
		}

		// 设置是否清理局部地图
		if (not this->nh_.getParam(this->ns_ + "/clean_local_map", this->cleanLocalMap_)) {
			this->cleanLocalMap_ = true; // 如果未找到参数，设置默认值为 true
			cout << this->hint_ << ": No clean local map option. Use default: true." << endl;
		} else {
			cout << this->hint_ << ": Clean local map option is set to: " << this->cleanLocalMap_ << endl;
		}

		// 若存在预先构建的点云地图文件，则获取其绝对路径
		if (not this->nh_.getParam(this->ns_ + "/prebuilt_map_directory", this->prebuiltMapDir_)) {
			this->prebuiltMapDir_ = ""; // 如果未找到参数，设置为空字符串
			cout << this->hint_ << ": Not using prebuilt map data." << endl;
		} else {
			// 获取该功能包的路径，并拼接预构建地图路径
			std::string autoFlightPkgPath = ros::package::getPath("planner");
			this->prebuiltMapDir_ = autoFlightPkgPath + this->prebuiltMapDir_;
			cout << this->hint_ << ": The prebuilt map absolute dir is found: " << this->prebuiltMapDir_ << endl;
		}

		// 若存在预先构建的sdf地图文件，则获取其绝对路径
		if (not this->nh_.getParam(this->ns_ + "/sdf_save_directory", this->savedsdfDir_)) {
			this->savedsdfDir_ = ""; // 如果未找到参数，设置为空字符串
			cout << this->hint_ << ": Not using saved sdf map data." << endl;
		} else {
			// 获取该功能包的路径，并拼接预构建地图路径
			std::string autoFlightPkgPath = ros::package::getPath("planner");
			this->savedsdfDir_ = autoFlightPkgPath + this->savedsdfDir_;
			cout << this->hint_ << ": The saved sdf map absolute dir is found: " << this->savedsdfDir_ << endl;
		}

		// 设置局部地图尺寸（用于可视化）
		std::vector<double> localMapSizeVec; // 定义一个向量用于存储局部地图尺寸
		if (not this->nh_.getParam(this->ns_ + "/local_map_size", localMapSizeVec)) {
			// 如果未找到参数，设置默认局部地图尺寸为 [10.0, 10.0, 2.0]
			localMapSizeVec = std::vector<double>{10.0, 10.0, 2.0};
			cout << this->hint_ << ": No local map size. Use default: [10.0, 10.0, 3.0] m." << endl;
		} else {
			// 输出局部地图尺寸
			cout << this->hint_ << ": Local map size: " << "[" << localMapSizeVec[0] << ", " << localMapSizeVec[1] << ", " << localMapSizeVec[2] << "]" << endl;
		}
		// 设置局部地图尺寸的 x、y、z 方向值
		this->localMapSize_(0) = localMapSizeVec[0] / 2;
		this->localMapSize_(1) = localMapSizeVec[1] / 2;
		this->localMapSize_(2) = localMapSizeVec[2] / 2;
		// 计算局部地图体素的 x、y、z 方向值
		this->localMapVoxel_(0) = int(ceil(localMapSizeVec[0] / (2 * this->mapRes_)));
		this->localMapVoxel_(1) = int(ceil(localMapSizeVec[1] / (2 * this->mapRes_)));
		this->localMapVoxel_(2) = int(ceil(localMapSizeVec[2] / (2 * this->mapRes_)));

		// 设置最大可视化高度
		if (not this->nh_.getParam(this->ns_ + "/max_height_visualization", this->maxVisHeight_)) {
			this->maxVisHeight_ = 3.0; // 如果未找到参数，设置默认值为 3.0
			cout << this->hint_ << ": No max visualization height. Use default: 3.0 m." << endl;
		} else {
			cout << this->hint_ << ": Max visualization height: " << this->maxVisHeight_ << endl;
		}

		// 设置是否可视化全局地图
		if (not this->nh_.getParam(this->ns_ + "/visualize_global_map", this->visGlobalMap_)) {
			this->visGlobalMap_ = false; // 如果未找到参数，设置默认值为 false（仅可视化局部地图）
			cout << this->hint_ << ": No visualize map option. Use default: visualize local map." << endl;
		} else {
			cout << this->hint_ << ": Visualize map option. local (0)/global (1): " << this->visGlobalMap_ << endl;
		}

		// 日志显示
		if (not this->nh_.getParam(this->ns_ + "/verbose", this->verbose_)){
			this->verbose_ = true;
			cout << this->hint_ << ": No verbose option. Use default: check update info." << endl;
		}
		else{
			if (not this->verbose_){
				cout << this->hint_ << ": Not display messages" << endl;
			}
			else{
				cout << this->hint_ << ": Display messages" << endl;
			}
		}


	}

	// 设置预构建的点云地图
	void occMap::initPrebuiltMap() {
		// 创建一个指向点云的智能指针，用于存储预构建地图的点云数据
		pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
	
		// 尝试加载预构建地图文件
		if (pcl::io::loadPCDFile<pcl::PointXYZ>(this->prebuiltMapDir_, *cloud) == -1) { 
			// 如果加载失败，输出提示信息
			cout << this->hint_ << ": No prebuilt map found/not using the prebuilt map." << endl;
		} else {
			// 如果加载成功，输出地图点的数量
			cout << this->hint_ << ": Map loaded with " << cloud->width * cloud->height << " data points. " << endl;
	
			int address; // 用于存储点在地图中的地址
			Eigen::Vector3i pointIndex; // 用于存储点的索引
			Eigen::Vector3d pointPos; // 用于存储点的坐标
			Eigen::Vector3i inflateIndex; // 用于存储膨胀后的索引
			int inflateAddress; // 用于存储膨胀后的地址
			int occ_Address; // 用于存储占用地址
	
			// 计算膨胀尺寸（x、y、z方向）
			int xInflateSize = ceil(this->robotSize_(0) / (2 * this->mapRes_));
			int yInflateSize = ceil(this->robotSize_(1) / (2 * this->mapRes_));
			int zInflateSize = ceil(this->robotSize_(2) / (2 * this->mapRes_));
	
			// 初始化当前地图范围的最小值和最大值
			Eigen::Vector3d currMapRangeMin(0.0, 0.0, 0.0);
			Eigen::Vector3d currMapRangeMax(0.0, 0.0, 0.0);
	
			// 计算地图的最大索引
			const int maxIndex = this->mapVoxelMax_(0) * this->mapVoxelMax_(1) * this->mapVoxelMax_(2);
	
			// 遍历点云中的每个点
			for (const auto& point : *cloud) {
				// 将点的坐标转换为地图中的地址
				address = this->posToAddress(point.x, point.y, point.z);
				pointPos(0) = point.x; 
				pointPos(1) = point.y; 
				pointPos(2) = point.z;
				this->posToIndex(pointPos, pointIndex); // 将点的坐标转换为索引
	
				// 将点标记为占用状态
				this->occupancy_[address] = this->pMaxLog_;
	
				// 更新地图范围的最小值和最大值
				if (pointPos(0) < currMapRangeMin(0)) {
					currMapRangeMin(0) = pointPos(0);
				}
				if (pointPos(0) > currMapRangeMax(0)) {
					currMapRangeMax(0) = pointPos(0);
				}
				if (pointPos(1) < currMapRangeMin(1)) {
					currMapRangeMin(1) = pointPos(1);
				}
				if (pointPos(1) > currMapRangeMax(1)) {
					currMapRangeMax(1) = pointPos(1);
				}
				if (pointPos(2) < currMapRangeMin(2)) {
					currMapRangeMin(2) = pointPos(2);
				}
				if (pointPos(2) > currMapRangeMax(2)) {
					currMapRangeMax(2) = pointPos(2);
				}

				occ_Address = this->indexToAddress(pointIndex); // 获取点的占用地址
				if ((occ_Address >= 0) and (occ_Address <= maxIndex)) {
					this->grid_map[occ_Address] = true;
				}
	
				// 根据无人机尺寸对点进行膨胀处理
				for (int ix = -xInflateSize; ix <= xInflateSize; ++ix) {
					for (int iy = -yInflateSize; iy <= yInflateSize; ++iy) {
						for (int iz = -zInflateSize; iz <= zInflateSize; ++iz) {
							inflateIndex(0) = pointIndex(0) + ix;
							inflateIndex(1) = pointIndex(1) + iy;
							inflateIndex(2) = pointIndex(2) + iz;
							inflateAddress = this->indexToAddress(inflateIndex);
	
							// 如果膨胀后的点超出地图范围，则跳过
							if ((inflateAddress < 0) or (inflateAddress > maxIndex)) {
								continue;
							}
							// 将膨胀后的点标记为占用状态
							this->occupancyInflated_[inflateAddress] = true;
						}
					}
				}
			}
	
			// 更新当前地图范围的最小值和最大值
			this->currMapRangeMin_ = currMapRangeMin;
			this->currMapRangeMax_ = currMapRangeMax;
		}
	}

	void occMap::initSdfMap() {
		// 检查sdf地图是否已经初始化
		if (this->sdf_map_.size() != 0) {
			cout << this->hint_ << ": SDF map already initialized" << endl;
			return;
		}
		
		cout << this->hint_ << ": Initialize SDF map..." << endl;
		int reservedSize = this->mapVoxelMax_(0) * this->mapVoxelMax_(1) * this->mapVoxelMax_(2);
		// 检查地图是否已经初始化
		if (reservedSize != 0) {
			this->sdf_map_.resize(reservedSize, 10000.0);
			cout << this->hint_ << ": SDF map space has been allocated, size:" << reservedSize << endl;
		}

		// 检查占用地图是否已经初始化
		if (this->grid_map.size() == 0) {
			cout << this->hint_ << ": Occupation map not initialized, unable to generate SDF map" << endl;
			return;
		}

		Eigen::Vector3i minRange = this->mapVoxelMin_;
		Eigen::Vector3i maxRange = this->mapVoxelMax_;

		this->esdfTemp1_.resize(reservedSize, 0.0);
		this->esdfTemp2_.resize(reservedSize, 0.0);
		this->esdfDistancePos_.resize(reservedSize, 10000.0);
		this->esdfDistanceNeg_.resize(reservedSize, 10000.0);

		cout << this->hint_ << ": Calculate positive distance transformation" << endl;
		auto start_time = std::chrono::high_resolution_clock::now();
		// positive DT (距离变换) - 计算到障碍物的距离
		for (int x=minRange(0); x<maxRange(0); x++){
			for (int y=minRange(1); y<maxRange(1); y++){
		  		this->fillESDF([&](int z){return this->grid_map[this->indexToAddress(x, y, z)] == true ? 0 : std::numeric_limits<double>::max();},
		      			 [&](int z, double val) {this->esdfTemp1_[this->indexToAddress(x, y, z)] = val;}, minRange(2), maxRange(2), 2);
			}
		}

		for (int x=minRange(0); x<maxRange(0); x++){
			for (int z=minRange(2); z<maxRange(2); z++){
				this->fillESDF([&](int y){return this->esdfTemp1_[this->indexToAddress(x, y, z)];},
					     [&](int y, double val){this->esdfTemp2_[this->indexToAddress(x, y, z)]=val;}, minRange(1), maxRange(1), 1);
			}
		}

		for (int y=minRange(1); y<maxRange(1); y++){
			for (int z=minRange(2); z<maxRange(2); z++){
		  		this->fillESDF([&](int x) {return this->esdfTemp2_[this->indexToAddress(x, y, z)];},
		           		 	   [&](int x, double val){this->esdfDistancePos_[this->indexToAddress(x, y, z)]=this->mapRes_ * std::sqrt(val);}, minRange(0), maxRange(0), 0);
			}
		}
		cout << this->hint_ << ": Calculate negative distance transformation" << endl;
		// negative DT (距离变换) - 计算到自由空间的距离
		for (int x=minRange(0); x<maxRange(0); x++){
			for (int y=minRange(1); y<maxRange(1); y++){
				this->fillESDF([&](int z){return this->grid_map[this->indexToAddress(x, y, z)] == false ? 0 : std::numeric_limits<double>::max();},
		  		[&](int z, double val){this->esdfTemp1_[this->indexToAddress(x, y, z)]=val;}, minRange(2), maxRange(2), 2);
			}
		}

		for (int x=minRange(0); x<maxRange(0); x++){
			for (int z=minRange(2); z<maxRange(2); z++){
				this->fillESDF([&](int y) {return this->esdfTemp1_[this->indexToAddress(x, y, z)];},
		       				   [&](int y, double val){this->esdfTemp2_[this->indexToAddress(x, y, z)]=val;}, minRange(1), maxRange(1), 1);
			}
		}

		for (int y=minRange(1); y<maxRange(1); y++){
			for (int z=minRange(2); z<maxRange(2); z++){
				this->fillESDF([&](int x){return this->esdfTemp2_[this->indexToAddress(x, y, z)];},
		       				   [&](int x, double val){this->esdfDistanceNeg_[this->indexToAddress(x, y, z)]=this->mapRes_ * std::sqrt(val);}, minRange(0), maxRange(0), 0);
			}
		}

		cout << this->hint_ << ": Merge distance transformation results" << endl;
		// 合并正负距离变换结果，创建带符号距离场
		int valid_cells = 0;
		double max_dist = 0.0;
		for (int x=minRange(0); x<maxRange(0); x++){
			for (int y=minRange(1); y<maxRange(1); y++){
				for (int z=minRange(2); z<maxRange(2); z++){
					int idx = this->indexToAddress(x, y, z);
					this->sdf_map_[idx] = this->esdfDistancePos_[idx] - this->mapRes_ / 2.0;

					if (this->esdfDistanceNeg_[idx] > 0.0){
				  		this->sdf_map_[idx] += (-this->esdfDistanceNeg_[idx] + this->mapRes_ / 2.0) ;
					}
					
					// 统计有效单元格和最大距离
					if (this->sdf_map_[idx] > 0.0) {
						valid_cells++;
						max_dist = std::max(max_dist, this->sdf_map_[idx]);
					}
				}
			}
		}

		auto end_time = std::chrono::high_resolution_clock::now();
		auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
		
		cout << this->hint_ << ": SDF map initialization completed! Time taken: " << duration << " ms" << std::endl;
		cout << this->hint_ << ": Valid cells: " << valid_cells << " ("  << 100.0 * valid_cells / reservedSize << "%), Maximum distance: " << max_dist << " m" << std::endl;
		
		// this->esdfTemp1_.clear();
		// this->esdfTemp2_.clear();
		// this->esdfDistancePos_.clear();
		// this->esdfDistanceNeg_.clear();
	} 

	void occMap::registerCallback() {
		// 根据传感器输入模式选择回调函数
		if (this->sensorInputMode_ == 0) { // 如果传感器输入模式为深度图像
			// 创建深度图像的订阅器
			this->depthSub_.reset(new message_filters::Subscriber<sensor_msgs::Image>(this->nh_, this->depthTopicName_, 50));
			if (this->localizationMode_ == 0) { // 如果定位模式为位姿
				// 创建位姿的订阅器
				this->poseSub_.reset(new message_filters::Subscriber<geometry_msgs::PoseStamped>(this->nh_, this->poseTopicName_, 25));
				// 创建了一个消息同步器，用于同步深度图像（depth）和位姿（pose）消息，确保不同来源的数据在时间上的一致性
				this->depthPoseSync_.reset(new message_filters::Synchronizer<depthPoseSync>(depthPoseSync(100), *this->depthSub_, *this->poseSub_));
				// 当上述的消息同步器成功配对了深度图像和位姿消息时，会调用这个回调函数来处理这些消息
				this->depthPoseSync_->registerCallback(boost::bind(&occMap::depthPoseCB, this, _1, _2));
			} else if (this->localizationMode_ == 1) { // 如果定位模式为里程计
				// 创建里程计的订阅器
				this->odomSub_.reset(new message_filters::Subscriber<nav_msgs::Odometry>(this->nh_, this->odomTopicName_, 25));
				// 创建深度图像和里程计的同步器
				this->depthOdomSync_.reset(new message_filters::Synchronizer<depthOdomSync>(depthOdomSync(100), *this->depthSub_, *this->odomSub_));
				// 注册深度图像和里程计的回调函数
				this->depthOdomSync_->registerCallback(boost::bind(&occMap::depthOdomCB, this, _1, _2));
			} else {
				// 如果定位模式无效，输出错误信息并退出程序
				ROS_ERROR("[OccMap]: Invalid localization mode!");
				exit(0);
			}
		} else if (this->sensorInputMode_ == 1) { // 如果传感器输入模式为雷达(点云)
			// 创建点云的订阅器
			this->pointcloudSub_.reset(new message_filters::Subscriber<sensor_msgs::PointCloud2>(this->nh_, this->pointcloudTopicName_, 50));
			if (this->localizationMode_ == 0) { // 如果定位模式为位姿
				// 创建位姿的订阅器
				this->poseSub_.reset(new message_filters::Subscriber<geometry_msgs::PoseStamped>(this->nh_, this->poseTopicName_, 25));
				// 创建点云和位姿的同步器
				this->pointcloudPoseSync_.reset(new message_filters::Synchronizer<pointcloudPoseSync>(pointcloudPoseSync(100), *this->pointcloudSub_, *this->poseSub_));
				// 注册点云和位姿的回调函数
				this->pointcloudPoseSync_->registerCallback(boost::bind(&occMap::pointcloudPoseCB, this, _1, _2));
			} else if (this->localizationMode_ == 1) { // 如果定位模式为里程计
				// 创建里程计的订阅器
				this->odomSub_.reset(new message_filters::Subscriber<nav_msgs::Odometry>(this->nh_, this->odomTopicName_, 25));
				// 创建点云和里程计的同步器
				this->pointcloudOdomSync_.reset(new message_filters::Synchronizer<pointcloudOdomSync>(pointcloudOdomSync(100), *this->pointcloudSub_, *this->odomSub_));
				// 注册点云和里程计的回调函数
				this->pointcloudOdomSync_->registerCallback(boost::bind(&occMap::pointcloudOdomCB, this, _1, _2));
			} else {
				// 如果定位模式无效，输出错误信息并退出程序
				ROS_ERROR("[OccMap]: Invalid localization mode!");
				exit(0);
			}
		} else {
			// 如果传感器输入模式无效，输出错误信息并退出程序
			ROS_ERROR("[OccMap]: Invalid sensor input mode!");
			exit(0);
		}
	
		// 注册占用地图更新的定时器回调函数，每 0.05 秒触发一次
		this->occTimer_ = this->nh_.createTimer(ros::Duration(0.05), &occMap::updateOccupancyCB, this);
	
		// 注册地图膨胀的定时器回调函数，每 0.05 秒触发一次
		this->inflateTimer_ = this->nh_.createTimer(ros::Duration(0.05), &occMap::inflateMapCB, this);
	
		// 注册可视化的定时器回调函数，每 0.1 秒触发一次
		this->visTimer_ = this->nh_.createTimer(ros::Duration(0.1), &occMap::visCB, this);
	
		// 启动一个线程用于可视化
		this->visWorker_ = std::thread(&occMap::startVisualization, this);
		this->visWorker_.detach(); // 将线程分离，使其独立运行
	}
	
	void occMap::registerPub() {
		// 注册深度点云的发布器，话题名称为 "/depth_cloud"
		this->depthCloudPub_ = this->nh_.advertise<sensor_msgs::PointCloud2>(this->ns_ + "/depth_cloud", 10);
	
		// 注册体素地图的发布器，话题名称为 "/voxel_map"
		this->mapVisPub_ = this->nh_.advertise<sensor_msgs::PointCloud2>(this->ns_ + "/voxel_map", 10);
	
		// 注册膨胀体素地图的发布器，话题名称为 "/inflated_voxel_map"
		this->inflatedMapVisPub_ = this->nh_.advertise<sensor_msgs::PointCloud2>(this->ns_ + "/inflated_voxel_map", 10);
	
		// 注册 2D 占用地图的发布器，话题名称为 "/2D_occupancy_map"
		this->map2DPub_ = this->nh_.advertise<nav_msgs::OccupancyGrid>(this->ns_ + "/2D_occupancy_map", 10);
	
		// 注册探索体素地图的发布器，话题名称为 "/explored_voxel_map"
		this->mapExploredPub_ = this->nh_.advertise<sensor_msgs::PointCloud2>(this->ns_ + "/explored_voxel_map", 10);
	
		// 注册服务，用于检查位置是否发生碰撞
		this->collisionCheckServer_ = this->nh_.advertiseService(this->ns_ + "/check_pos_collision", &occMap::checkCollision, this);
	
		// 注册服务，用于执行光线投射
		this->raycastServer_ = this->nh_.advertiseService(this->ns_ + "/raycast", &occMap::getRayCast, this);
	}

	bool occMap::checkCollision(map_manager::CheckPosCollision::Request& req, map_manager::CheckPosCollision::Response& res) {
		// 检查是否发生碰撞
		if (req.inflated) {
			// 如果请求中指定了膨胀区域，则检查膨胀后的占用状态
			res.occupied = this->isInflatedOccupied(Eigen::Vector3d(req.x, req.y, req.z));
		} else {
			// 否则检查普通占用状态
			res.occupied = this->isOccupied(Eigen::Vector3d(req.x, req.y, req.z));
		}
		return true; // 返回成功
	}
	
	// 执行光线投射（Ray Casting），用于在地图中从指定起点沿着特定方向投射光线，并返回光线与地图中障碍物的交点（命中点）。如果没有命中障碍物，则返回光线的终点
	bool occMap::getRayCast(map_manager::RayCast::Request& req, map_manager::RayCast::Response& res) {
		// 执行光线投射
		double hres = req.hres * M_PI / 180.0; // 水平分辨率（角度转弧度）
		int numHbeams = int(360 / req.hres); // 水平光束数量
		double vres = double(((req.vfov_max - req.vfov_min) * M_PI / 180.0) / (req.vbeams - 1)); // 垂直分辨率
		double vStartAngle = req.vfov_min * M_PI / 180.0; // 垂直起始角度
		int numVbeams = req.vbeams; // 垂直光束数量
		double range = req.range; // 光线投射的最大范围
		Eigen::Vector3d start(req.position.x, req.position.y, req.position.z); // 光线起点
	
		double starthAngle = req.startAngle; // 水平起始角度
		for (int h = 0; h < numHbeams; ++h) {
			// 遍历每个水平光束
			double hAngle = starthAngle + double(h) * hres; // 当前水平角度
			Eigen::Vector3d hdirection(cos(hAngle), sin(hAngle), 0.0); // 水平方向向量
			for (int v = 0; v < numVbeams; ++v) {
				// 遍历每个垂直光束
				double vAngle = vStartAngle + double(v) * vres; // 当前垂直角度
				double vup = tan(vAngle); // 垂直方向的增量
				Eigen::Vector3d direction = hdirection; // 初始化方向向量
				direction(2) += vup; // 添加垂直方向分量
				direction /= direction.norm(); // 归一化方向向量
				Eigen::Vector3d hitPoint; // 存储命中点
				bool success = this->castRay(start, direction, hitPoint, range, true); // 执行光线投射
				if (not success) {
					// 如果光线未命中，将命中点设置为光线终点
					hitPoint = start + range * direction;
				}
				for (int i = 0; i < 3; ++i) {
					// 将命中点的坐标添加到响应中
					res.points.push_back(hitPoint(i));
				}
			}
		}
		return true; // 返回成功
	}
	
	void occMap::depthPoseCB(const sensor_msgs::ImageConstPtr& img, const geometry_msgs::PoseStampedConstPtr& pose) {
		// 深度图像和位姿的回调函数
		cv_bridge::CvImagePtr imgPtr = cv_bridge::toCvCopy(img, img->encoding); // 将 ROS 图像消息转换为 OpenCV 图像
		if (img->encoding == sensor_msgs::image_encodings::TYPE_32FC1) {
			// 如果图像编码为 32 位浮点型，将其转换为 16 位无符号整型
			(imgPtr->image).convertTo(imgPtr->image, CV_16UC1, this->depthScale_);
		}
		imgPtr->image.copyTo(this->depthImage_); // 将深度图像存储到成员变量中
	
		Eigen::Matrix4d camPoseMatrix; // 存储相机的位姿矩阵
		this->getCameraPose(pose, camPoseMatrix); // 获取相机的位姿
	
		// 提取相机的位置和方向
		this->position_(0) = camPoseMatrix(0, 3);
		this->position_(1) = camPoseMatrix(1, 3);
		this->position_(2) = camPoseMatrix(2, 3);
		this->orientation_ = camPoseMatrix.block<3, 3>(0, 0);
	
		if (this->isInMap(this->position_)) {
			// 如果相机位置在地图范围内，设置需要更新占用地图的标志
			this->occNeedUpdate_ = true;
		} else {
			// 否则设置为不需要更新
			this->occNeedUpdate_ = false;
		}
	}

	void occMap::depthOdomCB(const sensor_msgs::ImageConstPtr& img, const nav_msgs::OdometryConstPtr& odom) {
		// 深度图像和里程计的回调函数
	
		// 将 ROS 图像消息转换为 OpenCV 图像
		cv_bridge::CvImagePtr imgPtr = cv_bridge::toCvCopy(img, img->encoding);
		if (img->encoding == sensor_msgs::image_encodings::TYPE_32FC1) {
			// 如果图像编码为 32 位浮点型，将其转换为 16 位无符号整型
			(imgPtr->image).convertTo(imgPtr->image, CV_16UC1, this->depthScale_);
		}
		imgPtr->image.copyTo(this->depthImage_); // 将深度图像存储到成员变量中
	
		// 存储当前相机的位姿（位置和方向）
		Eigen::Matrix4d camPoseMatrix; // 定义 4x4 的相机位姿矩阵
		this->getCameraPose(odom, camPoseMatrix); // 从里程计中获取相机位姿
	
		// 提取相机的位置
		this->position_(0) = camPoseMatrix(0, 3); // x 坐标
		this->position_(1) = camPoseMatrix(1, 3); // y 坐标
		this->position_(2) = camPoseMatrix(2, 3); // z 坐标
	
		// 提取相机的方向（旋转矩阵）
		this->orientation_ = camPoseMatrix.block<3, 3>(0, 0);
	
		// 检查相机位置是否在地图范围内
		if (this->isInMap(this->position_)) {
			this->occNeedUpdate_ = true; // 如果在范围内，设置需要更新占用地图的标志
		} else {
			this->occNeedUpdate_ = false; // 否则设置为不需要更新
		}
	}
	
	void occMap::pointcloudPoseCB(const sensor_msgs::PointCloud2ConstPtr& pointcloud, const geometry_msgs::PoseStampedConstPtr& pose) {
		// 点云和位姿的回调函数
	
		// 将 ROS 点云消息转换为 PCL 点云
		pcl::PCLPointCloud2 pclPC2;
		pcl_conversions::toPCL(*pointcloud, pclPC2); // 转换为 PCL 格式的点云
		pcl::fromPCLPointCloud2(pclPC2, this->pointcloud_); // 转换为 PCL 点云对象
	
		// 存储当前相机的位姿（位置和方向）
		Eigen::Matrix4d camPoseMatrix; // 定义 4x4 的相机位姿矩阵
		this->getCameraPose(pose, camPoseMatrix); // 从位姿消息中获取相机位姿
	
		// 提取相机的位置
		this->position_(0) = camPoseMatrix(0, 3); // x 坐标
		this->position_(1) = camPoseMatrix(1, 3); // y 坐标
		this->position_(2) = camPoseMatrix(2, 3); // z 坐标
	
		// 提取相机的方向（旋转矩阵）
		this->orientation_ = camPoseMatrix.block<3, 3>(0, 0);
	
		// 检查相机位置是否在地图范围内
		if (this->isInMap(this->position_)) {
			this->occNeedUpdate_ = true; // 如果在范围内，设置需要更新占用地图的标志
		} else {
			this->occNeedUpdate_ = false; // 否则设置为不需要更新
		}
	}
	
	void occMap::pointcloudOdomCB(const sensor_msgs::PointCloud2ConstPtr& pointcloud, const nav_msgs::OdometryConstPtr& odom) {
		// 点云和里程计的回调函数
	
		// 将 ROS 点云消息转换为 PCL 点云
		pcl::PCLPointCloud2 pclPC2;
		pcl_conversions::toPCL(*pointcloud, pclPC2); // 转换为 PCL 格式的点云
		pcl::fromPCLPointCloud2(pclPC2, this->pointcloud_); // 转换为 PCL 点云对象
	
		// 存储当前相机的位姿（位置和方向）
		Eigen::Matrix4d camPoseMatrix; // 定义 4x4 的相机位姿矩阵
		this->getCameraPose(odom, camPoseMatrix); // 从里程计中获取相机位姿
	
		// 提取相机的位置
		this->position_(0) = camPoseMatrix(0, 3); // x 坐标
		this->position_(1) = camPoseMatrix(1, 3); // y 坐标
		this->position_(2) = camPoseMatrix(2, 3); // z 坐标
	
		// 提取相机的方向（旋转矩阵）
		this->orientation_ = camPoseMatrix.block<3, 3>(0, 0);
	
		// 检查相机位置是否在地图范围内
		if (this->isInMap(this->position_)) {
			this->occNeedUpdate_ = true; // 如果在范围内，设置需要更新占用地图的标志
		} else {
			this->occNeedUpdate_ = false; // 否则设置为不需要更新
		}
	}

	void occMap::updateOccupancyCB(const ros::TimerEvent&) {
		// 占用地图更新的定时器回调函数
	
		if (not this->occNeedUpdate_) {
			// 如果不需要更新占用地图，则直接返回
			return;
		}
	
		ros::Time startTime, endTime; // 定义开始时间和结束时间变量
		startTime = ros::Time::now(); // 获取当前时间作为开始时间
	
		if (this->sensorInputMode_ == 0) {
			// 如果传感器输入模式为深度图像，则从深度图像投影3D点
			this->projectDepthImage();
		} else if (this->sensorInputMode_ == 1) {
			// 如果传感器输入模式为点云，则直接获取点云数据
			this->getPointcloud();
		}
	
		// 执行光线投射并更新占用地图
		this->raycastUpdate();
	
		// 清理局部地图
		if (this->cleanLocalMap_) {
			this->cleanLocalMap();
		}
	
		// 地图膨胀（当前注释掉）
		// this->inflateLocalMap();
	
		endTime = ros::Time::now(); // 获取当前时间作为结束时间
	
		if (this->verbose_) {
			// 如果启用了详细日志，则输出占用地图更新所需的时间
			cout << this->hint_ << ": Occupancy update time: " << (endTime - startTime).toSec() << " s." << endl;
		}
	
		this->occNeedUpdate_ = false; // 重置占用地图更新标志
		this->mapNeedInflate_ = true; // 设置地图膨胀标志
	}
	
	void occMap::inflateMapCB(const ros::TimerEvent&) {
		// 地图膨胀的定时器回调函数
	
		if (this->mapNeedInflate_) {
			// 如果需要膨胀地图，则执行膨胀操作
			this->inflateLocalMap();
			this->mapNeedInflate_ = false; // 重置地图膨胀标志
			this->esdfNeedUpdate_ = true; // 设置ESDF更新标志
		}
	}
	
	// 从深度图像中投影出3D点云，并将这些点从相机坐标系转换到地图坐标系
	void occMap::projectDepthImage() {
		// 从深度图像投影3D点
	
		this->projPointsNum_ = 0; // 初始化投影点数量为0
	
		int cols = this->depthImage_.cols; // 获取深度图像的列数
		int rows = this->depthImage_.rows; // 获取深度图像的行数
		uint16_t* rowPtr; // 定义指向深度图像行的指针
	
		Eigen::Vector3d currPointCam, currPointMap; // 定义相机坐标系和地图坐标系下的点
		double depth; // 定义深度值
		const double inv_factor = 1.0 / this->depthScale_; // 深度缩放因子的倒数，用于将深度图像的编码值转换为实际深度。
		const double inv_fx = 1.0 / this->fx_; // 相机内参fx的倒数
		const double inv_fy = 1.0 / this->fy_; // 相机内参fy的倒数
	
		// 遍历深度图像中的每个像素
		for (int v = this->depthFilterMargin_; v < rows - this->depthFilterMargin_; v += this->skipPixel_) { // 遍历行
			rowPtr = this->depthImage_.ptr<uint16_t>(v) + this->depthFilterMargin_; // 获取当前行的指针
			for (int u = this->depthFilterMargin_; u < cols - this->depthFilterMargin_; u += this->skipPixel_) { // 遍历列
				depth = (*rowPtr) * inv_factor; // 计算深度值
	
				if (*rowPtr == 0) {
					// 如果深度值为0，则将深度设置为光线投射的最大长度
					depth = this->raycastMaxLength_ + 0.1;
				} else if (depth < this->depthMinValue_) {
					// 如果深度值小于最小深度值，则跳过
					continue;
				} else if (depth > this->depthMaxValue_) {
					// 如果深度值大于最大深度值，则将深度设置为光线投射的最大长度
					depth = this->raycastMaxLength_ + 0.1;
				}
	
				rowPtr = rowPtr + this->skipPixel_; // 移动到下一个像素
	
				// 计算相机坐标系下的3D点
				currPointCam(0) = (u - this->cx_) * depth * inv_fx;
				currPointCam(1) = (v - this->cy_) * depth * inv_fy;
				currPointCam(2) = depth;
	
				// 将点从相机坐标系转换到地图坐标系
				currPointMap = this->orientation_ * currPointCam + this->position_;
	
				if (this->useFreeRegions_) {
					// 如果启用了自由区域，则检查点是否在自由区域内
					if (this->isInHistFreeRegions(currPointMap)) {
						continue; // 如果在自由区域内，则跳过
					}
				}
	
				// 存储当前点到投影点数组中
				this->projPoints_[this->projPointsNum_] = currPointMap;
				this->projPointsNum_ = this->projPointsNum_ + 1; // 更新投影点数量
			}
		}
	}

	void occMap::getPointcloud() {
		// 从点云数据中获取投影点
	
		this->projPointsNum_ = this->pointcloud_.size(); // 获取点云中的点数量
		this->projPoints_.resize(this->projPointsNum_); // 调整投影点数组的大小以匹配点云点数量
	
		Eigen::Vector3d currPointCam, currPointMap; // 定义变量用于存储相机坐标系和地图坐标系下的点
	
		// 遍历点云中的每个点
		for (int i = 0; i < this->projPointsNum_; ++i) {
			currPointCam(0) = this->pointcloud_.points[i].x; // 获取点的 x 坐标（相机坐标系）
			currPointCam(1) = this->pointcloud_.points[i].y; // 获取点的 y 坐标（相机坐标系）
			currPointCam(2) = this->pointcloud_.points[i].z; // 获取点的 z 坐标（相机坐标系）
	
			// 将点从相机坐标系转换到地图坐标系
			currPointMap = this->orientation_ * currPointCam + this->position_;
	
			// 检查点与相机位置的距离是否大于等于 0.5
			if ((currPointMap - this->position_).norm() >= 0.5) {
				this->projPoints_[i] = currPointMap; // 如果满足条件，将点存储到投影点数组中
			}
		}
	}

	// 更新占用地图（Occupancy Map）的光线投射信息。它通过投影点来更新地图中体素的占用状态，并处理光线投射路径上的体素
	void occMap::raycastUpdate() {
		// 更新占用地图的光线投射函数
	
		if (this->projPointsNum_ == 0) {
			// 如果没有投影点，则直接返回
			return;
		}
		this->raycastNum_ += 1; // 增加光线投射的计数器
	
		// 初始化局部更新范围的最小值和最大值
		double xmin, xmax, ymin, ymax, zmin, zmax;
		xmin = xmax = this->position_(0); // 初始化为当前位置的 x 坐标
		ymin = ymax = this->position_(1); // 初始化为当前位置的 y 坐标
		zmin = zmax = this->position_(2); // 初始化为当前位置的 z 坐标
	
		// 遍历每个投影点，执行光线投射并更新占用地图
		Eigen::Vector3d currPoint; // 当前点
		bool pointAdjusted; // 标志当前点是否被调整
		int rayendVoxelID, raycastVoxelID; // 光线终点和光线路径的体素 ID
		double length; // 当前点与相机位置的距离
	
		for (int i = 0; i < this->projPointsNum_; ++i) {
			currPoint = this->projPoints_[i]; // 获取当前投影点
			if (std::isnan(currPoint(0)) || std::isnan(currPoint(1)) || std::isnan(currPoint(2))) {
				// 如果当前点包含 NaN 值，则跳过
				continue;
			}
	
			pointAdjusted = false; // 初始化为未调整状态
	
			// 检查当前点是否在地图范围内
			if (!this->isInMap(currPoint)) {
				currPoint = this->adjustPointInMap(currPoint); // 调整点到地图范围内
				pointAdjusted = true; // 标记为已调整
			}
	
			// 检查当前点是否超过光线投射的最大长度
			length = (currPoint - this->position_).norm(); // 计算距离
			if (length > this->raycastMaxLength_) {
				currPoint = this->adjustPointRayLength(currPoint); // 调整点到最大长度范围内
				pointAdjusted = true; // 标记为已调整
			}
	
			// 更新局部更新范围
			xmin = std::min(xmin, currPoint(0));
			ymin = std::min(ymin, currPoint(1));
			zmin = std::min(zmin, currPoint(2));
			xmax = std::max(xmax, currPoint(0));
			ymax = std::max(ymax, currPoint(1));
			zmax = std::max(zmax, currPoint(2));
	
			// 更新占用信息
			rayendVoxelID = this->updateOccupancyInfo(currPoint, !pointAdjusted); // 如果点未调整，则标记为占用
	
			// 检查光线终点体素是否已更新
			if (this->flagRayend_[rayendVoxelID] == this->raycastNum_) {
				continue; // 如果已更新，则跳过
			} else {
				this->flagRayend_[rayendVoxelID] = this->raycastNum_; // 标记为已更新
			}
	
			// 执行光线投射以更新占用地图
			this->raycaster_.setInput(currPoint / this->mapRes_, this->position_ / this->mapRes_); // 设置光线投射的起点和终点
			Eigen::Vector3d rayPoint, actualPoint; // 光线路径上的点和实际点
			while (this->raycaster_.step(rayPoint)) {
				actualPoint = rayPoint;
				actualPoint(0) += 0.5; // 调整到体素中心
				actualPoint(1) += 0.5;
				actualPoint(2) += 0.5;
				actualPoint *= this->mapRes_; // 转换到实际坐标
				raycastVoxelID = this->updateOccupancyInfo(actualPoint, false); // 更新占用信息为自由
	
				// 检查光线路径体素是否已更新
				if (this->flagTraverse_[raycastVoxelID] == this->raycastNum_) {
					break; // 如果已更新，则停止光线投射
				} else {
					this->flagTraverse_[raycastVoxelID] = this->raycastNum_; // 标记为已更新
				}
			}
		}
	
		// 存储局部更新范围并进行膨胀（用于 ESDF 更新）
		this->posToIndex(Eigen::Vector3d(xmin, ymin, zmin), this->localBoundMin_); // 转换最小边界到索引
		this->posToIndex(Eigen::Vector3d(xmax, ymax, zmax), this->localBoundMax_); // 转换最大边界到索引
		this->localBoundMin_ -= int(ceil(this->localBoundInflate_ / this->mapRes_)) * Eigen::Vector3i(1, 1, 0); // 在 x 和 y 方向膨胀
		this->localBoundMax_ += int(ceil(this->localBoundInflate_ / this->mapRes_)) * Eigen::Vector3i(1, 1, 0);
		this->boundIndex(this->localBoundMin_); // 确保边界在地图范围内
		this->boundIndex(this->localBoundMax_);
	
		// 更新缓存中的占用信息
		double logUpdateValue; // 日志更新值
		int cacheAddress, hit, miss; // 缓存地址、命中次数和未命中次数
		while (!this->updateVoxelCache_.empty()) {
			Eigen::Vector3i cacheIdx = this->updateVoxelCache_.front(); // 获取缓存中的体素索引
			this->updateVoxelCache_.pop(); // 移除缓存中的体素
			cacheAddress = this->indexToAddress(cacheIdx); // 转换索引到地址
	
			hit = this->countHit_[cacheAddress]; // 获取命中次数
			miss = this->countHitMiss_[cacheAddress] - hit; // 计算未命中次数
	
			if (hit >= miss && hit != 0) {
				logUpdateValue = this->pHitLog_; // 如果命中次数大于等于未命中次数，则使用命中日志值
			} else {
				logUpdateValue = this->pMissLog_; // 否则使用未命中日志值
			}
			this->countHit_[cacheAddress] = 0; // 清除命中计数
			this->countHitMiss_[cacheAddress] = 0; // 清除命中和未命中计数
	
			// 检查体素是否在局部更新范围内
			if (!this->isInLocalUpdateRange(cacheIdx)) {
				continue; // 如果不在范围内，则跳过
			}
	
			// 如果启用了自由区域，则检查点是否在自由区域内
			if (this->useFreeRegions_) {
				Eigen::Vector3d pos;
				this->indexToPos(cacheIdx, pos); // 转换索引到位置
				if (this->isInHistFreeRegions(pos)) {
					this->occupancy_[cacheAddress] = this->pMinLog_; // 设置为自由状态
					continue;
				}
			}
	
			// 更新占用概率
			if ((logUpdateValue >= 0) && (this->occupancy_[cacheAddress] >= this->pMaxLog_)) {
				continue; // 如果已达到最大值，则不增加
			} else if ((logUpdateValue <= 0) && (this->occupancy_[cacheAddress] == this->pMinLog_)) {
				continue; // 如果已达到最小值，则不减少
			} else if ((logUpdateValue <= 0) && (this->occupancy_[cacheAddress] < this->pMinLog_)) {
				this->occupancy_[cacheAddress] = this->pMinLog_; // 如果未知，则设置为自由状态
				continue;
			}
	
			// 更新占用概率值，确保在最小值和最大值之间
			this->occupancy_[cacheAddress] = std::min(std::max(this->occupancy_[cacheAddress] + logUpdateValue, this->pMinLog_), this->pMaxLog_);
	
			// 更新整个地图范围（如果不是未知状态）
			if (!this->isUnknown(cacheIdx)) {
				Eigen::Vector3d cachePos;
				this->indexToPos(cacheIdx, cachePos); // 转换索引到位置
				if (cachePos(0) > this->currMapRangeMax_(0)) {
					this->currMapRangeMax_(0) = cachePos(0);
				} else if (cachePos(0) < this->currMapRangeMin_(0)) {
					this->currMapRangeMin_(0) = cachePos(0);
				}
	
				if (cachePos(1) > this->currMapRangeMax_(1)) {
					this->currMapRangeMax_(1) = cachePos(1);
				} else if (cachePos(1) < this->currMapRangeMin_(1)) {
					this->currMapRangeMin_(1) = cachePos(1);
				}
	
				if (cachePos(2) > this->currMapRangeMax_(2)) {
					this->currMapRangeMax_(2) = cachePos(2);
				} else if (cachePos(2) < this->currMapRangeMin_(2)) {
					this->currMapRangeMin_(2) = cachePos(2);
				}
			}
		}
	}

	void occMap::cleanLocalMap() {
		// 清理局部地图的函数
	
		Eigen::Vector3i posIndex;
		this->posToIndex(this->position_, posIndex); // 将当前位置转换为体素索引
	
		// 计算局部地图的内边界和外边界
		Eigen::Vector3i innerMinBBX = posIndex - this->localMapVoxel_; // 内边界最小值
		Eigen::Vector3i innerMaxBBX = posIndex + this->localMapVoxel_; // 内边界最大值
		Eigen::Vector3i outerMinBBX = innerMinBBX - Eigen::Vector3i(5, 5, 5); // 外边界最小值
		Eigen::Vector3i outerMaxBBX = innerMaxBBX + Eigen::Vector3i(5, 5, 5); // 外边界最大值
	
		// 确保边界在地图范围内
		this->boundIndex(innerMinBBX);
		this->boundIndex(innerMaxBBX);
		this->boundIndex(outerMinBBX);
		this->boundIndex(outerMaxBBX);
	
		// 清理 x 轴方向的体素
		for (int y = outerMinBBX(1); y <= outerMaxBBX(1); ++y) {
			for (int z = outerMinBBX(2); z <= outerMaxBBX(2); ++z) {
				for (int x = outerMinBBX(0); x <= innerMinBBX(0); ++x) {
					// 将外边界到内边界的体素标记为未知状态
					this->occupancy_[this->indexToAddress(Eigen::Vector3i(x, y, z))] = this->pMinLog_ - this->UNKNOWN_FLAG_;
				}
				for (int x = innerMaxBBX(0); x <= outerMaxBBX(0); ++x) {
					// 将内边界到外边界的体素标记为未知状态
					this->occupancy_[this->indexToAddress(Eigen::Vector3i(x, y, z))] = this->pMinLog_ - this->UNKNOWN_FLAG_;
				}
			}
		}
	
		// 清理 y 轴方向的体素
		for (int x = outerMinBBX(0); x <= outerMaxBBX(0); ++x) {
			for (int z = outerMinBBX(2); z <= outerMaxBBX(2); ++z) {
				for (int y = outerMinBBX(1); y <= innerMinBBX(1); ++y) {
					// 将外边界到内边界的体素标记为未知状态
					this->occupancy_[this->indexToAddress(Eigen::Vector3i(x, y, z))] = this->pMinLog_ - this->UNKNOWN_FLAG_;
				}
				for (int y = innerMaxBBX(1); y <= outerMaxBBX(1); ++y) {
					// 将内边界到外边界的体素标记为未知状态
					this->occupancy_[this->indexToAddress(Eigen::Vector3i(x, y, z))] = this->pMinLog_ - this->UNKNOWN_FLAG_;
				}
			}
		}
	
		// 清理 z 轴方向的体素
		for (int x = outerMinBBX(0); x <= outerMaxBBX(0); ++x) {
			for (int y = outerMinBBX(1); y <= outerMaxBBX(1); ++y) {
				for (int z = outerMinBBX(2); z <= innerMinBBX(2); ++z) {
					// 将外边界到内边界的体素标记为未知状态
					this->occupancy_[this->indexToAddress(Eigen::Vector3i(x, y, z))] = this->pMinLog_ - this->UNKNOWN_FLAG_;
				}
				for (int z = innerMaxBBX(2); z <= outerMaxBBX(2); ++z) {
					// 将内边界到外边界的体素标记为未知状态
					this->occupancy_[this->indexToAddress(Eigen::Vector3i(x, y, z))] = this->pMinLog_ - this->UNKNOWN_FLAG_;
				}
			}
		}
	}
	
	void occMap::inflateLocalMap() {
		// 膨胀局部地图的函数
	
		// 获取局部边界的最小值和最大值
		int xmin = this->localBoundMin_(0);
		int xmax = this->localBoundMax_(0);
		int ymin = this->localBoundMin_(1);
		int ymax = this->localBoundMax_(1);
		int zmin = this->localBoundMin_(2);
		int zmax = this->localBoundMax_(2);
	
		Eigen::Vector3i clearIndex;
	
		// 清理当前数据范围内的膨胀数据
		for (int x = xmin; x <= xmax; ++x) {
			for (int y = ymin; y <= ymax; ++y) {
				for (int z = zmin; z <= zmax; ++z) {
					clearIndex(0) = x;
					clearIndex(1) = y;
					clearIndex(2) = z;
					this->occupancyInflated_[this->indexToAddress(clearIndex)] = false; // 重置膨胀标志
				}
			}
		}
	
		// 计算膨胀尺寸（x、y、z方向）
		int xInflateSize = ceil(this->robotSize_(0) / (2 * this->mapRes_));
		int yInflateSize = ceil(this->robotSize_(1) / (2 * this->mapRes_));
		int zInflateSize = ceil(this->robotSize_(2) / (2 * this->mapRes_));
	
		// 根据当前占用状态进行膨胀
		Eigen::Vector3i pointIndex, inflateIndex;
		int inflateAddress;
		const int maxIndex = this->mapVoxelMax_(0) * this->mapVoxelMax_(1) * this->mapVoxelMax_(2);
	
		for (int x = xmin; x <= xmax; ++x) {
			for (int y = ymin; y <= ymax; ++y) {
				for (int z = zmin; z <= zmax; ++z) {
					pointIndex(0) = x;
					pointIndex(1) = y;
					pointIndex(2) = z;
	
					if (this->isOccupied(pointIndex)) {
						// 遍历膨胀范围内的体素
						for (int ix = -xInflateSize; ix <= xInflateSize; ++ix) {
							for (int iy = -yInflateSize; iy <= yInflateSize; ++iy) {
								for (int iz = -zInflateSize; iz <= zInflateSize; ++iz) {
									inflateIndex(0) = pointIndex(0) + ix;
									inflateIndex(1) = pointIndex(1) + iy;
									inflateIndex(2) = pointIndex(2) + iz;
									inflateAddress = this->indexToAddress(inflateIndex);
	
									// 如果膨胀后的点超出地图范围，则跳过
									if ((inflateAddress < 0) or (inflateAddress > maxIndex)) {
										continue;
									}
	
									// 将膨胀后的点标记为占用状态
									this->occupancyInflated_[inflateAddress] = true;
								}
							}
						}
					}
				}
			}
		}
	}

	void occMap::visCB(const ros::TimerEvent&) {
		// 可视化的定时器回调函数
		// this->publishProjPoints(); // 发布投影点（当前注释掉）
		// this->publishMap(); // 发布占用地图（当前注释掉）
		// this->publishInflatedMap(); // 发布膨胀后的占用地图
		// this->publish2DOccupancyGrid(); // 发布2D占用网格（当前注释掉）
	}
	
	void occMap::projPointsVisCB(const ros::TimerEvent&) {
		// 投影点的可视化定时器回调函数
		this->publishProjPoints(); // 发布投影点
	}
	
	void occMap::mapVisCB(const ros::TimerEvent&) {
		// 占用地图的可视化定时器回调函数
		this->publishMap(); // 发布占用地图
	}
	
	void occMap::inflatedMapVisCB(const ros::TimerEvent&) {
		// 膨胀地图的可视化定时器回调函数
		// this->publishInflatedMap(); // 发布膨胀后的占用地图
	}
	
	void occMap::map2DVisCB(const ros::TimerEvent&) {
		// 2D占用网格的可视化定时器回调函数
		this->publish2DOccupancyGrid(); // 发布2D占用网格
	}
	
	void occMap::startVisualization() {
		// 启动可视化线程
		ros::Rate r(10); // 设置循环频率为10Hz
		while (ros::ok()) {
			// 发布投影点、占用地图和2D占用网格
			this->publishProjPoints(); // 发布投影点
			this->publishMap(); // 发布占用地图
			// this->publishInflatedMap(); // 发布膨胀后的占用地图（当前注释掉）
			this->publish2DOccupancyGrid(); // 发布2D占用网格
			r.sleep(); // 按照设定频率休眠
		}
	}
	
	void occMap::getMapVisData(pcl::PointCloud<pcl::PointXYZ>& mapCloud, pcl::PointCloud<pcl::PointXYZ>& inflatedMapCloud, pcl::PointCloud<pcl::PointXYZ>& exploredMapCloud, pcl::PointCloud<pcl::PointXYZ>& depthCloud) {
		// 获取地图可视化数据
	
		pcl::PointXYZ pt; // 定义点云点
		Eigen::Vector3d minRange, maxRange; // 定义地图范围的最小值和最大值
	
		if (this->visGlobalMap_) {
			// 如果可视化全局地图
			minRange = this->currMapRangeMin_; // 使用当前地图范围的最小值
			maxRange = this->currMapRangeMax_; // 使用当前地图范围的最大值
		} else {
			// 如果可视化局部地图
			minRange = this->position_ - localMapSize_; // 计算局部地图的最小范围
			maxRange = this->position_ + localMapSize_; // 计算局部地图的最大范围
			minRange(2) = this->groundHeight_; // 设置地面高度
		}
	
		Eigen::Vector3i minRangeIdx, maxRangeIdx; // 定义范围的索引
		this->posToIndex(minRange, minRangeIdx); // 将最小范围转换为索引
		this->posToIndex(maxRange, maxRangeIdx); // 将最大范围转换为索引
		this->boundIndex(minRangeIdx); // 确保最小索引在地图范围内
		this->boundIndex(maxRangeIdx); // 确保最大索引在地图范围内
	
		// 遍历投影点，将其添加到深度点云中
		for (int i = 0; i < this->projPointsNum_; ++i) {
			pt.x = this->projPoints_[i](0); // 设置点的x坐标
			pt.y = this->projPoints_[i](1); // 设置点的y坐标
			pt.z = this->projPoints_[i](2); // 设置点的z坐标
			depthCloud.push_back(pt); // 将点添加到深度点云
		}
	
		// 设置深度点云的属性
		depthCloud.width = depthCloud.points.size();
		depthCloud.height = 1;
		depthCloud.is_dense = true;
		depthCloud.header.frame_id = "map";
	
		// 遍历地图范围内的体素
		for (int x = minRangeIdx(0); x <= maxRangeIdx(0); ++x) {
			for (int y = minRangeIdx(1); y <= maxRangeIdx(1); ++y) {
				for (int z = minRangeIdx(2); z <= maxRangeIdx(2); ++z) {
					Eigen::Vector3i pointIdx(x, y, z); // 当前体素的索引
	
					if (this->isOccupied(pointIdx)) {
						// 如果体素被占用
						Eigen::Vector3d point;
						this->indexToPos(pointIdx, point); // 将索引转换为坐标
						if (point(2) <= this->maxVisHeight_) {
							// 如果点的高度小于最大可视化高度
							pt.x = point(0);
							pt.y = point(1);
							pt.z = point(2);
							mapCloud.push_back(pt); // 将点添加到占用地图点云
						}
					}
	
					if (this->isInflatedOccupied(pointIdx)) {
						// 如果体素被膨胀占用
						Eigen::Vector3d point;
						this->indexToPos(pointIdx, point); // 将索引转换为坐标
						if (point(2) <= this->maxVisHeight_) {
							// 如果点的高度小于最大可视化高度
							pt.x = point(0);
							pt.y = point(1);
							pt.z = point(2);
							inflatedMapCloud.push_back(pt); // 将点添加到膨胀地图点云
						}
					}
	
					if (!this->isUnknown(pointIdx)) {
						// 如果体素不是未知状态
						Eigen::Vector3d point;
						this->indexToPos(pointIdx, point); // 将索引转换为坐标
						pt.x = point(0);
						pt.y = point(1);
						pt.z = point(2);
						exploredMapCloud.push_back(pt); // 将点添加到探索地图点云
					}
				}
			}
		}
	
		// 设置占用地图点云的属性
		mapCloud.width = mapCloud.points.size();
		mapCloud.height = 1;
		mapCloud.is_dense = true;
		mapCloud.header.frame_id = "map";
	
		// 设置膨胀地图点云的属性
		inflatedMapCloud.width = inflatedMapCloud.points.size();
		inflatedMapCloud.height = 1;
		inflatedMapCloud.is_dense = true;
		inflatedMapCloud.header.frame_id = "map";
	
		// 设置探索地图点云的属性
		exploredMapCloud.width = exploredMapCloud.points.size();
		exploredMapCloud.height = 1;
		exploredMapCloud.is_dense = true;
		exploredMapCloud.header.frame_id = "map";
	}
	
	void occMap::publishProjPoints() {
		// 发布投影点的函数
	
		pcl::PointXYZ pt; // 定义一个点云点
		pcl::PointCloud<pcl::PointXYZ> cloud; // 定义一个点云对象
	
		// 遍历所有投影点
		for (int i = 0; i < this->projPointsNum_; ++i) {
			pt.x = this->projPoints_[i](0); // 设置点的 x 坐标
			pt.y = this->projPoints_[i](1); // 设置点的 y 坐标
			pt.z = this->projPoints_[i](2); // 设置点的 z 坐标
			cloud.push_back(pt); // 将点添加到点云中
		}
	
		// 设置点云的属性
		cloud.width = cloud.points.size(); // 点云的宽度为点的数量
		cloud.height = 1; // 点云的高度为 1（无组织点云）
		cloud.is_dense = true; // 点云是稠密的
		cloud.header.frame_id = "map"; // 设置点云的坐标系为 "map"
	
		sensor_msgs::PointCloud2 cloudMsg; // 定义一个 ROS 点云消息
		pcl::toROSMsg(cloud, cloudMsg); // 将 PCL 点云转换为 ROS 点云消息
		this->depthCloudPub_.publish(cloudMsg); // 发布点云消息
	}
	
	void occMap::publishMap() {
		// 发布占用地图的函数
	
		pcl::PointXYZ pt; // 定义一个点云点
		pcl::PointCloud<pcl::PointXYZ> cloud; // 定义一个点云对象，用于存储占用点
		pcl::PointCloud<pcl::PointXYZ> exploredCloud; // 定义一个点云对象，用于存储已探索点
	
		Eigen::Vector3d minRange, maxRange; // 定义地图范围的最小值和最大值
		if (this->visGlobalMap_) {
			// 如果可视化全局地图
			minRange = this->currMapRangeMin_; // 使用当前地图范围的最小值
			maxRange = this->currMapRangeMax_; // 使用当前地图范围的最大值
		} else {
			// 如果可视化局部地图
			minRange = this->position_ - localMapSize_; // 计算局部地图的最小范围
			maxRange = this->position_ + localMapSize_; // 计算局部地图的最大范围
			minRange(2) = this->groundHeight_; // 设置地面高度
		}
	
		Eigen::Vector3i minRangeIdx, maxRangeIdx; // 定义范围的索引
		this->posToIndex(minRange, minRangeIdx); // 将最小范围转换为索引
		this->posToIndex(maxRange, maxRangeIdx); // 将最大范围转换为索引
		this->boundIndex(minRangeIdx); // 确保最小索引在地图范围内
		this->boundIndex(maxRangeIdx); // 确保最大索引在地图范围内
	
		// 遍历地图范围内的体素
		for (int x = minRangeIdx(0); x <= maxRangeIdx(0); ++x) {
			for (int y = minRangeIdx(1); y <= maxRangeIdx(1); ++y) {
				for (int z = minRangeIdx(2); z <= maxRangeIdx(2); ++z) {
					Eigen::Vector3i pointIdx(x, y, z); // 当前体素的索引
	
					if (this->isOccupied(pointIdx)) {
						// 如果体素被占用
						Eigen::Vector3d point;
						this->indexToPos(pointIdx, point); // 将索引转换为坐标
						if (point(2) <= this->maxVisHeight_) {
							// 如果点的高度小于最大可视化高度
							pt.x = point(0);
							pt.y = point(1);
							pt.z = point(2);
							cloud.push_back(pt); // 将点添加到占用地图点云
						}
					}
	
					if (!this->isUnknown(pointIdx)) {
						// 如果体素不是未知状态
						Eigen::Vector3d point;
						this->indexToPos(pointIdx, point); // 将索引转换为坐标
						pt.x = point(0);
						pt.y = point(1);
						pt.z = point(2);
						exploredCloud.push_back(pt); // 将点添加到探索地图点云
					}
				}
			}
		}
	
		// 设置占用地图点云的属性
		cloud.width = cloud.points.size();
		cloud.height = 1;
		cloud.is_dense = true;
		cloud.header.frame_id = "map";
	
		// 设置探索地图点云的属性
		exploredCloud.width = exploredCloud.points.size();
		exploredCloud.height = 1;
		exploredCloud.is_dense = true;
		exploredCloud.header.frame_id = "map";
	
		sensor_msgs::PointCloud2 cloudMsg; // 定义一个 ROS 点云消息
		sensor_msgs::PointCloud2 exploredCloudMsg; // 定义一个 ROS 点云消息用于探索地图
		pcl::toROSMsg(cloud, cloudMsg); // 将占用地图点云转换为 ROS 点云消息
		pcl::toROSMsg(exploredCloud, exploredCloudMsg); // 将探索地图点云转换为 ROS 点云消息
		this->mapVisPub_.publish(cloudMsg); // 发布占用地图点云消息
		this->mapExploredPub_.publish(exploredCloudMsg); // 发布探索地图点云消息
	}
	
	void occMap::publishInflatedMap() {
		// 发布膨胀地图的函数
	
		pcl::PointXYZ pt; // 定义一个点云点
		pcl::PointCloud<pcl::PointXYZ> cloud; // 定义一个点云对象，用于存储膨胀点
	
		Eigen::Vector3d minRange, maxRange; // 定义地图范围的最小值和最大值
		if (this->visGlobalMap_) {
			// 如果可视化全局地图
			minRange = this->currMapRangeMin_; // 使用当前地图范围的最小值
			maxRange = this->currMapRangeMax_; // 使用当前地图范围的最大值
		} else {
			// 如果可视化局部地图
			minRange = this->position_ - localMapSize_; // 计算局部地图的最小范围
			maxRange = this->position_ + localMapSize_; // 计算局部地图的最大范围
			minRange(2) = this->groundHeight_; // 设置地面高度
		}
	
		Eigen::Vector3i minRangeIdx, maxRangeIdx; // 定义范围的索引
		this->posToIndex(minRange, minRangeIdx); // 将最小范围转换为索引
		this->posToIndex(maxRange, maxRangeIdx); // 将最大范围转换为索引
		this->boundIndex(minRangeIdx); // 确保最小索引在地图范围内
		this->boundIndex(maxRangeIdx); // 确保最大索引在地图范围内
	
		// 遍历地图范围内的体素
		for (int x = minRangeIdx(0); x <= maxRangeIdx(0); ++x) {
			for (int y = minRangeIdx(1); y <= maxRangeIdx(1); ++y) {
				for (int z = minRangeIdx(2); z <= maxRangeIdx(2); ++z) {
					Eigen::Vector3i pointIdx(x, y, z); // 当前体素的索引
	
					if (this->isInflatedOccupied(pointIdx)) {
						// 如果体素被膨胀占用
						Eigen::Vector3d point;
						this->indexToPos(pointIdx, point); // 将索引转换为坐标
						if (point(2) <= this->maxVisHeight_) {
							// 如果点的高度小于最大可视化高度
							pt.x = point(0);
							pt.y = point(1);
							pt.z = point(2);
							cloud.push_back(pt); // 将点添加到膨胀地图点云
						}
					}
				}
			}
		}
	
		// 设置膨胀地图点云的属性
		cloud.width = cloud.points.size();
		cloud.height = 1;
		cloud.is_dense = true;
		cloud.header.frame_id = "map";
	
		sensor_msgs::PointCloud2 cloudMsg; // 定义一个 ROS 点云消息
		pcl::toROSMsg(cloud, cloudMsg); // 将膨胀地图点云转换为 ROS 点云消息
		this->inflatedMapVisPub_.publish(cloudMsg); // 发布膨胀地图点云消息
	}
	
	void occMap::publish2DOccupancyGrid() {
		// 发布2D占用网格的函数
	
		Eigen::Vector3d minRange, maxRange; // 定义地图范围的最小值和最大值
		minRange = this->mapSizeMin_; // 设置最小范围为地图的最小尺寸
		maxRange = this->mapSizeMax_; // 设置最大范围为地图的最大尺寸
		minRange(2) = this->groundHeight_; // 将最小范围的z坐标设置为地面高度
		Eigen::Vector3i minRangeIdx, maxRangeIdx; // 定义范围的索引
		this->posToIndex(minRange, minRangeIdx); // 将最小范围转换为索引
		this->posToIndex(maxRange, maxRangeIdx); // 将最大范围转换为索引
		this->boundIndex(minRangeIdx); // 确保最小索引在地图范围内
		this->boundIndex(maxRangeIdx); // 确保最大索引在地图范围内
	
		nav_msgs::OccupancyGrid mapMsg; // 定义一个ROS的占用网格消息
		for (int i = 0; i < maxRangeIdx(0); ++i) {
			for (int j = 0; j < maxRangeIdx(1); ++j) {
				mapMsg.data.push_back(0); // 初始化网格数据为0（空闲状态）
			}
		}
	
		double z = 0.5; // 设置2D网格的高度为0.5米
		int zIdx = int(z / this->mapRes_); // 计算z方向的索引
		for (int x = minRangeIdx(0); x <= maxRangeIdx(0); ++x) { // 遍历x方向的索引
			for (int y = minRangeIdx(1); y <= maxRangeIdx(1); ++y) { // 遍历y方向的索引
				Eigen::Vector3i pointIdx(x, y, zIdx); // 定义当前体素的索引
				int map2DIdx = x + y * maxRangeIdx(0); // 计算2D网格的索引
				if (this->isUnknown(pointIdx)) {
					// 如果体素是未知状态
					mapMsg.data[map2DIdx] = -1; // 设置网格值为-1（未知状态）
				} else if (this->isOccupied(pointIdx)) {
					// 如果体素是占用状态
					mapMsg.data[map2DIdx] = 100; // 设置网格值为100（占用状态）
				} else {
					// 如果体素是空闲状态
					mapMsg.data[map2DIdx] = 0; // 设置网格值为0（空闲状态）
				}
			}
		}
	
		mapMsg.header.frame_id = "map"; // 设置网格的坐标系为"map"
		mapMsg.header.stamp = ros::Time::now(); // 设置时间戳为当前时间
		mapMsg.info.resolution = this->mapRes_; // 设置网格的分辨率
		mapMsg.info.width = maxRangeIdx(0); // 设置网格的宽度
		mapMsg.info.height = maxRangeIdx(1); // 设置网格的高度
		mapMsg.info.origin.position.x = minRange(0); // 设置网格的原点x坐标
		mapMsg.info.origin.position.y = minRange(1); // 设置网格的原点y坐标
		this->map2DPub_.publish(mapMsg); // 发布2D占用网格消息
	}

	// 保存SDF地图到二进制文件
	bool occMap::saveSDFToFile(const std::string& filename) {
        std::ofstream outfile(filename, std::ios::binary);
        if (!outfile) {
            std::cout << this->hint_ << ": Failed to open file for writing: " << filename << std::endl;
            return false;
        }

        // 写入地图维度信息
        int dims[3] = {
            this->mapVoxelMax_(0),
            this->mapVoxelMax_(1),
            this->mapVoxelMax_(2)
        };
        outfile.write(reinterpret_cast<char*>(dims), sizeof(dims));

        // 写入数据总数
        size_t data_size = this->sdf_map_.size();
        outfile.write(reinterpret_cast<char*>(&data_size), sizeof(data_size));

        // 写入实际数据
        outfile.write(reinterpret_cast<char*>(this->sdf_map_.data()), 
                     data_size * sizeof(double));

        if (outfile.fail()) {
            std::cout << this->hint_ << ": Error occurred during file writing" << std::endl;
            outfile.close();
            return false;
        }

        outfile.close();
        std::cout << this->hint_ << ": Successfully saved SDF map to: " << filename << std::endl;
        return true;
    }

    // 从文件加载SDF地图
    bool occMap::loadSDFFromFile(const std::string& filename) {
        std::ifstream infile(filename, std::ios::binary);
        if (!infile) {
            std::cout << this->hint_ << ": Failed to open file for reading: " << filename << std::endl;
            return false;
        }

        // 读取地图维度信息
        int file_dims[3];
        infile.read(reinterpret_cast<char*>(file_dims), sizeof(file_dims));

        // 验证维度匹配
        if (file_dims[0] != this->mapVoxelMax_(0) ||
            file_dims[1] != this->mapVoxelMax_(1) ||
            file_dims[2] != this->mapVoxelMax_(2)) {
            std::cout << this->hint_ << ": Map dimensions mismatch! File: ["
                      << file_dims[0] << ", " << file_dims[1] << ", " << file_dims[2]
                      << "] Current: [" 
                      << this->mapVoxelMax_.transpose() << "]" << std::endl;
            infile.close();
            return false;
        }

        // 读取数据大小
        size_t file_data_size;
        infile.read(reinterpret_cast<char*>(&file_data_size), sizeof(file_data_size));

        // 验证数据大小匹配
        const size_t expected_size = this->mapVoxelMax_(0) * 
                                    this->mapVoxelMax_(1) * 
                                    this->mapVoxelMax_(2);
        if (file_data_size != expected_size) {
            std::cout << this->hint_ << ": Data size mismatch! File: " << file_data_size
                      << " Expected: " << expected_size << std::endl;
            infile.close();
            return false;
        }

        // 准备存储空间
        this->sdf_map_.resize(file_data_size);

        // 读取数据
        infile.read(reinterpret_cast<char*>(this->sdf_map_.data()),
                   file_data_size * sizeof(double));

        if (infile.fail()) {
            std::cout << this->hint_ << ": Error occurred during file reading" << std::endl;
            infile.close();
            return false;
        }

        infile.close();
        std::cout << this->hint_ << ": Successfully loaded SDF map from: " << filename << std::endl;
        return true;
    }

}
