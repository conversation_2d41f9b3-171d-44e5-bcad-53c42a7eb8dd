/*
    FILE: dynamicPredictor.cpp
    ---------------------------------
    function implementation of dynamic osbtacle predictor
*/
#include <dynamic_predictor/dynamicPredictor.h>

namespace dynamicPredictor{
    // 构造函数：初始化动态障碍物预测器
    predictor::predictor(const ros::NodeHandle& nh) : nh_(nh) {
        this->ns_ = "dynamic_predictor";        // 设置节点命名空间
        this->hint_ = "[predictor]";            // 设置日志输出的提示标识
        this->initParam();                      // 初始化参数
        this->registerPub();                    // 注册发布器
        this->registerCallback();               // 注册回调函数
    }

    void predictor::initParam() {
        // 初始化预测步数参数
        if (not this->nh_.getParam(this->ns_ + "/prediction_size", this->numPred_)) {
            this->numPred_ = 5;                 // 如果没有找到参数，使用默认值5
            std::cout << this->hint_ << ": No prediction size parameter found. Use default: 5." << std::endl;
        } else {
            std::cout << this->hint_ << ": The prediction size is set to: " << this->numPred_ << std::endl;
        }  

        // 初始化预测时间步长参数
        if (not this->nh_.getParam(this->ns_ + "/prediction_time_step", this->dt_)) {
            this->dt_ = 0.1;                    // 如果没有找到参数，使用默认值0.1秒
            std::cout << this->hint_ << ": No prediction time step parameter found. Use default: 0.1." << std::endl;
        } else {
            std::cout << this->hint_ << ": The prediction time step is set to: " << this->dt_ << std::endl;
        }  

        // 初始化预测置信度参数（z分数）
        if (not this->nh_.getParam(this->ns_ + "/prediction_z_score", this->zScore_)) {
            this->zScore_ = 1.645;              // 如果没有找到参数，使用默认值1.645（90%置信区间）
            std::cout << this->hint_ << ": No prediction z score parameter found. Use default: 1.645." << std::endl;
        } else {
            std::cout << this->hint_ << ": The prediction z score is set to: " << this->zScore_ << std::endl;
        }

        // 初始化最小转向时间参数
        if (not this->nh_.getParam(this->ns_ + "/min_turning_time", this->minTurningTime_)) {
            this->minTurningTime_ = 2.0;        // 如果没有找到参数，使用默认值2.0秒
            std::cout << this->hint_ << ": No minimum turning time parameter found. Use default: 1.0." << std::endl;
        } else {
            std::cout << this->hint_ << ": The minimum turning time is set to: " << this->minTurningTime_ << std::endl;
        }  

        // 初始化最大转向时间参数
        if (not this->nh_.getParam(this->ns_ + "/max_turning_time", this->maxTurningTime_)) {
            this->maxTurningTime_ = 3.0;        // 如果没有找到参数，使用默认值3.0秒
            std::cout << this->hint_ << ": No maximum turning time parameter found. Use default: 3.0." << std::endl;
        } else {
            if (this->maxTurningTime_ < this->minTurningTime_) {
                this->maxTurningTime_ = this->minTurningTime_ + 1.0;  // 确保最大转向时间大于最小转向时间
            }
            std::cout << this->hint_ << ": The maximum turning time is set to: " << this->maxTurningTime_ << std::endl;
        }

        // 初始化最大前方概率参数
        double maxFrontProb;
        if (not this->nh_.getParam(this->ns_ + "/max_front_prob", maxFrontProb)) {
            maxFrontProb = 0.5;                 // 如果没有找到参数，使用默认值0.5
            this->paramr_ = (1 - maxFrontProb) / (3 * maxFrontProb - 1); // 计算右转概率参数
            this->paraml_ = (1 - maxFrontProb) / (3 * maxFrontProb - 1); // 计算左转概率参数
            std::cout << this->hint_ << ": No max front prob param. Use default: 0.5." << std::endl;
        } else {
            this->paramr_ = (1 - maxFrontProb) / (3 * maxFrontProb - 1); // 计算右转概率参数
            this->paraml_ = (1 - maxFrontProb) / (3 * maxFrontProb - 1); // 计算左转概率参数
            std::cout << this->hint_ << ": Max front prob param is set to: " << maxFrontProb << std::endl;
        } 

        // 初始化前方角度参数
        if (not this->nh_.getParam(this->ns_ + "/front_angle", this->frontAngle_)) {
            this->frontAngle_ = 1 / 6 * M_PI;   // 如果没有找到参数，使用默认值30度
            this->paramf_ = sqrt(pow(this->frontAngle_, 2) / (-2 * log(this->paraml_ * (1 + sin(this->frontAngle_)) - this->paraml_))); // 计算前方概率参数
            std::cout << this->hint_ << ": No front angle param. Use default: 30 degree." << std::endl;
        } else {
            this->frontAngle_ = this->frontAngle_ * M_PI / 180; // 将角度从度转换为弧度
            this->paramf_ = sqrt(pow(this->frontAngle_, 2) / (-2 * log(this->paraml_ * (1 + sin(this->frontAngle_)) - this->paraml_))); // 计算前方概率参数
            std::cout << this->hint_ << ": Front angle param is set to: " << this->frontAngle_ << std::endl;
        } 

        // 初始化停止速度阈值参数
        if (not this->nh_.getParam(this->ns_ + "/stop_velocity_thereshold", this->stopVel_)) {
            this->stopVel_ = 0.2;               // 如果没有找到参数，使用默认值0.2
            this->params_ = atanh(0.5) / this->stopVel_; // 计算停止概率参数
            std::cout << this->hint_ << ": No stop velocity thereshold param. Use default: 0.2." << std::endl;
        } else {
            this->params_ = atanh(0.5) / this->stopVel_; // 计算停止概率参数
            std::cout << this->hint_ << ": Stop velocity thereshold param is set to: " << this->stopVel_ << std::endl;
        } 

        // 输出计算的概率参数
        std::cout << this->hint_ << ": Front param is set to: " << this->paramf_ << std::endl;
        std::cout << this->hint_ << ": Left param is set to: " << this->paraml_ << std::endl;
        std::cout << this->hint_ << ": Right param is set to: " << this->paramr_ << std::endl;
        std::cout << this->hint_ << ": Stop param is set to: " << this->params_ << std::endl;

        // 初始化概率缩放参数
        if (not this->nh_.getParam(this->ns_ + "/prob_scale_param", this->pscale_)) {
            this->pscale_ = 1.5;               // 如果没有找到参数，使用默认值1.5
            std::cout << this->hint_ << ": No prob scale param. Use default: 1.5." << std::endl;
        } else {
            std::cout << this->hint_ << ": Prob scale param is set to: " << this->pscale_ << std::endl;
        } 
    }

    // 设置动态检测器
    void predictor::setDetector(const std::shared_ptr<onboardDetector::dynamicDetector>& detector) {
        this->detector_ = detector;            // 将动态检测器对象赋值给成员变量
        this->detectorReady_ = true;           // 标记动态检测器已准备好
    }

    // 设置虚拟检测器
    void predictor::setDetector(const std::shared_ptr<onboardDetector::fakeDetector>& detector) {
        this->detectorGT_ = detector;          // 将虚拟检测器对象赋值给成员变量
        this->useFakeDetector_ = true;         // 标记使用虚拟检测器
        this->detectorGTReady_ = true;         // 标记虚拟检测器已准备好
    }

    // 设置地图管理器
    void predictor::setMap(const std::shared_ptr<mapManager::dynamicMap>& map) {
        this->map_ = map;                      // 将地图管理器对象赋值给成员变量
        this->map_->getRobotSize(this->robotSize_); // 从地图管理器中获取机器人尺寸
        this->setDetector(this->map_->getDetector()); // 从地图管理器中获取检测器并设置
        this->mapReady_ = true;                // 标记地图已准备好
        this->size_x = map->mapSize_(0);
        this->size_y = map->mapSize_(1);
    }

    // 注册发布器
    void predictor::registerPub() {
        // 发布历史轨迹
        this->historyTrajPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/history_trajectories", 10);
        // 发布预测轨迹
        this->predTrajPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/predict_trajectories", 10);
        // 发布意图概率可视化
        this->intentVisPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/intent_probability", 10);
        // 发布方差点云
        this->varPointsPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/var_points", 10);
        // 发布预测包围盒
        this->predBBoxPub_ = this->nh_.advertise<visualization_msgs::MarkerArray>(this->ns_ + "/pred_bbox", 10);
        // 发布预测数据
        this->predDataPub_ = this->nh_.advertise<dynamic_predictor::PredictionData>("/planner/prediction", 10);
    }

    // 注册定时器回调函数
    void predictor::registerCallback() {
        // 注册预测定时器回调函数，周期为 0.033 秒
        this->predTimer_ = this->nh_.createTimer(ros::Duration(0.02), &predictor::predCB, this);
        // 注册可视化定时器回调函数，周期为 0.033 秒
        this->visTimer_ = this->nh_.createTimer(ros::Duration(0.02), &predictor::visCB, this);
    }

    // 可视化回调函数
    void predictor::visCB(const ros::TimerEvent&) {
        this->publishHistoryTraj();            // 发布历史轨迹
        this->publishPredTraj();               // 发布预测轨迹
        this->publishIntentVis();              // 发布意图概率可视化
        this->publishVarPoints();              // 发布方差点云
        this->publishPredBBox();               // 发布预测包围盒
    }

    // 预测回调函数
    void predictor::predCB(const ros::TimerEvent&) {
        this->predict();                       // 调用预测函数
        this->publishPredData();               // 发布预测数据
    }

    // ——————————————————————————————————————————————预测的主函数，关键中的关键——————————————————————————————————————————————
    void predictor::predict() { 
        // 获取历史数据
        if (this->useFakeDetector_ and this->detectorGTReady_ and this->mapReady_) { 
            // 如果使用虚拟检测器且检测器和地图已准备好，则从虚拟检测器获取动态障碍物的历史数据
            this->detectorGT_->getDynamicObstaclesHist(this->posHist_, this->velHist_, this->accHist_, this->sizeHist_, this->robotSize_);
        } else if (not this->useFakeDetector_ and this->detectorReady_ and this->mapReady_) {
            // 如果使用真实检测器且检测器和地图已准备好，则从真实检测器获取动态障碍物的历史数据
            this->detector_->getDynamicObstaclesHist(this->posHist_, this->velHist_, this->accHist_, this->sizeHist_, this->robotSize_);
        }
    
        // 如果历史位置数据不为空
        if (this->posHist_.size()) {
            if (this->posHist_[0].size()) { // 如果第一个障碍物的历史位置数据不为空
                // 意图预测
                std::vector<Eigen::VectorXd> intentProbTemp; // 临时存储意图概率
                this->intentProb(intentProbTemp); // 调用意图预测函数
    
                // 轨迹预测
                std::vector<std::vector<std::vector<std::vector<Eigen::Vector3d>>>> allPredPointsTemp; // 临时存储所有预测点
                std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPredTemp; // 临时存储位置预测
                std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePredTemp; // 临时存储尺寸预测
                this->predTraj(allPredPointsTemp, posPredTemp, sizePredTemp); // 调用轨迹预测函数
    
                // 更新预测结果
                this->intentProb_ = intentProbTemp; // 更新意图概率
                this->posPred_ = posPredTemp; // 更新位置预测
                this->sizePred_ = sizePredTemp; // 更新尺寸预测
                this->allPredPoints_ = allPredPointsTemp; // 更新所有预测点
            }
        } else { // 如果历史数据为空
            this->intentProb_.clear(); // 清空意图概率
            this->allPredPoints_.clear(); // 清空所有预测点
            this->posPred_.clear(); // 清空位置预测
            this->sizePred_.clear(); // 清空尺寸预测
        }
    }

    // 由状态转移矩阵计算动态障碍物的意图概率
    void predictor::intentProb(std::vector<Eigen::VectorXd> &intentProbTemp) {
        int numOb = this->posHist_.size(); // 获取动态障碍物的数量
        intentProbTemp.resize(numOb); // 调整意图概率容器的大小
    
        for (int i = 0; i < numOb; ++i) {
            // 初始化状态概率向量 P
            Eigen::VectorXd P;
            P.resize(this->numIntent_); // 设置向量大小为意图数量
            P.setConstant(1.0 / this->numIntent_); // 初始化为均匀分布
    
            int numHist = this->posHist_[i].size(); // 获取当前障碍物的历史数据长度
            for (int j = 2; j < numHist; ++j) {
                // 计算状态转移矩阵
                Eigen::Vector3d prevPos, prevVel, currPos, currVel;
                prevPos = this->posHist_[i][numHist - j - 1]; // 获取前一个位置
                prevVel = this->velHist_[i][numHist - j - 1]; // 获取前一个速度
                currPos = this->posHist_[i][numHist - j - 2]; // 获取当前的位置
                currVel = this->velHist_[i][numHist - j - 2]; // 获取当前的速度
    
                // 计算前后两个位置的角度
                double prevAngle = atan2(prevPos(1) - this->posHist_[i][numHist - j](1), prevPos(0) - this->posHist_[i][numHist - j](0));
                double currAngle = atan2(currPos(1) - prevPos(1), currPos(0) - prevPos(0));
    
                // 生成状态转移矩阵
                Eigen::MatrixXd transMat = this->genTransitionMatrix(prevAngle, currAngle, currVel);
    
                // 更新状态概率向量
                Eigen::VectorXd newP = transMat * P;
                P = newP;
            }
            intentProbTemp[i] = P; // 保存当前障碍物的意图概率
        }
    }
    
    // 生成状态转移矩阵
    Eigen::MatrixXd predictor::genTransitionMatrix(const double &prevAngle, const double &currAngle, const Eigen::Vector3d &currVel) {
        Eigen::MatrixXd transMat; // 状态转移矩阵
        Eigen::VectorXd probVec;  // 概率向量
        probVec.resize(this->numIntent_); // 设置向量大小为意图数量
        transMat.resize(this->numIntent_, this->numIntent_); // 设置矩阵大小为意图数量的平方
    
        // 计算角度变化量 theta
        double theta = currAngle - prevAngle;
        if (theta > M_PI) {
            theta -= 2 * M_PI; // 将角度限制在 [-π, π] 范围内
        } else if (theta <= -M_PI) {
            theta += 2 * M_PI;
        }
    
        double r = sqrt(pow(currVel(0), 2) + pow(currVel(1), 2)); // 计算速度大小
    
        // 填充状态转移矩阵的每一列
        for (int i = 0; i < this->numIntent_; i++) {
            Eigen::VectorXd scale;
            scale.setOnes(this->numIntent_); // 初始化缩放向量为全 1
            scale(i) = this->pscale_; // 对当前意图应用缩放参数
            Eigen::VectorXd probVec = this->genTransitionVector(theta, r, scale); // 生成概率向量
            transMat.block(0, i, this->numIntent_, 1) = probVec; // 将概率向量填入矩阵
        }
    
        return transMat; // 返回状态转移矩阵
    }
    
    // 生成状态转移向量
    Eigen::VectorXd predictor::genTransitionVector(const double &theta, const double &r, const Eigen::VectorXd &scale) {
        Eigen::VectorXd probVec; // 概率向量
        probVec.resize(this->numIntent_); // 设置向量大小为意图数量
    
        double ps, pf, pr, pl;
    
        // 计算前进概率（高斯分布）
        pf = scale(0) * (exp(-0.5 * pow(theta / this->paramf_, 2)) + this->paraml_);
        // 计算左转概率
        pl = scale(1) * (this->paraml_ * (1 + sin(theta)));
        // 计算右转概率
        pr = scale(2) * (this->paramr_ * (1 - sin(theta)));
        // 计算停止概率
        ps = (1 - tanh(this->params_ / scale(3) * r));
    
        // 归一化概率
        double sum = pr + pl + pf;
        pr = (1 - ps) * pr / sum;
        pl = (1 - ps) * pl / sum;
        pf = (1 - ps) * pf / sum;
    
        // 填充概率向量
        probVec(FORWARD) = pf; // 前进
        probVec(LEFT) = pl;    // 左转
        probVec(RIGHT) = pr;   // 右转
        probVec(STOP) = ps;    // 停止
    
        return probVec; // 返回概率向量
    }

    void predictor::predTraj(std::vector<std::vector<std::vector<std::vector<Eigen::Vector3d>>>> &allPredPointsTemp,
        std::vector<std::vector<std::vector<Eigen::Vector3d>>> &posPredTemp,
        std::vector<std::vector<std::vector<Eigen::Vector3d>>> &sizePredTemp){

        // 清空并初始化位置预测容器
        posPredTemp.clear();
        posPredTemp.resize(this->posHist_.size()); // 根据历史位置的数量调整大小

        // 清空并初始化尺寸预测容器
        sizePredTemp.clear();
        sizePredTemp.resize(this->sizeHist_.size()); // 根据历史尺寸的数量调整大小

        // 清空并初始化所有预测点容器
        allPredPointsTemp.clear();
        allPredPointsTemp.resize(this->posHist_.size()); // 根据历史位置的数量调整大小

        // 遍历每个动态障碍物进行预测
        for (int i = 0; i < int(this->posHist_.size()); i++) {
            posPredTemp[i].resize(this->numIntent_); // 为每个意图类型分配空间
            sizePredTemp[i].resize(this->numIntent_); // 为每个意图类型分配空间
            allPredPointsTemp[i].resize(this->numIntent_); // 为每个意图类型分配空间

            // 遍历每种意图类型进行预测
            for (int j = FORWARD; j <= STOP; ++j) {
                std::vector<std::vector<Eigen::Vector3d>> predPoints; // 存储预测点
                std::vector<Eigen::Vector3d> predSize; // 存储预测尺寸

                // 调用生成预测点的函数，根据意图类型、当前状态和历史数据生成每种意图（除停止外）一系列轨迹的预测点
                this->genPoints(j, this->posHist_[i][0], this->velHist_[i][0], this->accHist_[i][0], this->sizeHist_[i][0], predPoints, predSize);

                if (predPoints.size()) { // 如果生成了预测点
                    std::vector<Eigen::Vector3d> predPos; // 存储预测位置
                    this->genTraj(predPoints, predPos, predSize); // 调用生成轨迹的函数
                    posPredTemp[i][j] = predPos; // 更新位置预测
                    sizePredTemp[i][j] = predSize; // 更新尺寸预测
                    allPredPointsTemp[i][j] = predPoints; // 更新所有预测点
                } else { // 如果没有生成预测点
                    Eigen::Vector3d size = this->sizeHist_[i][0]; // 获取当前尺寸
                    Eigen::Vector3d currVel = this->velHist_[i][0]; // 获取动态障碍物当前速度
                    Eigen::Vector3d currPos = this->posHist_[i][0]; // 获取当前位置
                    std::vector<Eigen::Vector3d> predPos; // 存储预测位置
                    double vel = sqrt(pow(currVel(0), 2) + pow(currVel(1), 2)); // 计算速度大小

                    // 根据停止模型生成预测点
                    for (int k = 0; k < this->numPred_ + 1; k++) {
                        predPos.push_back(currPos); // 添加当前位置
                        predSize.push_back(size); // 添加当前尺寸
                        size(0) += 2 * min(vel, this->stopVel_) * this->dt_; // 更新尺寸的 x 方向
                        size(1) += 2 * min(vel, this->stopVel_) * this->dt_; // 更新尺寸的 y 方向
                    }

                    posPredTemp[i][j] = predPos; // 更新位置预测
                    sizePredTemp[i][j] = predSize; // 更新尺寸预测
                }
            }
        }
    }

    // 根据意图类型生成预测点
    void predictor::genPoints(const int &intentType, const Eigen::Vector3d &currPos, const Eigen::Vector3d &currVel, const Eigen::Vector3d &currAcc, const Eigen::Vector3d &currSize, std::vector<std::vector<Eigen::Vector3d>> &predPoints, std::vector<Eigen::Vector3d> &predSize) {
        predPoints.clear(); // 清空预测点容器
        predSize.clear();   // 清空预测尺寸容器
        double vel = sqrt(pow(currVel(0), 2) + pow(currVel(1), 2)); // 计算动态障碍物当前速度的大小
    
        if (vel <= this->stopVel_) { // 如果动态障碍物此时的速度小于停止阈值
            this->modelStop(currPos, currVel, currSize, predPoints, predSize); // 调用停止模型生成预测点
        } else {
            if (intentType == FORWARD) { // 如果意图是前进
                this->modelForward(currPos, currVel, currAcc, currSize, predPoints, predSize); // 调用前进模型生成预测点
            } 
            else if (intentType == LEFT or intentType == RIGHT) { // 如果意图是左转或右转
                this->modelTurning(intentType, currPos, currVel, currAcc, currSize, predPoints, predSize); // 调用转向模型生成预测点
            } 
            else if (intentType == STOP) { // 如果意图是停止
                this->modelStop(currPos, currVel, currSize, predPoints, predSize); // 调用停止模型生成预测点
            }
        }
    }
    
    // 前进模型：生成前进方向一系列轨迹的预测点
    void predictor::modelForward(const Eigen::Vector3d &currPos, const Eigen::Vector3d &currVel, const Eigen::Vector3d &currAcc, const Eigen::Vector3d &currSize, std::vector<std::vector<Eigen::Vector3d>> &predPoints, std::vector<Eigen::Vector3d> &predSize) {
        predPoints.clear(); // 清空预测点容器
        predSize.clear();   // 清空预测尺寸容器
        double vel = sqrt(pow(currVel(0), 2) + pow(currVel(1), 2)); // 计算当前速度的大小
        double angleInit = atan2(currVel(1), currVel(0)); // 计算当前速度方向的角度
        double minVel = vel - vel; // 最小速度
        double maxVel = vel + vel; // 最大速度
        double minAngle = angleInit - this->frontAngle_; // 最小角度
        double maxAngle = angleInit + this->frontAngle_; // 最大角度
        bool isValid = true; // 标记预测点是否有效
    
        // 遍历角度范围和速度范围，生成一系列轨迹的预测点
        for (double i = minAngle; i < maxAngle; i += 0.1) {
            for (double j = minVel; j < maxVel; j += 0.1) {
                std::vector<Eigen::Vector3d> predPointTemp; // 临时存储预测点
                Eigen::VectorXd currState(4); // 当前状态向量
                currState << currPos(0), currPos(1), j * cos(i), j * sin(i); // 初始化状态向量
                predPointTemp.clear();
                predPointTemp.push_back(currPos); // 添加当前位置
    
                for (int k = 0; k < this->numPred_; k++) { // 遍历预测步数
                    Eigen::MatrixXd model = MatrixXd::Identity(4, 4); // 初始化状态转移矩阵
                    model.block(0, 2, 2, 2) = Eigen::MatrixXd::Identity(2, 2) * this->dt_; // 设置速度对位置的影响
                    Eigen::VectorXd nextState = model * currState; // 计算下一状态
                    Eigen::Vector3d p;
                    p << nextState(0), nextState(1), currPos(2); // 提取下一位置
    
                    if (this->map_->isInflatedOccupied(p)) { // 如果预测点与障碍物发生碰撞
                        isValid = false; // 标记为无效
                        break;
                    } else {
                        predPointTemp.push_back(p); // 添加预测点
                    }
                    currState = nextState; // 更新当前状态
                }
    
                if (isValid) {
                    predPoints.push_back(predPointTemp); // 保存有效的预测点
                } else {
                    isValid = true; // 重置标记
                    break;
                }
            }
        }
    
        for (int i = 0; i < this->numPred_ + 1; i++) {
            predSize.push_back(currSize); // 添加预测尺寸
        }
    }
    
    // 转向模型：生成左转或右转方向一系列轨迹的预测点
    void predictor::modelTurning(const int &intentType, const Eigen::Vector3d &currPos, const Eigen::Vector3d &currVel, const Eigen::Vector3d &currAcc, const Eigen::Vector3d &currSize, std::vector<std::vector<Eigen::Vector3d>> &predPoints, std::vector<Eigen::Vector3d> &predSize) {
        predPoints.clear(); // 清空预测点容器
        predSize.clear();   // 清空预测尺寸容器
        double vel = sqrt(pow(currVel(0), 2) + pow(currVel(1), 2)); // 计算当前速度的大小
        double angleInit = atan2(currVel(1), currVel(0)); // 计算当前速度方向的角度
        double minVel = vel - vel; // 最小速度
        double maxVel = vel + vel; // 最大速度
        double endMin, endMax; // 终止角度范围
    
        if (intentType != LEFT and intentType != RIGHT) { // 如果意图类型不是左转或右转
            cout << this->hint_ << ": Please enter the correct intent!!!" << endl; // 输出错误提示
        }
    
        double minAngVel, maxAngVel; // 最小和最大角速度
        if (intentType == LEFT) { // 如果意图是左转
            endMin = this->frontAngle_ + angleInit; // 终止角度最小值
            endMax = (M_PI - this->frontAngle_) + angleInit; // 终止角度最大值
            minAngVel = (M_PI / 2) / this->maxTurningTime_; // 最小角速度
            maxAngVel = (M_PI / 2) / this->minTurningTime_; // 最大角速度
        } else { // 如果意图是右转
            endMin = -(M_PI - this->frontAngle_) + angleInit; // 终止角度最小值
            endMax = -this->frontAngle_ + angleInit; // 终止角度最大值
            minAngVel = (-M_PI / 2) / this->minTurningTime_; // 最小角速度
            maxAngVel = (-M_PI / 2) / this->maxTurningTime_; // 最大角速度
        }
        bool isValid = true; // 标记预测点是否有效
    
        // 遍历速度范围、角速度范围和终止角度范围，生成预测点
        for (double i = minVel; i < maxVel; i += 0.2) {
            for (double j = minAngVel; j < maxAngVel; j += 0.2) {
                for (double endAngle = endMin; endAngle < endMax; endAngle += 0.2) {
                    std::vector<Eigen::Vector3d> predPointTemp; // 临时存储预测点
                    Eigen::VectorXd currState(4); // 当前状态向量
                    double angle = angleInit; // 初始化角度
                    currState << currPos(0), currPos(1), i * cos(angle), i * sin(angle); // 初始化状态向量
                    predPointTemp.clear();
                    predPointTemp.push_back(currPos); // 添加当前位置
    
                    for (int k = 0; k < this->numPred_; k++) { // 遍历预测步数
                        Eigen::MatrixXd model = MatrixXd::Identity(4, 4); // 初始化状态转移矩阵
                        model.block(0, 2, 2, 2) = Eigen::MatrixXd::Identity(2, 2) * this->dt_; // 设置速度对位置的影响
                        Eigen::VectorXd nextState = model * currState; // 计算下一状态
                        Eigen::Vector3d p;
                        p << nextState(0), nextState(1), currPos(2); // 提取下一位置
    
                        if (this->map_->isInflatedOccupied(p)) { // 如果预测点与障碍物发生碰撞
                            isValid = false; // 标记为无效
                            break;
                        } else {
                            predPointTemp.push_back(p); // 添加预测点
                        }
                        currState = nextState; // 更新当前状态
                        angle += j * this->dt_; // 更新角度
                        if (intentType == LEFT) { // 如果意图是左转
                            angle = min(angle, endAngle); // 限制角度范围
                        } else if (intentType == RIGHT) { // 如果意图是右转
                            angle = max(angle, endAngle); // 限制角度范围
                        }
                        double v = sqrt(pow(currState(2), 2) + pow(currState(3), 2)); // 计算速度大小
                        currState(2) = v * cos(angle); // 更新速度的 x 分量
                        currState(3) = v * sin(angle); // 更新速度的 y 分量
                    }
    
                    if (isValid) {
                        predPoints.push_back(predPointTemp); // 保存有效的预测点
                    } else {
                        isValid = true; // 重置标记
                    }
                }
            }
        }
    
        for (int i = 0; i < this->numPred_ + 1; i++) {
            predSize.push_back(currSize); // 添加预测尺寸
        }
    }
    
    // 停止模型：生成停止状态的预测点
    void predictor::modelStop(const Eigen::Vector3d &currPos, const Eigen::Vector3d &currVel, const Eigen::Vector3d &currSize, 
                              std::vector<std::vector<Eigen::Vector3d>> &predPoints, std::vector<Eigen::Vector3d> &predSize) {
        predPoints.clear(); // 清空预测点容器
        predSize.clear();   // 清空预测尺寸容器
        std::vector<Eigen::Vector3d> predPointTemp; // 临时存储预测点
        Eigen::Vector3d size = currSize; // 初始化尺寸为当前尺寸
        double vel = sqrt(pow(currVel(0), 2) + pow(currVel(1), 2)); // 计算当前速度的大小
    
        for (int i = 0; i < this->numPred_ + 1; i++) { // 遍历预测步数
            predPointTemp.push_back(currPos); // 添加当前位置到预测点
            predSize.push_back(size); // 添加当前尺寸到预测尺寸
            size(0) += 2 * min(vel, this->stopVel_) * this->dt_; // 更新尺寸的 x 方向
            size(1) += 2 * min(vel, this->stopVel_) * this->dt_; // 更新尺寸的 y 方向
        }
        predPoints.push_back(predPointTemp); // 保存预测点
    }
    
    // 生成轨迹：根据每种意图的一系列轨迹的轨迹点位置取平均值来确定该意图的最终预测轨迹
    void predictor::genTraj(const std::vector<std::vector<Eigen::Vector3d>> &predPoints, std::vector<Eigen::Vector3d> &predPos, 
                            std::vector<Eigen::Vector3d> &predSize) {
        predPos.clear(); // 清空预测位置容器
        for (int i = 0; i < this->numPred_ + 1; i++) { // 遍历预测步数
            double meanx = 0, meany = 0; // 初始化均值
            double variancex = 0, variancey = 0; // 初始化方差
            double sumx = 0, sumy = 0; // 初始化 x 和 y 的总和
            double sumVarx = 0, sumVary = 0; // 初始化 x 和 y 的方差总和
            int counter = 0; // 初始化计数器
    
            for (int j = 0; j < int(predPoints.size()); j++) { // 遍历所有预测点
                if (i < int(predPoints[j].size())) { // 如果当前预测点存在
                    sumx += predPoints[j][i](0); // 累加 x 坐标
                    sumy += predPoints[j][i](1); // 累加 y 坐标
                    counter++; // 计数器加 1
                }
            }
    
            if (counter) { // 如果计数器不为 0
                meanx = sumx / counter; // 计算 x 坐标的均值
                meany = sumy / counter; // 计算 y 坐标的均值
                for (int j = 0; j < int(predPoints.size()); j++) { // 遍历所有预测点
                    sumVarx += pow(predPoints[j][i](0) - meanx, 2); // 累加 x 坐标的方差
                    sumVary += pow(predPoints[j][i](1) - meany, 2); // 累加 y 坐标的方差
                }
                variancex = sumVarx / counter; // 计算 x 坐标的方差
                variancey = sumVary / counter; // 计算 y 坐标的方差
                Eigen::Vector3d p; // 创建位置向量
                p << meanx, meany, predPoints[0][0](2); // 设置位置向量
                predPos.push_back(p); // 添加位置到预测位置容器
                predSize[i](0) += 2 * sqrt(variancex) * this->zScore_; // 根据高斯分布计算置信区间并更新尺寸 x
                predSize[i](1) += 2 * sqrt(variancey) * this->zScore_; // 根据高斯分布计算置信区间并更新尺寸 y
            } else {
                break; // 如果没有预测点，退出循环
            }
        }
        this->positionCorrection(predPos, predPoints); // 如果均值轨迹发生碰撞，找到最近的轨迹；否则使用均值轨迹
    }
    
    // 位置校正：修正均值轨迹以避免碰撞
    void predictor::positionCorrection(std::vector<Eigen::Vector3d> &mean, const std::vector<std::vector<Eigen::Vector3d>> &predPoints) {
        bool isCollide = false; // 初始化碰撞标志
        for (int i = 0; i < int(mean.size()); i++) { // 遍历均值轨迹
            if (this->map_->isInflatedOccupied(mean[i])) { // 如果均值轨迹发生碰撞
                isCollide = true; // 设置碰撞标志为 true
                break; // 退出循环
            }
        }
    
        double sum = 0; // 初始化距离总和
        double minSum = INFINITY; // 初始化最小距离总和为无穷大
        int minIdx = -1; // 初始化最小距离索引为 -1
        if (isCollide) { // 如果发生碰撞
            for (int i = 0; i < int(predPoints.size()); i++) { // 遍历所有预测点
                sum = 0; // 重置距离总和
                for (int j = 0; j < int(mean.size()); j++) { // 遍历均值轨迹
                    sum += sqrt(pow(predPoints[i][j](0) - mean[j](0), 2) + pow(predPoints[i][j](1) - mean[j](1), 2)); // 计算预测点与均值轨迹的距离
                    if (sum > minSum) { // 如果当前距离总和大于最小距离总和
                        break; // 退出循环
                    }
                }
                if (sum < minSum) { // 如果当前距离总和小于最小距离总和
                    minSum = sum; // 更新最小距离总和
                    minIdx = i; // 更新最小距离索引
                }
            }
            mean = predPoints[minIdx]; // 使用最近的预测点替代均值轨迹
        }
    }
    
    // 发布方差点云
    void predictor::publishVarPoints() {
        visualization_msgs::MarkerArray trajMsg; // 创建轨迹消息数组
        int countMarker = 0; // 初始化标记计数器
        for (size_t i = 0; i < this->allPredPoints_.size(); ++i) { // 遍历所有预测点
            visualization_msgs::Marker traj; // 创建轨迹标记
            traj.header.frame_id = "map"; // 设置坐标系为地图
            traj.header.stamp = ros::Time::now(); // 设置时间戳为当前时间
            traj.ns = "predictor"; // 设置命名空间为预测器
            traj.id = countMarker; // 设置标记 ID
            traj.type = visualization_msgs::Marker::POINTS; // 设置标记类型为点
            traj.scale.x = 0.03; // 设置点的 x 方向大小
            traj.scale.y = 0.03; // 设置点的 y 方向大小
            traj.scale.z = 0.03; // 设置点的 z 方向大小
            traj.color.a = 1.0; // 设置透明度
            traj.color.r = 0.0; // 设置红色分量
            traj.color.g = 0.0; // 设置绿色分量
            traj.color.b = 1.0; // 设置蓝色分量
            traj.lifetime = ros::Duration(0.1); // 设置标记的生命周期
    
            for (size_t j = 0; j < this->allPredPoints_[i].size(); ++j) { // 遍历每个预测点
                for (size_t k = 0; k < this->allPredPoints_[i][j].size(); k++) { // 遍历每个意图的预测点
                    for (size_t l = 0; l < this->allPredPoints_[i][j][k].size(); l++) { // 遍历每个时间步的预测点
                        geometry_msgs::Point p; // 创建点消息
                        Eigen::Vector3d pos = this->allPredPoints_[i][j][k][l]; // 获取预测点的位置
                        p.x = pos(0); p.y = pos(1); p.z = pos(2); // 设置点的坐标
                        double meanx, meany; // 初始化均值
                        double stdx, stdy; // 初始化标准差
                        meanx = this->posPred_[i][j][l](0); // 获取 x 坐标的均值
                        meany = this->posPred_[i][j][l](1); // 获取 y 坐标的均值
                        stdx = this->sizePred_[i][j][l](0) - this->sizeHist_[i][0](0); // 计算 x 坐标的标准差
                        stdy = this->sizePred_[i][j][l](1) - this->sizeHist_[i][0](1); // 计算 y 坐标的标准差
                        if (p.x <= meanx + stdx / 2 and p.x >= meanx - stdx / 2) { // 如果点的 x 坐标在均值范围内
                            if (p.y <= meany + stdy / 2 and p.y >= meany - stdy / 2) { // 如果点的 y 坐标在均值范围内
                                traj.points.push_back(p); // 添加点到轨迹标记
                            }
                        }
                    }
                }
            }
    
            ++countMarker; // 增加标记计数器
            trajMsg.markers.push_back(traj); // 添加轨迹标记到消息数组
        }
        this->varPointsPub_.publish(trajMsg); // 发布轨迹消息
    }

    // 发布历史轨迹
    void predictor::publishHistoryTraj() {
        visualization_msgs::MarkerArray trajMsg; // 创建轨迹消息数组
        int countMarker = 0; // 初始化标记计数器
        for (size_t i = 0; i < this->posHist_.size(); ++i) { // 遍历每个障碍物的历史轨迹
            visualization_msgs::Marker traj; // 创建轨迹标记
            traj.header.frame_id = "map"; // 设置坐标系为地图
            traj.header.stamp = ros::Time::now(); // 设置时间戳为当前时间
            traj.ns = "predictor"; // 设置命名空间为预测器
            traj.id = countMarker; // 设置标记 ID
            traj.type = visualization_msgs::Marker::LINE_STRIP; // 设置标记类型为线条
            traj.scale.x = 0.03; // 设置线条的 x 方向大小
            traj.scale.y = 0.03; // 设置线条的 y 方向大小
            traj.scale.z = 0.03; // 设置线条的 z 方向大小
            traj.color.a = 1.0; // 设置透明度
            traj.color.r = 0.0; // 设置红色分量
            traj.color.g = 1.0; // 设置绿色分量
            traj.color.b = 0.0; // 设置蓝色分量
            traj.lifetime = ros::Duration(0.1); // 设置标记的生命周期
    
            for (size_t j = 0; j < this->posHist_[i].size(); ++j) { // 遍历当前障碍物的历史轨迹点
                geometry_msgs::Point p; // 创建点消息
                Eigen::Vector3d pos = this->posHist_[i][j]; // 获取历史轨迹点的位置
                p.x = pos(0); p.y = pos(1); p.z = pos(2); // 设置点的坐标
                traj.points.push_back(p); // 将点添加到轨迹标记中
            }
    
            ++countMarker; // 增加标记计数器
            trajMsg.markers.push_back(traj); // 将轨迹标记添加到消息数组中
        }
        this->historyTrajPub_.publish(trajMsg); // 发布轨迹消息
    }
    
    // 发布预测轨迹
    void predictor::publishPredTraj() {
        if (this->posPred_.size() != 0) { // 如果存在预测轨迹
            visualization_msgs::MarkerArray trajMsg; // 创建轨迹消息数组
            int countMarker = 0; // 初始化标记计数器
            for (int i = 0; i < int(this->posPred_.size()); ++i) { // 遍历每个障碍物的预测轨迹
                for (int j = 0; j < int(this->posPred_[i].size()); j++) { // 遍历每种意图的预测轨迹
                    visualization_msgs::Marker traj; // 创建轨迹标记
                    traj.header.frame_id = "map"; // 设置坐标系为地图
                    traj.header.stamp = ros::Time::now(); // 设置时间戳为当前时间
                    traj.ns = "predictor"; // 设置命名空间为预测器
                    traj.id = countMarker; // 设置标记 ID
                    traj.type = visualization_msgs::Marker::LINE_STRIP; // 设置标记类型为线条
                    traj.scale.x = 0.1; // 设置线条的 x 方向大小
                    traj.scale.y = 0.1; // 设置线条的 y 方向大小
                    traj.scale.z = 0.1; // 设置线条的 z 方向大小
                    traj.color.a = 1.0; // 设置透明度
                    traj.color.r = 1.0; // 设置红色分量
                    traj.color.g = 0.0; // 设置绿色分量
                    traj.color.b = 0.0; // 设置蓝色分量
                    traj.lifetime = ros::Duration(0.1); // 设置标记的生命周期
    
                    for (int k = 0; k < int(this->posPred_[i][j].size()); ++k) { // 遍历当前意图的预测轨迹点
                        geometry_msgs::Point p; // 创建点消息
                        Eigen::Vector3d pos = this->posPred_[i][j][k]; // 获取预测轨迹点的位置
                        p.x = pos(0); p.y = pos(1); p.z = pos(2); // 设置点的坐标
                        traj.points.push_back(p); // 将点添加到轨迹标记中
                    }
                    ++countMarker; // 增加标记计数器
                    trajMsg.markers.push_back(traj); // 将轨迹标记添加到消息数组中
                }
            }
            this->predTrajPub_.publish(trajMsg); // 发布轨迹消息
        }
    }
    
    // 发布意图可视化
    void predictor::publishIntentVis() {
        visualization_msgs::MarkerArray intentVisMsg; // 创建意图可视化消息数组
        int countMarker = 0; // 初始化标记计数器
        for (int i = 0; i < int(this->posHist_.size()); ++i) { // 遍历每个障碍物
            visualization_msgs::Marker intentMarker; // 创建意图标记
            intentMarker.header.frame_id = "map"; // 设置坐标系为地图
            intentMarker.header.stamp = ros::Time::now(); // 设置时间戳为当前时间
            intentMarker.ns = "predictor"; // 设置命名空间为预测器
            intentMarker.id = countMarker; // 设置标记 ID
            intentMarker.type = visualization_msgs::Marker::TEXT_VIEW_FACING; // 设置标记类型为面向视图的文本
            intentMarker.pose.position.x = this->posHist_[i][0](0); // 设置文本位置的 x 坐标
            intentMarker.pose.position.y = this->posHist_[i][0](1); // 设置文本位置的 y 坐标
            intentMarker.pose.position.z = this->posHist_[i][0](2) + this->sizeHist_[i][0](2) / 2. + 0.3; // 设置文本位置的 z 坐标
            intentMarker.scale.x = 0.35; // 设置文本的 x 方向缩放
            intentMarker.scale.y = 0.35; // 设置文本的 y 方向缩放
            intentMarker.scale.z = 0.35; // 设置文本的 z 方向缩放
            intentMarker.color.a = 1.0; // 设置透明度
            intentMarker.color.r = 1.0; // 设置红色分量
            intentMarker.color.g = 0.0; // 设置绿色分量
            intentMarker.color.b = 0.0; // 设置蓝色分量
            intentMarker.lifetime = ros::Duration(0.1); // 设置标记的生命周期
    
            // 创建意图文本
            std::vector<std::string> intentText(4); // 创建意图文本数组
            intentText[FORWARD] = "Front: " + std::to_string(this->intentProb_[i](FORWARD)); // 前进意图文本
            intentText[LEFT] = "Left: " + std::to_string(this->intentProb_[i](LEFT)); // 左转意图文本
            intentText[RIGHT] = "Right: " + std::to_string(this->intentProb_[i](RIGHT)); // 右转意图文本
            intentText[STOP] = "Stop: " + std::to_string(this->intentProb_[i](STOP)); // 停止意图文本
    
            // 找到概率最大的意图
            int maxIdx = 0; // 初始化最大概率意图索引
            double max = 0; // 初始化最大概率值
            for (int j = 0; j < this->numIntent_; j++) { // 遍历所有意图
                if (this->intentProb_[i](j) > max) { // 如果当前意图的概率大于最大概率
                    maxIdx = j; // 更新最大概率意图索引
                    max = this->intentProb_[i](j); // 更新最大概率值
                }
            }
            intentMarker.text = intentText[maxIdx]; // 设置文本为最大概率意图
            intentVisMsg.markers.push_back(intentMarker); // 将意图标记添加到消息数组中
            ++countMarker; // 增加标记计数器
        }
        this->intentVisPub_.publish(intentVisMsg); // 发布意图可视化消息
    }
    // 发布预测包围盒
    void predictor::publishPredBBox() {
        if (this->posPred_.size() == 0) return; // 如果没有预测轨迹，则直接返回
    
        // 创建用于发布包围盒的消息数组
        visualization_msgs::MarkerArray predBBoxMsg;
    
        // 创建线条标记，用于绘制包围盒的边框
        visualization_msgs::Marker line;
        line.header.frame_id = "map"; // 设置坐标系为地图
        line.type = visualization_msgs::Marker::LINE_LIST; // 设置标记类型为线条列表
        line.action = visualization_msgs::Marker::ADD; // 设置标记操作为添加
        line.ns = "box3D"; // 设置命名空间为 "box3D"
        line.scale.x = 0.06; // 设置线条的宽度
        line.color.r = 0; // 设置线条颜色为绿色
        line.color.g = 1;
        line.color.b = 0;
        line.color.a = 1.0; // 设置线条的透明度
        line.lifetime = ros::Duration(0.1); // 设置标记的生命周期
    
        // 创建范围标记，用于显示预测障碍物的大小
        visualization_msgs::Marker range;
        range.header.frame_id = "map"; // 设置坐标系为地图
        range.header.stamp = ros::Time::now(); // 设置时间戳为当前时间
        range.ns = "pred_ob_size"; // 设置命名空间为 "pred_ob_size"
        range.id = 0; // 初始化标记 ID
        range.type = visualization_msgs::Marker::SPHERE; // 设置标记类型为球体
        range.action = visualization_msgs::Marker::ADD; // 设置标记操作为添加
        range.color.a = 0.4; // 设置球体的透明度
        range.color.r = 0.0; // 设置球体颜色为蓝色
        range.color.g = 0.0;
        range.color.b = 1.0;
        range.lifetime = ros::Duration(0.1); // 设置标记的生命周期
    
        // 遍历每个障碍物的意图概率
        for (int i = 0; i < int(this->intentProb_.size()); ++i) {
            std::vector<std::pair<double, int>> intentProb; // 存储意图概率及其索引
            for (int j = 0; j < this->numIntent_; ++j) {
                intentProb.push_back({this->intentProb_[i](j), j}); // 将意图概率和索引存入向量
            }
    
            // 按照概率从大到小排序
            std::sort(intentProb.begin(), intentProb.end(),
                      [](const std::pair<double, int> &left, const std::pair<double, int> &right) {
                          return left.first > right.first;
                      });
    
            // 仅处理概率最高的意图（Top N 意图，这里 N = 1）
            for (int n = 0; n < 1; ++n) {
                int intentIdx = intentProb[n].second; // 获取最高概率意图的索引
                std::vector<Eigen::Vector3d> predTraj = this->posPred_[i][intentIdx]; // 获取对应意图的预测轨迹
                std::vector<Eigen::Vector3d> predSize = this->sizePred_[i][intentIdx]; // 获取对应意图的预测尺寸
    
                // 遍历预测轨迹中的点，每隔 10 个时间步处理一次
                for (int t = 10; t < int(predTraj.size()); t += 10) {
                    Eigen::Vector3d obPos = predTraj[t]; // 获取当前时间步的预测位置
                    Eigen::Vector3d predObSize = predSize[t]; // 获取当前时间步的预测尺寸
                    Eigen::Vector3d obSize = this->sizeHist_[i][0]; // 获取障碍物的历史尺寸
    
                    // 计算包围盒的中心位置和尺寸
                    double x = obPos(0);
                    double y = obPos(1);
                    double z = (obPos(2) + obSize(2) / 2) / 2;
                    double x_width = obSize(0);
                    double y_width = obSize(1);
                    double z_width = 2 * obPos(2);
    
                    // 创建包围盒的顶点
                    std::vector<geometry_msgs::Point> verts;
                    geometry_msgs::Point p;
    
                    // 顶点 0
                    p.x = x - x_width / 2.;
                    p.y = y - y_width / 2.;
                    p.z = z - z_width / 2.;
                    verts.push_back(p);
    
                    // 顶点 1
                    p.x = x - x_width / 2.;
                    p.y = y + y_width / 2.;
                    p.z = z - z_width / 2.;
                    verts.push_back(p);
    
                    // 顶点 2
                    p.x = x + x_width / 2.;
                    p.y = y + y_width / 2.;
                    p.z = z - z_width / 2.;
                    verts.push_back(p);
    
                    // 顶点 3
                    p.x = x + x_width / 2.;
                    p.y = y - y_width / 2.;
                    p.z = z - z_width / 2.;
                    verts.push_back(p);
    
                    // 顶点 4
                    p.x = x - x_width / 2.;
                    p.y = y - y_width / 2.;
                    p.z = z + z_width / 2.;
                    verts.push_back(p);
    
                    // 顶点 5
                    p.x = x - x_width / 2.;
                    p.y = y + y_width / 2.;
                    p.z = z + z_width / 2.;
                    verts.push_back(p);
    
                    // 顶点 6
                    p.x = x + x_width / 2.;
                    p.y = y + y_width / 2.;
                    p.z = z + z_width / 2.;
                    verts.push_back(p);
    
                    // 顶点 7
                    p.x = x + x_width / 2.;
                    p.y = y - y_width / 2.;
                    p.z = z + z_width / 2.;
                    verts.push_back(p);
    
                    // 定义包围盒的边
                    int vert_idx[12][2] = {
                        {0, 1}, {1, 2}, {2, 3}, {0, 3}, {0, 4}, {1, 5},
                        {3, 7}, {2, 6}, {4, 5}, {5, 6}, {4, 7}, {6, 7}};
    
                    // 添加边到线条标记
                    for (size_t i = 0; i < 12; i++) {
                        line.points.push_back(verts[vert_idx[i][0]]);
                        line.points.push_back(verts[vert_idx[i][1]]);
                    }
    
                    predBBoxMsg.markers.push_back(line); // 将线条标记添加到消息数组
                    line.id++; // 更新线条标记的 ID
    
                    // 设置范围标记的位置和尺寸
                    range.pose.position.x = obPos(0);
                    range.pose.position.y = obPos(1);
                    range.pose.position.z = obPos(2);
                    range.scale.x = predObSize(0);
                    range.scale.y = predObSize(1);
                    range.scale.z = 0.1;
                    range.id++; // 更新范围标记的 ID
                    predBBoxMsg.markers.push_back(range); // 将范围标记添加到消息数组
                }
            }
        }
        this->predBBoxPub_.publish(predBBoxMsg); // 发布包围盒消息
    }
    // 获取预测结果（位置、尺寸和意图概率）
    void predictor::getPrediction(std::vector<std::vector<std::vector<Eigen::Vector3d>>> &predPos, 
                                   std::vector<std::vector<std::vector<Eigen::Vector3d>>> &predSize, 
                                   std::vector<Eigen::VectorXd> &intentProb) {
        if (this->sizePred_.size()) { // 如果预测尺寸数据不为空
            predPos = this->posPred_; // 将位置预测结果赋值给输出参数
            predSize = this->sizePred_; // 将尺寸预测结果赋值给输出参数
            intentProb = this->intentProb_; // 将意图概率赋值给输出参数
        } else { // 如果预测尺寸数据为空
            predPos.clear(); // 清空位置预测结果
            predSize.clear(); // 清空尺寸预测结果
            intentProb.clear(); // 清空意图概率
        }
    }
    
    // 获取预测结果（障碍物对象）
    void predictor::getPrediction(std::vector<dynamicPredictor::obstacle> &predOb) {
        for (int i = 0; i < int(this->posPred_.size()); i++) { // 遍历每个障碍物的预测结果
            dynamicPredictor::obstacle ob; // 创建一个障碍物对象
            ob.posPred = this->posPred_[i]; // 设置障碍物的预测位置
            ob.sizePred = this->sizePred_[i]; // 设置障碍物的预测尺寸
            ob.intentProb = this->intentProb_[i]; // 设置障碍物的意图概率
            predOb.push_back(ob); // 将障碍物对象添加到输出参数中
        }
    }

    // 发布预测数据
    void predictor::publishPredData() {
        dynamic_predictor::PredictionData predDataMsg; // 创建预测数据消息
        predDataMsg.header.stamp = ros::Time::now(); // 设置时间戳为当前时间
        predDataMsg.header.frame_id = "map"; // 设置坐标系
        
        // 检查是否有预测数据
        if (this->intentProb_.size() == 0) {
            // 没有数据时，发布空消息
            predDataMsg.num_obstacles = 0;
            predDataMsg.num_intents = 4;
            predDataMsg.num_time_steps = 0;
            predDataMsg.intent_probs_data.clear();
            predDataMsg.pos_pred_data.clear();
            predDataMsg.size_pred_data.clear();
        } else {
            int numObstacles = this->intentProb_.size();
            int numIntents = 4;  // 固定为4种意图
            int numTimeSteps = this->numPred_ + 1;  // 时间步数 = numPred_ + 1

            predDataMsg.num_obstacles = numObstacles;
            predDataMsg.num_intents = numIntents; // FORWARD, LEFT, RIGHT, STOP
            
            // 处理意图概率数据 - 展开为一维数组 [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, ...]
            predDataMsg.intent_probs_data.clear();
            predDataMsg.intent_probs_data.reserve(numObstacles * 4);
            
            for (int i = 0; i < numObstacles; i++) {
                for (int j = 0; j < numIntents; j++) {
                    predDataMsg.intent_probs_data.push_back(this->intentProb_[i](j));
                }
            }

            predDataMsg.num_time_steps = numTimeSteps;
            // 预分配空间
            int totalPoints = numObstacles * numIntents * numTimeSteps;
            predDataMsg.pos_pred_data.clear();
            predDataMsg.pos_pred_data.reserve(totalPoints);
            predDataMsg.size_pred_data.clear();
            predDataMsg.size_pred_data.reserve(totalPoints);
            
            // 按 [障碍物][意图][时间步] 的顺序展开数据
            for (int i = 0; i < numObstacles; i++) {
                for (int j = 0; j < numIntents; j++) {
                    for (int k = 0; k < numTimeSteps; k++) {
                        // 处理位置数据
                        geometry_msgs::Point pos;
                        pos.x = this->posPred_[i][j][k](0);
                        pos.y = this->posPred_[i][j][k](1);
                        pos.z = this->posPred_[i][j][k](2);
                        predDataMsg.pos_pred_data.push_back(pos);
                        // 处理尺寸数据
                        geometry_msgs::Point size;
                        size.x = this->sizePred_[i][j][k](0);
                        size.y = this->sizePred_[i][j][k](1);
                        size.z = this->sizePred_[i][j][k](2);
                        predDataMsg.size_pred_data.push_back(size);
                    }
                }
            }

        }
        this->predDataPub_.publish(predDataMsg); // 发布预测数据消息
    }
}