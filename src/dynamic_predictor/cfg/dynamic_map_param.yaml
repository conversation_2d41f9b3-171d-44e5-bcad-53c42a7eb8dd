sensor_input_mode: 0 # 0 camera 1: lidar 2: both
localization_mode: 1 # 0: pose (default) 1: odom
depth_image_topic: /camera/depth/image_raw
odom_topic: /global_odom/odom

# robot size
robot_size: [0.35, 0.35, 0.3]

# Camera Parameters
# depth_intrinsics: [386.22674560546875, 386.22674560546875, 317.3930969238281, 239.78431701660156] # fx, fy, cx, cy real   rostopic echo  /camera/depth/camera_info获取
depth_intrinsics: [319.9348449707031, 319.9348449707031, 320.0, 240.0] # fx,  fy, cx, cy simulation  
depth_scale_factor: 10 # 1000 for Intel Realsense Camera
depth_min_value: 0.5
depth_max_value: 5.0
depth_filter_margin: 2 # filter
depth_skip_pixel: 2 # filter
image_cols: 640
image_rows: 480
body_to_camera: [0.0,  0.0,  1.0,  0.03,
                -1.0,  0.0,  0.0,  0.0 ,   
                 0.0, -1.0,  0.0,  0.06,
                 0.0,  0.0,  0.0,  1.0]
body_to_lidar:  [1.0,  0.0,  0.0,  0.03,
                 0.0,  1.0,  0.0,  0.0 ,   
                 0.0,  0.0,  1.0,  0.06,
                 0.0,  0.0,  0.0,  1.0]

# Raycasting
raycast_max_length: 5.0
p_hit: 0.70
p_miss: 0.35
p_min: 0.12
p_max: 0.97
p_occ: 0.80


# Map
map_resolution: 0.05
ground_height: -0.1 # m
map_size: [18, 9, 3] # meter. in x y z direction (reserved size)
local_update_range: [10, 10, 10]
local_bound_inflation: 3.0 # inflate local bound in meter
clean_local_map: false

# visualziation
local_map_size: [20, 11, 4] # meter. in x y z direction (only for visualization)
max_height_visualization: 5 # m
visualize_global_map: true
verbose: false

prebuilt_map_directory: "/cfg/saved_map/sim_map.pcd" 