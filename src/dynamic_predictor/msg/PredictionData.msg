# 动态障碍物预测数据消息
# 对应C++数据结构：
# - std::vector<Eigen::VectorXd> intentProb_
# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_

std_msgs/Header header

# ========== 数据维度信息 ==========
int32 num_obstacles          # 障碍物数量
int32 num_intents           # 意图数量 (固定为4: FORWARD=0, LEFT=1, RIGHT=2, STOP=3)
int32 num_time_steps        # 预测时间步数

# ========== 意图概率数据 ==========
# 对应: std::vector<Eigen::VectorXd> intentProb_
# 数据排列: [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, obs1_intent1, ...]
# 访问方式: prob = intent_probs_data[obstacle_idx * num_intents + intent_idx]
float64[] intent_probs_data

# ========== 位置预测数据 ==========
# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
# 访问方式: pos = pos_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
geometry_msgs/Point[] pos_pred_data

# ========== 尺寸预测数据 ==========
# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_
# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
# 访问方式: size = size_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
geometry_msgs/Point[] size_pred_data 