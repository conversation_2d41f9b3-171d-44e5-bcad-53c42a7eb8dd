/*
	FILE: trackingController.cpp
	-------------------------------
	function implementation of px4 tracking controller
*/

#include <tracking_controller/trackingController.h>

namespace controller{
		trackingController::trackingController(const ros::NodeHandle& nh) : nh_(nh){
		// 构造函数，初始化 ROS 节点句柄并调用初始化函数
		this->initParam();       // 初始化控制器参数
		this->registerPub();     // 注册 ROS 发布者
		this->registerCallback(); // 注册 ROS 回调函数
	}
	
	void trackingController::initParam(){
		// 初始化控制器参数
		// 机体速率控制
		if (not this->nh_.getParam("controller/body_rate_control", this->bodyRateControl_)){
			this->bodyRateControl_ = true; 
			cout << "[trackingController]: No body rate control param. Use default: acceleration control." << endl;
		}
		else{
			// 如果参数已设置，输出当前机体速率控制的值
			cout << "[trackingController]: Body rate control is set to: " << this->bodyRateControl_  << endl;
		}	

		std::vector<double> mapSizeTemp;
		if (not this->nh_.getParam("controller/map_size", mapSizeTemp)){
			this->map_size_ = Eigen::Vector3d(18.0, 9.0, 3.0); 
			cout << "[trackingController]: No map size param. Use default: [18.0, 9.0, 3.0]." << endl;
		}
		else{
			// 如果参数已设置，输出当前机体速率控制的值
			this->map_size_(0) = mapSizeTemp[0];
			this->map_size_(1) = mapSizeTemp[1];
			this->map_size_(2) = mapSizeTemp[2];
			cout << "[trackingController]: Map size is set to: [" << this->map_size_(0) << ", " << this->map_size_(1) << ", " << this->map_size_(2) << "]." << endl;
		}	
	
		// 姿态控制
		if (not this->nh_.getParam("controller/attitude_control", this->attitudeControl_)){
			this->attitudeControl_ = true; 
			cout << "[trackingController]: No attitude control param. Use default: acceleration control." << endl;
		}
		else{
			// 如果参数已设置，输出当前姿态控制的值
			cout << "[trackingController]: Attitude control is set to: " << this->attitudeControl_  << endl;
		}		
	
		// 加速度控制
		if (not this->nh_.getParam("controller/acceleration_control", this->accControl_)){
			this->accControl_ = true; 
			cout << "[trackingController]: No acceleration control param. Use default: acceleration control." << endl;
		}
		else{
			// 如果参数已设置，输出当前加速度控制的值
			cout << "[trackingController]: Acceleration control is set to: " << this->accControl_  << endl;
		}			
	
		// 检查是否设置了位置控制的比例增益参数 "controller/position_p"
		std::vector<double> pPosTemp;
		if (not this->nh_.getParam("controller/position_p", pPosTemp)){
			// 如果未设置参数，默认比例增益为 [1.0, 1.0, 1.0]
			this->pPos_(0) = 1.0;
			this->pPos_(1) = 1.0;
			this->pPos_(2) = 1.0;
			cout << "[trackingController]: No position p param. Use default: [1.0, 1.0, 1.0]." << endl;
		}
		else{
			// 如果参数已设置，读取并设置比例增益
			this->pPos_(0) = pPosTemp[0];
			this->pPos_(1) = pPosTemp[1];
			this->pPos_(2) = pPosTemp[2];			
			cout << "[trackingController]: Position p is set to: " << "[" << this->pPos_(0) << ", " << this->pPos_(1) << ", " << this->pPos_(2) << "]." << endl;
		}	
	
		// 检查是否设置了位置控制的积分增益参数 "controller/position_i"
		std::vector<double> iPosTemp;
		if (not this->nh_.getParam("controller/position_i", iPosTemp)){
			// 如果未设置参数，默认积分增益为 [0.0, 0.0, 0.0]
			this->iPos_(0) = 0.0;
			this->iPos_(1) = 0.0;
			this->iPos_(2) = 0.0;
			cout << "[trackingController]: No position i param. Use default: [0.0, 0.0, 0.0]." << endl;
		}
		else{
			// 如果参数已设置，读取并设置积分增益
			this->iPos_(0) = iPosTemp[0];
			this->iPos_(1) = iPosTemp[1];
			this->iPos_(2) = iPosTemp[2];			
			cout << "[trackingController]: Position i is set to: " << "[" << this->iPos_(0) << ", " << this->iPos_(1) << ", " << this->iPos_(2) << "]." << endl;
		}
	
		// 检查是否设置了位置控制的微分增益参数 "controller/position_d"
		std::vector<double> dPosTemp;
		if (not this->nh_.getParam("controller/position_d", dPosTemp)){
			// 如果未设置参数，默认微分增益为 [0.0, 0.0, 0.0]
			this->dPos_(0) = 0.0;
			this->dPos_(1) = 0.0;
			this->dPos_(2) = 0.0;
			cout << "[trackingController]: No position d param. Use default: [0.0, 0.0, 0.0]." << endl;
		}
		else{
			// 如果参数已设置，读取并设置微分增益
			this->dPos_(0) = dPosTemp[0];
			this->dPos_(1) = dPosTemp[1];
			this->dPos_(2) = dPosTemp[2];			
			cout << "[trackingController]: Position d is set to: " << "[" << this->dPos_(0) << ", " << this->dPos_(1) << ", " << this->dPos_(2) << "]." << endl;
		}

			// P for Velocity
		std::vector<double> pVelTemp; // 用于临时存储从参数服务器获取的速度控制比例增益
		if (not this->nh_.getParam("controller/velocity_p", pVelTemp)){
			// 如果未设置参数，默认比例增益为 [1.0, 1.0, 1.0]
			this->pVel_(0) = 1.0;
			this->pVel_(1) = 1.0;
			this->pVel_(2) = 1.0;			
			cout << "[trackingController]: No velocity p param. Use default: [1.0, 1.0, 1.0]." << endl;
		}
		else{
			// 如果参数已设置，读取并设置比例增益
			this->pVel_(0) = pVelTemp[0];
			this->pVel_(1) = pVelTemp[1];
			this->pVel_(2) = pVelTemp[2];	
			cout << "[trackingController]: Velocity p is set to:" << "[" << this->pVel_(0) << ", " << this->pVel_(1) << ", " << this->pVel_(2) << "]." << endl; 
		}
		
		// I for Velocity
		std::vector<double> iVelTemp; // 用于临时存储从参数服务器获取的速度控制积分增益
		if (not this->nh_.getParam("controller/velocity_i", iVelTemp)){
			// 如果未设置参数，默认积分增益为 [0.0, 0.0, 0.0]
			this->iVel_(0) = 0.0;
			this->iVel_(1) = 0.0;
			this->iVel_(2) = 0.0;			
			cout << "[trackingController]: No velocity p param. Use default: [0.0, 0.0, 0.0]." << endl;
		}
		else{
			// 如果参数已设置，读取并设置积分增益
			this->iVel_(0) = iVelTemp[0];
			this->iVel_(1) = iVelTemp[1];
			this->iVel_(2) = iVelTemp[2];	
			cout << "[trackingController]: Velocity i is set to:" << "[" << this->iVel_(0) << ", " << this->iVel_(1) << ", " << this->iVel_(2) << "]." << endl; 
		}
		
		// D for Velocity
		std::vector<double> dVelTemp; // 用于临时存储从参数服务器获取的速度控制微分增益
		if (not this->nh_.getParam("controller/velocity_d", dVelTemp)){
			// 如果未设置参数，默认微分增益为 [0.0, 0.0, 0.0]
			this->dVel_(0) = 0.0;
			this->dVel_(1) = 0.0;
			this->dVel_(2) = 0.0;			
			cout << "[trackingController]: No velocity p param. Use default: [0.0, 0.0, 0.0]." << endl;
		}
		else{
			// 如果参数已设置，读取并设置微分增益
			this->dVel_(0) = dVelTemp[0];
			this->dVel_(1) = dVelTemp[1];
			this->dVel_(2) = dVelTemp[2];	
			cout << "[trackingController]: Velocity d is set to:" << "[" << this->dVel_(0) << ", " << this->dVel_(1) << ", " << this->dVel_(2) << "]." << endl; 
		}
		
		// Attitude control tau (attitude controller by body rate)
		if (not this->nh_.getParam("controller/attitude_control_tau", this->attitudeControlTau_)){
			// 如果未设置参数，默认姿态控制时间常数为 0.3
			this->attitudeControlTau_ = 0.3;
			cout << "[trackingController]: No attitude control tau param. Use default: 0.3." << endl;
		}
		else{
			// 如果参数已设置，输出当前姿态控制时间常数
			cout << "[trackingController]: Attitude control tau is set to: " << this->attitudeControlTau_  << endl;
		}
		
		// Estimated Maximum acceleration
		if (not this->nh_.getParam("controller/hover_thrust", this->hoverThrust_)){
			// 如果未设置参数，默认悬停推力为 0.738
			this->hoverThrust_ = 0.738;
			cout << "[trackingController]: No hover thrust param. Use default: 0.738." << endl;
		}
		else{
			// 如果参数已设置，输出当前悬停推力
			cout << "[trackingController]: Hover thrust is set to: " << this->hoverThrust_  << endl;
		}
		
		// Display message
		if (not this->nh_.getParam("controller/verbose", this->verbose_)){
			// 如果未设置参数，默认不显示详细信息
			this->verbose_ = false;
			cout << "[trackingController]: No display message param. Use default: false." << endl;
		}
		else{
			// 如果参数已设置，输出当前详细信息显示状态
			cout << "[trackingController]: Display message is set to: " << this->verbose_  << endl;
		}
	}

		void trackingController::registerPub(){
		// 注册命令发布器，用于发布姿态控制命令
		this->cmdPub_ = this->nh_.advertise<mavros_msgs::AttitudeTarget>("/mavros/setpoint_raw/attitude", 100);
	
		this->accCmdPub_ = this->nh_.advertise<mavros_msgs::PositionTarget>("/mavros/setpoint_raw/local", 100);
		
		// 注册当前位姿可视化发布器，用于发布机器人当前位姿
		this->poseVisPub_ = this->nh_.advertise<geometry_msgs::PoseStamped>("/tracking_controller/robot_pose", 1);
	
		// 注册轨迹历史可视化发布器，用于发布机器人历史轨迹
		this->histTrajVisPub_ = this->nh_.advertise<nav_msgs::Path>("/tracking_controller/trajectory_history", 1);
	
		// 注册目标位姿可视化发布器，用于发布目标位姿
		this->targetVisPub_ = this->nh_.advertise<geometry_msgs::PoseStamped>("/tracking_controller/target_pose", 1);
	
		// 注册目标轨迹历史可视化发布器，用于发布目标的历史轨迹
		this->targetHistTrajVisPub_ = this->nh_.advertise<nav_msgs::Path>("/tracking_controller/target_trajectory_history", 1); 
	
		// 注册速度和加速度可视化发布器，用于发布速度和加速度信息
		this->velAndAccVisPub_ = this->nh_.advertise<visualization_msgs::Marker>("/tracking_controller/vel_and_acc_info", 1);
	}
	
	void trackingController::registerCallback(){
		// 注册里程计订阅器
		this->odomSub_ = this->nh_.subscribe("/global_odom/odom", 1, &trackingController::odomCB, this);
		// 注册 IMU 数据订阅器，用于接收惯性测量单元数据
		this->imuSub_ = this->nh_.subscribe("/mavros/imu/data", 1, &trackingController::imuCB, this);
	
		// 注册目标状态订阅器，用于接收目标的状态信息
		this->targetSub_ = this->nh_.subscribe("/planner/target_state", 1, &trackingController::targetCB, this);
	
		// 注册控制命令定时器，每 0.01 秒触发一次，用于发布控制命令
		this->cmdTimer_ = this->nh_.createTimer(ros::Duration(0.01), &trackingController::cmdCB, this);
	
		// 如果未启用加速度控制，则注册推力估计定时，因为bodyRateControl和attitudeControl需要用到准确的悬停推力值。
		if (not this->accControl_){
			// 推力估计定时器，每 0.01 秒触发一次
			this->thrustEstimatorTimer_ = this->nh_.createTimer(ros::Duration(0.01), &trackingController::thrustEstimateCB, this);
		}
	
		// // 注册可视化定时器，每 0.033 秒触发一次，用于发布可视化信息
		// this->visTimer_ = this->nh_.createTimer(ros::Duration(0.033), &trackingController::visCB, this);
	}
	
	void trackingController::odomCB(const nav_msgs::OdometryConstPtr& odom){
		// 里程计回调函数，更新当前里程计数据
		this->odom_ = *odom; // 将接收到的里程计数据存储到成员变量中
		this->odomReceived_ = true; // 标记里程计数据已接收
	}
	
	void trackingController::imuCB(const sensor_msgs::ImuConstPtr& imu){
		// IMU 数据回调函数，更新当前 IMU 数据
		this->imuData_ = *imu; // 将接收到的 IMU 数据存储到成员变量中
		this->imuReceived_ = true; // 标记 IMU 数据已接收
	}
	
	void trackingController::targetCB(const tracking_controller::TargetConstPtr& target){
		// 目标状态回调函数，更新当前目标状态
		this->target_ = *target; // 将接收到的目标状态存储到成员变量中
		this->firstTargetReceived_ = true; // 标记首次接收到目标状态
		this->targetReceived_ = true; // 标记目标状态已接收
	}

	void trackingController::cmdCB(const ros::TimerEvent&){
		// 控制命令回调函数，用于根据当前状态和目标状态计算控制命令并发布
		if (not this->odomReceived_ or not this->targetReceived_){return;} // 如果未接收到里程计或目标状态数据，则直接返回
		Eigen::Vector4d cmd; // 定义控制命令变量
	
		// 1. 根据期望加速度计算目标参考姿态
		Eigen::Vector4d attitudeRefQuat; // 目标参考姿态四元数
		Eigen::Vector3d accRef; // 目标参考加速度
		this->computeAttitudeAndAccRef(attitudeRefQuat, accRef); // 调用函数计算参考姿态和加速度
	
		if (this->bodyRateControl_){
			// 2. 如果启用了机体速率控制，则根据参考姿态计算机体速率
			this->computeBodyRate(attitudeRefQuat, accRef, cmd);
	
			// 3. 发布机体速率作为控制输入
			this->publishCommand(cmd);
		}
	
		if (this->attitudeControl_){
			// 如果启用了姿态控制，则直接使用参考姿态作为控制命令
			cmd = attitudeRefQuat;
			this->publishCommand(cmd, accRef); // 发布姿态控制命令
		}
	
		if (this->accControl_){
			// 如果启用了加速度控制，则发布加速度控制命令
			this->publishCommand(accRef);
		}
	
		this->targetReceived_ = false; // 重置目标接收标志
	}
	
	void trackingController::thrustEstimateCB(const ros::TimerEvent&){
		// 推力估计回调函数，用于估计悬停推力
		if (not this->thrustReady_ or not this->imuReceived_){return;} // 如果推力未准备好或未接收到 IMU 数据，则直接返回
		if (this->kfFirstTime_){
			// 如果是第一次运行卡尔曼滤波器，初始化时间戳并返回
			this->kfFirstTime_ = false;
			this->kfStartTime_ = ros::Time::now();
			return;
		}
	
		// 当命令推力可用时运行估计器
		double hoverThrust = this->hoverThrust_; // 当前悬停推力
		double cmdThrust = this->cmdThrust_; // 当前命令推力
		Eigen::Vector3d currAccBody (this->imuData_.linear_acceleration.x, this->imuData_.linear_acceleration.y, this->imuData_.linear_acceleration.z); // 当前机体加速度
		Eigen::Vector4d currQuat (this->odom_.pose.pose.orientation.w, this->odom_.pose.pose.orientation.x, this->odom_.pose.pose.orientation.y, this->odom_.pose.pose.orientation.z); // 当前姿态四元数
		Eigen::Matrix3d currRot = controller::quat2RotMatrix(currQuat); // 将四元数转换为旋转矩阵
		Eigen::Vector3d currAcc = currRot * currAccBody; // 将机体加速度转换为世界坐标系加速度
	
		// 状态变量：悬停推力
		double states = hoverThrust;
		double A = 1; // 状态转移矩阵
		double H = -(cmdThrust * 9.79362) * (1.0/pow(hoverThrust, 2)); // 测量矩阵
		double z = (currAcc(2) - 9.79362); // 测量值（加速度）
	
		// 卡尔曼滤波器预测步骤
		states = A * states; // 状态预测
		this->stateVar_ += this->processNoiseVar_; // 更新状态方差
	
		// 卡尔曼滤波器校正步骤
		double Ivar = std::max(H * this->stateVar_ * H + this->measureNoiseVar_, this->measureNoiseVar_); // 计算测量方差
		double K = this->stateVar_ * H/Ivar; // 计算卡尔曼增益
		double I = z - (cmdThrust/hoverThrust - 1.0) * 9.79362; // 计算残差
	
		// 更新悬停推力
		double newHoverThrust = hoverThrust + K * I; // 更新悬停推力估计值
		this->stateVar_ = (1.0 - K * H) * this->stateVar_; // 更新状态方差
	
		// if (this->verbose_){
		// 	// 如果启用了详细信息显示，输出估计方差
		// 	cout << "[trackingController]: Estimation variance: " << this->stateVar_ << endl;
		// }
	
		double prevMinThrust = 0.0; // 前一次最小推力
		double prevMaxThrust = 1.0; // 前一次最大推力
		if (this->prevEstimateThrusts_.size() < 10){
			// 如果历史推力估计值少于 10 个，则直接添加
			this->prevEstimateThrusts_.push_back(newHoverThrust);
		}
		else{
			// 如果历史推力估计值达到 10 个，则移除最早的一个并添加新的值
			this->prevEstimateThrusts_.pop_front();
			this->prevEstimateThrusts_.push_back(newHoverThrust);
			std::deque<double>::iterator itMin = std::min_element(this->prevEstimateThrusts_.begin(), this->prevEstimateThrusts_.end()); // 找到最小值
			std::deque<double>::iterator itMax = std::max_element(this->prevEstimateThrusts_.begin(), this->prevEstimateThrusts_.end()); // 找到最大值
			prevMinThrust = *itMin;
			prevMaxThrust = *itMax;
		}
	
		// 如果状态方差足够小，则更新悬停推力
		if (std::abs(prevMinThrust - prevMaxThrust) < 0.003){
			if (newHoverThrust > 0 and newHoverThrust < 1.0){
				// 如果新的悬停推力在合理范围内，则更新悬停推力
				this->hoverThrust_ = newHoverThrust;
				ros::Time currTime = ros::Time::now();
				double estimatedTime = (currTime - this->kfStartTime_).toSec(); // 计算估计时间
				if (this->verbose_){
					// 如果启用了详细信息显示，输出新的悬停推力估计值
					cout << "[trackingController]: New estimate at " << estimatedTime  << "s, and Estimated thrust is: " << newHoverThrust << ". Variance: " << this->stateVar_ << endl; 
				}
			}
			else{
				// 如果新的悬停推力不在合理范围内，输出失败信息
				cout << "[trackingController]: !!!!!!!!!!AUTO THRUST ESTIMATION FAILS!!!!!!!!!" << endl;
			}
		}
	}
		void trackingController::visCB(const ros::TimerEvent&){
		// 可视化回调函数，用于发布各种可视化信息
		this->publishPoseVis(); // 发布当前位姿的可视化信息
		this->publishHistTraj(); // 发布历史轨迹的可视化信息
		this->publishTargetVis(); // 发布目标位姿的可视化信息
		this->publishTargetHistTraj(); // 发布目标历史轨迹的可视化信息
		this->publishVelAndAccVis(); // 发布速度和加速度的可视化信息
	}
	
	void trackingController::publishCommand(const Eigen::Vector4d& cmd){
		// 发布机体速率控制命令
		mavros_msgs::AttitudeTarget cmdMsg; // 定义姿态目标消息
		cmdMsg.header.stamp = ros::Time::now(); // 设置消息的时间戳
		cmdMsg.header.frame_id = "map"; // 设置消息的参考坐标系
		cmdMsg.body_rate.x = cmd(0); // 设置机体速率的 x 分量
		cmdMsg.body_rate.y = cmd(1); // 设置机体速率的 y 分量
		cmdMsg.body_rate.z = cmd(2); // 设置机体速率的 z 分量
		cmdMsg.thrust = cmd(3); // 设置推力
		cmdMsg.type_mask = mavros_msgs::AttitudeTarget::IGNORE_ATTITUDE; // 忽略姿态信息，仅使用速率和推力
		//cout << cmd(0) << ", " << cmd(1) << ", " << cmd(2) << ", " << cmd(3) << endl;
		this->cmdPub_.publish(cmdMsg); // 发布命令消息
	}
	
	void trackingController::publishCommand(const Eigen::Vector4d& cmd, const Eigen::Vector3d& accRef){
		// 发布姿态控制命令
		mavros_msgs::AttitudeTarget cmdMsg; // 定义姿态目标消息
		cmdMsg.header.stamp = ros::Time::now(); // 设置消息的时间戳
		cmdMsg.header.frame_id = "map"; // 设置消息的参考坐标系
		cmdMsg.orientation.w = cmd(0); // 设置姿态四元数的 w 分量
		cmdMsg.orientation.x = cmd(1); // 设置姿态四元数的 x 分量
		cmdMsg.orientation.y = cmd(2); // 设置姿态四元数的 y 分量
		cmdMsg.orientation.z = cmd(3); // 设置姿态四元数的 z 分量
		double thrust = accRef.norm(); // 计算加速度的模长作为推力
		double thrustPercent = std::max(0.0, std::min(1.0, 1.0 * thrust / (9.79362 * 1.0 / this->hoverThrust_))); // 计算推力百分比
		this->cmdThrust_ = thrustPercent; // 更新当前推力百分比
		this->cmdThrustTime_ = ros::Time::now(); // 更新推力时间戳
		this->thrustReady_ = true; // 标记推力已准备好
		cmdMsg.thrust = thrustPercent; // 设置推力百分比
		cmdMsg.type_mask = mavros_msgs::AttitudeTarget::IGNORE_ROLL_RATE |
							mavros_msgs::AttitudeTarget::IGNORE_PITCH_RATE |
							mavros_msgs::AttitudeTarget::IGNORE_YAW_RATE; // 忽略速率信息，仅使用姿态和推力
		//cout << cmd(0) << ", " << cmd(1) << ", " << cmd(2) << ", " << cmd(3) << endl;
		this->cmdPub_.publish(cmdMsg); // 发布命令消息
	}
	
	void trackingController::publishCommand(const Eigen::Vector3d& accRef){
		// 发布加速度控制命令
		mavros_msgs::PositionTarget cmdMsg; // 定义位置目标消息
		cmdMsg.coordinate_frame = cmdMsg.FRAME_LOCAL_NED; // 设置坐标系为本地 NED 坐标系
		cmdMsg.header.stamp = ros::Time::now(); // 设置消息的时间戳
		cmdMsg.header.frame_id = "map"; // 设置消息的参考坐标系
		cmdMsg.acceleration_or_force.x = accRef(0); // 设置加速度的 x 分量
		cmdMsg.acceleration_or_force.y = accRef(1); // 设置加速度的 y 分量
		cmdMsg.acceleration_or_force.z = accRef(2) - 9.79362; // 设置加速度的 z 分量，减去重力加速度
		cmdMsg.yaw = this->target_.yaw; // 设置目标的偏航角
		cmdMsg.type_mask = cmdMsg.IGNORE_PX + cmdMsg.IGNORE_PY + cmdMsg.IGNORE_PZ + cmdMsg.IGNORE_VX + cmdMsg.IGNORE_VY + cmdMsg.IGNORE_VZ + cmdMsg.IGNORE_YAW_RATE; // 忽略位置、速度和偏航速率，仅使用加速度和偏航角
		this->accCmdPub_.publish(cmdMsg); // 发布命令消息
	}
	
	void trackingController::computeAttitudeAndAccRef(Eigen::Vector4d& attitudeRefQuat, Eigen::Vector3d& accRef){
		// 计算参考姿态和加速度
		// 参考加速度由以下四部分组成：
		// 1. 目标加速度（来自目标状态）
		// 2. 位置和速度的反馈控制加速度（PID 控制）
		// 3. 空气阻力（当前未考虑）
		// 4. 重力加速度
	
		if (this->firstTime_){
			// 如果是第一次运行，初始化时间和误差积分
			this->prevTime_ = ros::Time::now(); // 设置初始时间
			this->deltaTime_ = 0.0; // 时间增量初始化为 0
			this->posErrorInt_ = Eigen::Vector3d(0.0, 0.0, 0.0); // 初始化位置误差积分
			this->velErrorInt_ = Eigen::Vector3d(0.0, 0.0, 0.0); // 初始化速度误差积分
		}
		else{
			// 如果不是第一次运行，计算时间增量并更新时间
			ros::Time currTime = ros::Time::now(); // 获取当前时间
			this->deltaTime_ = (currTime - this->prevTime_).toSec(); // 计算时间增量
			this->prevTime_ = currTime; // 更新上一次时间
		}
	
		// 1. 目标加速度
		Eigen::Vector3d accTarget(this->target_.acceleration.x, this->target_.acceleration.y, this->target_.acceleration.z); // 从目标状态中获取目标加速度
	
		// 2. 位置和速度的反馈控制（PID 控制）
		Eigen::Vector3d currPos(this->odom_.pose.pose.position.x, this->odom_.pose.pose.position.y, this->odom_.pose.pose.position.z); // 当前位置信息
		Eigen::Vector3d currVel(this->odom_.twist.twist.linear.x, this->odom_.twist.twist.linear.y, this->odom_.twist.twist.linear.z); // 当前速度（机体坐标系）
		Eigen::Vector4d currQuat(this->odom_.pose.pose.orientation.w, this->odom_.pose.pose.orientation.x, this->odom_.pose.pose.orientation.y, this->odom_.pose.pose.orientation.z); // 当前姿态四元数
		Eigen::Vector3d targetPos(this->target_.position.x, this->target_.position.y, this->target_.position.z); // 目标位置
		Eigen::Vector3d targetVel(this->target_.velocity.x, this->target_.velocity.y, this->target_.velocity.z); // 目标速度
		Eigen::Vector3d positionError = targetPos - currPos; // 计算位置误差
		Eigen::Vector3d velocityError = targetVel - currVel; // 计算速度误差
		this->posErrorInt_ += this->deltaTime_ * positionError; // 更新位置误差积分
		this->velErrorInt_ += this->deltaTime_ * velocityError; // 更新速度误差积分
	
		if (this->firstTime_){
			// 如果是第一次运行，初始化误差变化量
			this->deltaPosError_ = Eigen::Vector3d(0.0, 0.0, 0.0); 
			this->prevPosError_ = positionError; // 初始化上一次位置误差
			this->deltaVelError_ = Eigen::Vector3d(0.0, 0.0, 0.0); 
			this->prevVelError_ = velocityError; // 初始化上一次速度误差
			this->firstTime_ = false; // 标记第一次运行完成
		}
		else{
			// 如果不是第一次运行，计算误差变化量
			this->deltaPosError_ = (positionError - this->prevPosError_) / this->deltaTime_; // 计算位置误差变化量
			this->prevPosError_ = positionError; // 更新上一次位置误差
			this->deltaVelError_ = (velocityError - this->prevVelError_) / this->deltaTime_; // 计算速度误差变化量
			this->prevVelError_ = velocityError; // 更新上一次速度误差
		}
	
		// 如果需要屏蔽速度输入，则将速度误差和积分清零
		if (this->target_.type_mask == this->target_.IGNORE_ACC_VEL){
			velocityError *= 0.0; // 清零速度误差
			this->velErrorInt_ *= 0.0; // 清零速度误差积分
			this->dVel_ *= 0.0; // 清零速度误差微分
		}
	
		// 计算反馈加速度
		Eigen::Vector3d accFeedback = this->pPos_.asDiagonal() * positionError + this->iPos_.asDiagonal() * this->posErrorInt_ + this->dPos_.asDiagonal() * this->deltaPosError_ +
									  this->pVel_.asDiagonal() * velocityError + this->iVel_.asDiagonal() * this->velErrorInt_ + this->dVel_.asDiagonal() * this->deltaVelError_;
	
		// 3. 空气阻力（当前未考虑）
		Eigen::Vector3d accAirdrag(0.0, 0.0, 0.0);
	
		// 4. 重力加速度
		Eigen::Vector3d gravity{0.0, 0.0, -9.79362};
	
		// 计算最终参考加速度
		if (this->target_.type_mask == this->target_.IGNORE_ACC_VEL or this->target_.type_mask == this->target_.IGNORE_ACC){
			accTarget *= 0.0; // 如果屏蔽加速度输入，则清零目标加速度
		}
		accRef = accTarget + accFeedback - accAirdrag - gravity; // 计算最终参考加速度
	
		// 将参考加速度转换为参考姿态
		double yaw = this->target_.yaw;
		Eigen::Vector3d direction(cos(yaw), sin(yaw), 0.0); // 偏航方向向量
		Eigen::Vector3d zDirection = accRef / accRef.norm(); // 计算 z 轴方向
		Eigen::Vector3d yDirection = zDirection.cross(direction) / (zDirection.cross(direction)).norm(); // 计算 y 轴方向
		Eigen::Vector3d xDirection = yDirection.cross(zDirection) / (yDirection.cross(zDirection)).norm(); // 计算 x 轴方向
	
		// 使用三个轴向量构造旋转矩阵
		Eigen::Matrix3d attitudeRefRot;
		attitudeRefRot << xDirection(0), yDirection(0), zDirection(0),
						  xDirection(1), yDirection(1), zDirection(1),
						  xDirection(2), yDirection(2), zDirection(2);
		attitudeRefQuat = controller::rot2Quaternion(attitudeRefRot); // 将旋转矩阵转换为四元数
	}
	void trackingController::computeBodyRate(const Eigen::Vector4d& attitudeRefQuat, const Eigen::Vector3d& accRef, Eigen::Vector4d& cmd){
		// 计算机体速率控制命令，由加速度和姿态计算推力
	
		// 获取当前姿态的四元数
		Eigen::Vector4d currAttitudeQuat(
			this->odom_.pose.pose.orientation.w,
			this->odom_.pose.pose.orientation.x,
			this->odom_.pose.pose.orientation.y,
			this->odom_.pose.pose.orientation.z
		);
	
		// 定义反向四元数，用于计算姿态误差
		Eigen::Vector4d inverseQuat(1.0, -1.0, -1.0, -1.0);
	
		// 计算当前姿态的反向四元数
		Eigen::Vector4d currAttitudeQuatInv = inverseQuat.asDiagonal() * currAttitudeQuat;
	
		// 计算姿态误差四元数
		Eigen::Vector4d attitudeErrorQuat = quatMultiplication(currAttitudeQuatInv, attitudeRefQuat);
	
		// 根据姿态误差计算机体速率控制命令
		cmd(0) = (2.0 / this->attitudeControlTau_) * std::copysign(1.0, attitudeErrorQuat(0)) * attitudeErrorQuat(1); // x 轴速率
		cmd(1) = (2.0 / this->attitudeControlTau_) * std::copysign(1.0, attitudeErrorQuat(0)) * attitudeErrorQuat(2); // y 轴速率
		cmd(2) = (2.0 / this->attitudeControlTau_) * std::copysign(1.0, attitudeErrorQuat(0)) * attitudeErrorQuat(3); // z 轴速率
	
		// 计算推力
		// Eigen::Matrix3d currAttitudeRot = quat2RotMatrix(currAttitudeQuat);
		// Eigen::Vector3d zDirection = currAttitudeRot.col(2); // body z axis 
		// double thrust = accRef.dot(zDirection); // thrust in acceleration
		double thrust = accRef.norm(); // 计算加速度的模长作为推力
		double thrustPercent = std::max(0.0, std::min(1.0, 1.0 * thrust / (9.79362 * 1.0 / this->hoverThrust_))); // 计算推力百分比
		this->cmdThrust_ = thrustPercent; // 更新当前推力百分比
		this->cmdThrustTime_ = ros::Time::now(); // 更新推力时间戳
		this->thrustReady_ = true; // 标记推力已准备好
		cmd(3) = thrustPercent; // 设置推力百分比
	
		// 如果启用了详细信息显示，输出推力百分比
		if (this->verbose_){
			cout << "[trackingController]: Thrust percent: " << thrustPercent << endl;
		}
	}
	
	void trackingController::publishPoseVis(){
		// 发布当前位姿的可视化信息
		if (not this->odomReceived_) return; // 如果未接收到里程计数据，则直接返回
	
		geometry_msgs::PoseStamped ps; // 定义位姿消息
		ps.header.frame_id = "map"; // 设置参考坐标系为 "map"
		ps.header.stamp = ros::Time::now(); // 设置时间戳
		ps.pose.position.x = this->odom_.pose.pose.position.x; // 设置位姿的 x 坐标
		ps.pose.position.y = this->odom_.pose.pose.position.y; // 设置位姿的 y 坐标
		ps.pose.position.z = this->odom_.pose.pose.position.z; // 设置位姿的 z 坐标
		ps.pose.orientation = this->odom_.pose.pose.orientation; // 设置位姿的方向（四元数）
	
		// 如果历史轨迹的大小小于等于 100，则直接添加当前位姿
		if (this->histTraj_.size() <= 100){
			this->histTraj_.push_back(ps);
		}
		else{
			// 如果历史轨迹的大小超过 100，则移除最早的一个位姿并添加当前位姿
			this->histTraj_.push_back(ps);
			this->histTraj_.pop_front();
		}
	
		this->poseVis_ = ps; // 更新当前位姿的可视化信息
		this->poseVisPub_.publish(ps); // 发布当前位姿的可视化信息
	}
	
	void trackingController::publishHistTraj(){
		// 发布历史轨迹的可视化信息
		if (not this->odomReceived_) return; // 如果未接收到里程计数据，则直接返回
	
		nav_msgs::Path histTrajMsg; // 定义路径消息
		histTrajMsg.header.frame_id = "map"; // 设置参考坐标系为 "map"
		histTrajMsg.header.stamp = ros::Time::now(); // 设置时间戳
	
		// 将历史轨迹中的每个位姿添加到路径消息中
		for (size_t i = 0; i < this->histTraj_.size(); ++i){
			histTrajMsg.poses.push_back(this->histTraj_[i]);
		}
	
		this->histTrajVisPub_.publish(histTrajMsg); // 发布历史轨迹的可视化信息
	}
	
	void trackingController::publishTargetVis(){
		// 发布目标位姿的可视化信息
		if (not this->firstTargetReceived_) return; // 如果未接收到目标状态数据，则直接返回
	
		geometry_msgs::PoseStamped ps; // 定义位姿消息
		ps.header.frame_id = "map"; // 设置参考坐标系为 "map"
		ps.header.stamp = ros::Time::now(); // 设置时间戳
		ps.pose.position.x = this->target_.position.x; // 设置目标位姿的 x 坐标
		ps.pose.position.y = this->target_.position.y; // 设置目标位姿的 y 坐标
		ps.pose.position.z = this->target_.position.z; // 设置目标位姿的 z 坐标
		ps.pose.orientation = controller::quaternion_from_rpy(0, 0, this->target_.yaw); // 设置目标位姿的方向（四元数）
	
		// 如果目标历史轨迹的大小小于等于 100，则直接添加当前目标位姿
		if (this->targetHistTraj_.size() <= 100){
			this->targetHistTraj_.push_back(ps);
		}
		else{
			// 如果目标历史轨迹的大小超过 100，则移除最早的一个位姿并添加当前目标位姿
			this->targetHistTraj_.push_back(ps);
			this->targetHistTraj_.pop_front();
		}
	
		this->targetPoseVis_ = ps; // 更新目标位姿的可视化信息
		this->targetVisPub_.publish(ps); // 发布目标位姿的可视化信息
	}
	
	void trackingController::publishTargetHistTraj(){
		// 发布目标历史轨迹的可视化信息
		if (not this->firstTargetReceived_) return; // 如果未接收到目标状态数据，则直接返回
	
		nav_msgs::Path targetHistTrajMsg; // 定义路径消息
		targetHistTrajMsg.header.frame_id = "map"; // 设置参考坐标系为 "map"
		targetHistTrajMsg.header.stamp = ros::Time::now(); // 设置时间戳
	
		// 将目标历史轨迹中的每个位姿添加到路径消息中
		for (size_t i = 0; i < this->targetHistTraj_.size(); ++i){
			targetHistTrajMsg.poses.push_back(this->targetHistTraj_[i]);
		}
	
		this->targetHistTrajVisPub_.publish(targetHistTrajMsg); // 发布目标历史轨迹的可视化信息
	}
	
	void trackingController::publishVelAndAccVis(){
		// 发布速度和加速度的可视化信息
		if (not this->odomReceived_) return; // 如果未接收到里程计数据，则直接返回
	
		// 当前速度
		Eigen::Vector3d currPos(
			this->odom_.pose.pose.position.x,
			this->odom_.pose.pose.position.y,
			this->odom_.pose.pose.position.z
		);
		Eigen::Vector3d currVel_(
			this->odom_.twist.twist.linear.x,
			this->odom_.twist.twist.linear.y,
			this->odom_.twist.twist.linear.z
		);
		Eigen::Vector3d currVel = currVel_; // 将机体速度转换为世界坐标系速度
	
		// 当前加速度
		Eigen::Vector3d currAcc;
		ros::Time currTime = ros::Time::now(); // 获取当前时间
		if (this->velFirstTime_){
			// 如果是第一次运行，初始化时间和加速度
			this->velPrevTime_ = ros::Time::now(); // 设置初始时间
			currAcc = Eigen::Vector3d(0.0, 0.0, 0.0); // 初始化加速度为零
			this->velFirstTime_ = false; // 标记第一次运行完成
		}
		else{
			// 如果不是第一次运行，计算时间增量并计算加速度
			double dt = (currTime - this->velPrevTime_).toSec(); // 计算时间增量
			currAcc = (currVel - this->prevVel_) / dt; // 计算加速度
		}
		this->prevVel_ = currVel; // 更新上一次速度
		this->velPrevTime_ = currTime; // 更新上一次时间
	
		// 目标速度
		Eigen::Vector3d targetVel(
			this->target_.velocity.x,
			this->target_.velocity.y,
			this->target_.velocity.z
		);
	
		// 目标加速度
		Eigen::Vector3d targetAcc(
			this->target_.acceleration.x,
			this->target_.acceleration.y,
			this->target_.acceleration.z
		);
	
		// 定义可视化消息
		visualization_msgs::Marker velAndAccVisMsg;
		velAndAccVisMsg.header.frame_id = "map"; // 设置参考坐标系为 "map"
		velAndAccVisMsg.header.stamp = ros::Time::now(); // 设置时间戳
		velAndAccVisMsg.ns = "tracking_controller"; // 设置命名空间
		velAndAccVisMsg.type = visualization_msgs::Marker::TEXT_VIEW_FACING; // 设置标记类型为面向视图的文本
		velAndAccVisMsg.pose.position.x = this->odom_.pose.pose.position.x; // 设置文本位置的 x 坐标
		velAndAccVisMsg.pose.position.y = this->odom_.pose.pose.position.y; // 设置文本位置的 y 坐标
		velAndAccVisMsg.pose.position.z = this->odom_.pose.pose.position.z + 0.4; // 设置文本位置的 z 坐标
		velAndAccVisMsg.scale.x = 0.15; // 设置文本的 x 方向缩放
		velAndAccVisMsg.scale.y = 0.15; // 设置文本的 y 方向缩放
		velAndAccVisMsg.scale.z = 0.15; // 设置文本的 z 方向缩放
		velAndAccVisMsg.color.a = 1.0; // 设置文本的透明度
		velAndAccVisMsg.color.r = 1.0; // 设置文本的红色分量
		velAndAccVisMsg.color.g = 1.0; // 设置文本的绿色分量
		velAndAccVisMsg.color.b = 1.0; // 设置文本的蓝色分量
		velAndAccVisMsg.lifetime = ros::Duration(0.05); // 设置文本的生命周期
	
		// 计算速度和加速度的模长
		double vNorm = currVel.norm(); // 当前速度的模长
		double aNorm = currAcc.norm(); // 当前加速度的模长
		double vNormTgt = targetVel.norm(); // 目标速度的模长
		double aNormTgt = targetAcc.norm(); // 目标加速度的模长
	
		// 构造文本内容
		std::string velText = "|V|=" + std::to_string(vNorm) + ", |VT|=" + std::to_string(vNormTgt) +
							  "\n|A|=" + std::to_string(aNorm) + ", |AT|=" + std::to_string(aNormTgt);
		velAndAccVisMsg.text = velText; // 设置文本内容
	
		this->velAndAccVisPub_.publish(velAndAccVisMsg); // 发布速度和加速度的可视化信息
	}
}