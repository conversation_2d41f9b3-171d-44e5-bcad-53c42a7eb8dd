sensor_input_mode: 0 # 0 camera 1: lidar
localization_mode: 1 # 0: pose (default) 1: odom
depth_image_topic: /camera/depth/image_raw
# depth_image_topic: /no_topic
pose_topic: /mavros/local_position/pose
odom_topic: /mavros/local_position/odom

# robot size
robot_size: [0.8, 0.8, 0.3]
# robot_size: [0.2, 0.2, 0.2]

# Camera Parameters
depth_intrinsics: [554.254691191187, 554.254691191187, 320.5, 240.5] # fx,  fy, cx, cy
depth_scale_factor: 10 # 1000 for Intel Realsense Camera
depth_min_value: 0.3
depth_max_value: 5.0
depth_filter_margin: 2 # filter
depth_skip_pixel: 2 # filter
image_cols: 640
image_rows: 480
body_to_camera: [0.0,  0.0,  1.0,  0.09,
                -1.0,  0.0,  0.0,  0.0 ,   
                 0.0, -1.0,  0.0,  0.095,
                 0.0,  0.0,  0.0,  1.0]

# Raycasting
raycast_max_length: 5.0
p_hit: 0.70
p_miss: 0.35
p_min: 0.12
p_max: 0.97
p_occ: 0.80


# Map
map_resolution: 0.1
ground_height: -0.1 # m
map_size: [40, 40, 3] # meter. in x y z direction (reserved size)
# map_size: [50, 50, 3] # meter. in x y z direction (reserved size)
local_update_range: [5, 5, 5]
local_bound_inflation: 3.0 # inflate local bound in meter
clean_local_map: false

# visualziation
local_map_size: [20, 20, 6] # meter. in x y z direction (only for visualization)
max_height_visualization: 2.5 # m
visualize_global_map: true
verbose: false

# prebuilt_map_directory: "/home/<USER>/catkin_ws/src/CERLAB-UAV-Autonomy/trajectory_planner/map/square_static_map.pcd"
prebuilt_map_directory: "/home/<USER>/catkin_ws/src/CERLAB-UAV-Autonomy/trajectory_planner/map/floorplan1_static_map.pcd"
# prebuilt_map_directory: "/home/<USER>/catkin_ws/src/CERLAB-UAV-Autonomy/trajectory_planner/map/tree_static_map.pcd"

