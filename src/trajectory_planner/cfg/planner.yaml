rrt/map_resolution: 0.1
rrt/timeout: 0.1
rrt/env_box: [-100, 100, -100, 100, 1.0, 1.0]
rrt/incremental_distance: 0.5
rrt/goal_reach_distance: 0.4
rrt/connect_goal_ratio: 0.2
rrt/max_shortcut_dist: 5
rrt/ignore_unknown: true
rrt/pass_goal_check: true

poly_traj/polynomial_degree: 7
poly_traj/differential_degree: 4
poly_traj/continuity_degree: 3
poly_traj/desired_velocity: 1.0 # this is not used. Will be overwritten.
poly_traj/initial_radius: 0.5
poly_traj/timeout: 0.1
poly_traj/corridor_res: 5.0
poly_traj/shrinking_factor: 0.5
poly_traj/soft_constraint: false
poly_traj/constraint_radius: 0.5
poly_traj/sample_delta_time: 0.1
poly_traj/maximum_iteration_num: 10
poly_traj/use_pwl_failsafe: true

bspline_traj/timestep: 0.1
bspline_traj/distance_threshold: 0.5
bspline_traj/max_vel: 1.0 # this is not used. Will be overwritten.
bspline_traj/max_acc: 1.0 # this is not used. Will be overwritten.
bspline_traj/weight_distance: 1.0
bspline_traj/weight_smoothness: 1.0
bspline_traj/weight_feasibility: 1.0
bspline_traj/plan_in_z_axis: false
bspline_traj/min_height: 0.7
bspline_traj/max_height: 1.3
bspline_traj/uncertain_aware_factor: 1.0
bspline_traj/max_path_length: 7
bspline_traj/max_obstacle_size: [10, 10, 10]