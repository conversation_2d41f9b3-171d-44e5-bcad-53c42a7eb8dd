# Robot:
# collision_box: [1.0, 1.0, 0.6]
# collision_box: [0.8, 0.8, 0.4]
collision_box: [0.4, 0.4, 0.2]


# Environment:
env_box: [-100, 100, -100, 100, 1.0, 1.0] # xmin, xmax, ymin, ymax, zmin, zmax


# Planner parameters:
timeout: 0.1 #s
rrt_incremental_distance: 1.0 #m
rrt_connect_goal_ratio: 0.2
goal_reach_distance: 0.4 #m
map_resolution: 0.2 # this should be at least bigger or equal to the true map resolution
ignore_unknown: true
max_shortcut_dist: 5.0


# Planner Visualization:
vis_RRT: False # does not support for RRT*
vis_path: True

# RRT* parameter:
neighborhood_radius: 1.0 #m
max_num_neighbors: 10


# # polynomial trajectory
mode: false # True: adding waypoint, False: corridor constraint 
polynomial_degree: 7
differential_degree: 4 # 3: jerk 4: snap
continuity_degree: 4 # second order continuous
desired_velocity: 1 # m/s
desired_angular_velocity: 0.5 # rad/s
maximum_iteration_num: 100
traj_timeout: 0.1
sample_delta_time: 0.1

# soft waypoint constraint
soft_constraint: false
constraint_size: 0.5 # not in z diection

# corridor constraint (only for mode=false):
initial_radius: 0.5 # m
shrinking_factor: 0.8
corridor_res: 8.0 # how many corridor per second 