##
##	qpOASES -- An Implementation of the Online Active Set Strategy.
##	Copyright (C) 2007-2008 by <PERSON> et al. All rights reserved.
##
##	qpOASES is free software; you can redistribute it and/or
##	modify it under the terms of the GNU Lesser General Public
##	License as published by the Free Software Foundation; either
##	version 2.1 of the License, or (at your option) any later version.
##
##	qpOASES is distributed in the hope that it will be useful,
##	but WITHOUT ANY WARRANTY; without even the implied warranty of
##	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
##	Lesser General Public License for more details.
##
##	You should have received a copy of the GNU Lesser General Public
##	License along with qpOASES; if not, write to the Free Software
##	Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
##



VERSION HISTORY
===============

1.3embedded (last updated on 30th April 2009):
-----------------------------------------------------------------------

+ Re-programming of internal memory management to avoid dynamic memory allocations 
+ Most #ifdef directives removed
+ Almost all type definitions gathered within INCLUDE/Types.hpp
+ Irrelevant functionality removed (like the SQProblem class, functionality 
  for loading data from files or the SCILAB interface)
+ Replacement of all doubles by real_t
+ Introduction of define "PC_DEBUG" for switching off all print functions
+ stdio.h was made optional, string.h is no longer needed
+ relative paths removed from #include directives
+ made auxiliary objects locally static within solveInitialQP()
+ Matlab interface fixed for single precision
+ New return value -2 from Legacy wrapper added to Matlab/Simulink interfaces
+ KKT optimality check moved into QProblem(B) class, SolutionAnalysis class removed


1.3 (released on 2nd June 2008, last updated on 19th June 2008):
-----------------------------------------------------------------------

+ Implementation of "initialised homotopy" concept
+ Addition of the SolutionAnalysis class
+ Utility functions for solving test problems in OQP format added
+ Flexibility of Matlab(R) interface enhanced
+ Major source code cleanup
  (Attention: a few class names and calling interfaces have changed!)



1.2 (released on 9th October 2007):
-----------------------------------------------------------------------

+ Special treatment of diagonal Hessians
+ Improved infeasibility detection
+ Further improved Matlab(R) interface
+ Extended Simulink(R) interface
+ scilab interface added
+ Code cleanup and several bugfixes



1.1 (released on 8th July 2007):
--------------------------------

+ Implementation of the QProblemB class
+ Basic implementation of the SQProblem class
+ Improved Matlab(R) interface
+ Enabling/Disabling of constraints introduced
+ Several bugfixes



1.0 (released on 17th April 2007):
----------------------------------

Initial release.



##
##	end of file
##
