/*
 *    This file was auto-generated using the ACADO Toolkit.
 *    
 *    While ACADO Toolkit is free software released under the terms of
 *    the GNU Lesser General Public License (LGPL), the generated code
 *    as such remains the property of the user who used ACADO Toolkit
 *    to generate this code. In particular, user dependent data of the code
 *    do not inherit the GNU LGPL license. On the other hand, parts of the
 *    generated code that are a direct copy of source code from the
 *    ACADO Toolkit or the software tools it is based on, remain, as derived
 *    work, automatically covered by the LGPL license.
 *    
 *    ACADO Toolkit is distributed in the hope that it will be useful,
 *    but WITHOUT ANY WARRANTY; without even the implied warranty of
 *    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *    
 */


#include "acado_common.h"


void acado_rhs_forw(const real_t* in, real_t* out)
{
const real_t* xd = in;
const real_t* u = in + 112;

/* Compute outputs: */
out[0] = xd[3];
out[1] = xd[4];
out[2] = xd[5];
out[3] = u[0];
out[4] = u[1];
out[5] = u[2];
out[6] = u[3];
out[7] = u[4];
out[8] = xd[32];
out[9] = xd[33];
out[10] = xd[34];
out[11] = xd[35];
out[12] = xd[36];
out[13] = xd[37];
out[14] = xd[38];
out[15] = xd[39];
out[16] = xd[40];
out[17] = xd[41];
out[18] = xd[42];
out[19] = xd[43];
out[20] = xd[44];
out[21] = xd[45];
out[22] = xd[46];
out[23] = xd[47];
out[24] = xd[48];
out[25] = xd[49];
out[26] = xd[50];
out[27] = xd[51];
out[28] = xd[52];
out[29] = xd[53];
out[30] = xd[54];
out[31] = xd[55];
out[32] = (real_t)(0.0000000000000000e+00);
out[33] = (real_t)(0.0000000000000000e+00);
out[34] = (real_t)(0.0000000000000000e+00);
out[35] = (real_t)(0.0000000000000000e+00);
out[36] = (real_t)(0.0000000000000000e+00);
out[37] = (real_t)(0.0000000000000000e+00);
out[38] = (real_t)(0.0000000000000000e+00);
out[39] = (real_t)(0.0000000000000000e+00);
out[40] = (real_t)(0.0000000000000000e+00);
out[41] = (real_t)(0.0000000000000000e+00);
out[42] = (real_t)(0.0000000000000000e+00);
out[43] = (real_t)(0.0000000000000000e+00);
out[44] = (real_t)(0.0000000000000000e+00);
out[45] = (real_t)(0.0000000000000000e+00);
out[46] = (real_t)(0.0000000000000000e+00);
out[47] = (real_t)(0.0000000000000000e+00);
out[48] = (real_t)(0.0000000000000000e+00);
out[49] = (real_t)(0.0000000000000000e+00);
out[50] = (real_t)(0.0000000000000000e+00);
out[51] = (real_t)(0.0000000000000000e+00);
out[52] = (real_t)(0.0000000000000000e+00);
out[53] = (real_t)(0.0000000000000000e+00);
out[54] = (real_t)(0.0000000000000000e+00);
out[55] = (real_t)(0.0000000000000000e+00);
out[56] = (real_t)(0.0000000000000000e+00);
out[57] = (real_t)(0.0000000000000000e+00);
out[58] = (real_t)(0.0000000000000000e+00);
out[59] = (real_t)(0.0000000000000000e+00);
out[60] = (real_t)(0.0000000000000000e+00);
out[61] = (real_t)(0.0000000000000000e+00);
out[62] = (real_t)(0.0000000000000000e+00);
out[63] = (real_t)(0.0000000000000000e+00);
out[64] = (real_t)(0.0000000000000000e+00);
out[65] = (real_t)(0.0000000000000000e+00);
out[66] = (real_t)(0.0000000000000000e+00);
out[67] = (real_t)(0.0000000000000000e+00);
out[68] = (real_t)(0.0000000000000000e+00);
out[69] = (real_t)(0.0000000000000000e+00);
out[70] = (real_t)(0.0000000000000000e+00);
out[71] = (real_t)(0.0000000000000000e+00);
out[72] = xd[87];
out[73] = xd[88];
out[74] = xd[89];
out[75] = xd[90];
out[76] = xd[91];
out[77] = xd[92];
out[78] = xd[93];
out[79] = xd[94];
out[80] = xd[95];
out[81] = xd[96];
out[82] = xd[97];
out[83] = xd[98];
out[84] = xd[99];
out[85] = xd[100];
out[86] = xd[101];
out[87] = (real_t)(1.0000000000000000e+00);
out[88] = (real_t)(0.0000000000000000e+00);
out[89] = (real_t)(0.0000000000000000e+00);
out[90] = (real_t)(0.0000000000000000e+00);
out[91] = (real_t)(0.0000000000000000e+00);
out[92] = (real_t)(0.0000000000000000e+00);
out[93] = (real_t)(1.0000000000000000e+00);
out[94] = (real_t)(0.0000000000000000e+00);
out[95] = (real_t)(0.0000000000000000e+00);
out[96] = (real_t)(0.0000000000000000e+00);
out[97] = (real_t)(0.0000000000000000e+00);
out[98] = (real_t)(0.0000000000000000e+00);
out[99] = (real_t)(1.0000000000000000e+00);
out[100] = (real_t)(0.0000000000000000e+00);
out[101] = (real_t)(0.0000000000000000e+00);
out[102] = (real_t)(0.0000000000000000e+00);
out[103] = (real_t)(0.0000000000000000e+00);
out[104] = (real_t)(0.0000000000000000e+00);
out[105] = (real_t)(1.0000000000000000e+00);
out[106] = (real_t)(0.0000000000000000e+00);
out[107] = (real_t)(0.0000000000000000e+00);
out[108] = (real_t)(0.0000000000000000e+00);
out[109] = (real_t)(0.0000000000000000e+00);
out[110] = (real_t)(0.0000000000000000e+00);
out[111] = (real_t)(1.0000000000000000e+00);
}

/* Fixed step size:0.1 */
int acado_integrate( real_t* const rk_eta, int resetIntegrator )
{
int error;

int lRun2;
int run1;
acadoWorkspace.rk_ttt = 0.0000000000000000e+00;
rk_eta[8] = 1.0000000000000000e+00;
rk_eta[9] = 0.0000000000000000e+00;
rk_eta[10] = 0.0000000000000000e+00;
rk_eta[11] = 0.0000000000000000e+00;
rk_eta[12] = 0.0000000000000000e+00;
rk_eta[13] = 0.0000000000000000e+00;
rk_eta[14] = 0.0000000000000000e+00;
rk_eta[15] = 0.0000000000000000e+00;
rk_eta[16] = 0.0000000000000000e+00;
rk_eta[17] = 1.0000000000000000e+00;
rk_eta[18] = 0.0000000000000000e+00;
rk_eta[19] = 0.0000000000000000e+00;
rk_eta[20] = 0.0000000000000000e+00;
rk_eta[21] = 0.0000000000000000e+00;
rk_eta[22] = 0.0000000000000000e+00;
rk_eta[23] = 0.0000000000000000e+00;
rk_eta[24] = 0.0000000000000000e+00;
rk_eta[25] = 0.0000000000000000e+00;
rk_eta[26] = 1.0000000000000000e+00;
rk_eta[27] = 0.0000000000000000e+00;
rk_eta[28] = 0.0000000000000000e+00;
rk_eta[29] = 0.0000000000000000e+00;
rk_eta[30] = 0.0000000000000000e+00;
rk_eta[31] = 0.0000000000000000e+00;
rk_eta[32] = 0.0000000000000000e+00;
rk_eta[33] = 0.0000000000000000e+00;
rk_eta[34] = 0.0000000000000000e+00;
rk_eta[35] = 1.0000000000000000e+00;
rk_eta[36] = 0.0000000000000000e+00;
rk_eta[37] = 0.0000000000000000e+00;
rk_eta[38] = 0.0000000000000000e+00;
rk_eta[39] = 0.0000000000000000e+00;
rk_eta[40] = 0.0000000000000000e+00;
rk_eta[41] = 0.0000000000000000e+00;
rk_eta[42] = 0.0000000000000000e+00;
rk_eta[43] = 0.0000000000000000e+00;
rk_eta[44] = 1.0000000000000000e+00;
rk_eta[45] = 0.0000000000000000e+00;
rk_eta[46] = 0.0000000000000000e+00;
rk_eta[47] = 0.0000000000000000e+00;
rk_eta[48] = 0.0000000000000000e+00;
rk_eta[49] = 0.0000000000000000e+00;
rk_eta[50] = 0.0000000000000000e+00;
rk_eta[51] = 0.0000000000000000e+00;
rk_eta[52] = 0.0000000000000000e+00;
rk_eta[53] = 1.0000000000000000e+00;
rk_eta[54] = 0.0000000000000000e+00;
rk_eta[55] = 0.0000000000000000e+00;
rk_eta[56] = 0.0000000000000000e+00;
rk_eta[57] = 0.0000000000000000e+00;
rk_eta[58] = 0.0000000000000000e+00;
rk_eta[59] = 0.0000000000000000e+00;
rk_eta[60] = 0.0000000000000000e+00;
rk_eta[61] = 0.0000000000000000e+00;
rk_eta[62] = 1.0000000000000000e+00;
rk_eta[63] = 0.0000000000000000e+00;
rk_eta[64] = 0.0000000000000000e+00;
rk_eta[65] = 0.0000000000000000e+00;
rk_eta[66] = 0.0000000000000000e+00;
rk_eta[67] = 0.0000000000000000e+00;
rk_eta[68] = 0.0000000000000000e+00;
rk_eta[69] = 0.0000000000000000e+00;
rk_eta[70] = 0.0000000000000000e+00;
rk_eta[71] = 1.0000000000000000e+00;
rk_eta[72] = 0.0000000000000000e+00;
rk_eta[73] = 0.0000000000000000e+00;
rk_eta[74] = 0.0000000000000000e+00;
rk_eta[75] = 0.0000000000000000e+00;
rk_eta[76] = 0.0000000000000000e+00;
rk_eta[77] = 0.0000000000000000e+00;
rk_eta[78] = 0.0000000000000000e+00;
rk_eta[79] = 0.0000000000000000e+00;
rk_eta[80] = 0.0000000000000000e+00;
rk_eta[81] = 0.0000000000000000e+00;
rk_eta[82] = 0.0000000000000000e+00;
rk_eta[83] = 0.0000000000000000e+00;
rk_eta[84] = 0.0000000000000000e+00;
rk_eta[85] = 0.0000000000000000e+00;
rk_eta[86] = 0.0000000000000000e+00;
rk_eta[87] = 0.0000000000000000e+00;
rk_eta[88] = 0.0000000000000000e+00;
rk_eta[89] = 0.0000000000000000e+00;
rk_eta[90] = 0.0000000000000000e+00;
rk_eta[91] = 0.0000000000000000e+00;
rk_eta[92] = 0.0000000000000000e+00;
rk_eta[93] = 0.0000000000000000e+00;
rk_eta[94] = 0.0000000000000000e+00;
rk_eta[95] = 0.0000000000000000e+00;
rk_eta[96] = 0.0000000000000000e+00;
rk_eta[97] = 0.0000000000000000e+00;
rk_eta[98] = 0.0000000000000000e+00;
rk_eta[99] = 0.0000000000000000e+00;
rk_eta[100] = 0.0000000000000000e+00;
rk_eta[101] = 0.0000000000000000e+00;
rk_eta[102] = 0.0000000000000000e+00;
rk_eta[103] = 0.0000000000000000e+00;
rk_eta[104] = 0.0000000000000000e+00;
rk_eta[105] = 0.0000000000000000e+00;
rk_eta[106] = 0.0000000000000000e+00;
rk_eta[107] = 0.0000000000000000e+00;
rk_eta[108] = 0.0000000000000000e+00;
rk_eta[109] = 0.0000000000000000e+00;
rk_eta[110] = 0.0000000000000000e+00;
rk_eta[111] = 0.0000000000000000e+00;
for (lRun2 = 0; lRun2 < 186; ++lRun2)
acadoWorkspace.rk_xxx[lRun2 + 112] = rk_eta[lRun2 + 112];


for (run1 = 0; run1 < 1; ++run1)
{
acadoWorkspace.rk_xxx[0] = + rk_eta[0];
acadoWorkspace.rk_xxx[1] = + rk_eta[1];
acadoWorkspace.rk_xxx[2] = + rk_eta[2];
acadoWorkspace.rk_xxx[3] = + rk_eta[3];
acadoWorkspace.rk_xxx[4] = + rk_eta[4];
acadoWorkspace.rk_xxx[5] = + rk_eta[5];
acadoWorkspace.rk_xxx[6] = + rk_eta[6];
acadoWorkspace.rk_xxx[7] = + rk_eta[7];
acadoWorkspace.rk_xxx[8] = + rk_eta[8];
acadoWorkspace.rk_xxx[9] = + rk_eta[9];
acadoWorkspace.rk_xxx[10] = + rk_eta[10];
acadoWorkspace.rk_xxx[11] = + rk_eta[11];
acadoWorkspace.rk_xxx[12] = + rk_eta[12];
acadoWorkspace.rk_xxx[13] = + rk_eta[13];
acadoWorkspace.rk_xxx[14] = + rk_eta[14];
acadoWorkspace.rk_xxx[15] = + rk_eta[15];
acadoWorkspace.rk_xxx[16] = + rk_eta[16];
acadoWorkspace.rk_xxx[17] = + rk_eta[17];
acadoWorkspace.rk_xxx[18] = + rk_eta[18];
acadoWorkspace.rk_xxx[19] = + rk_eta[19];
acadoWorkspace.rk_xxx[20] = + rk_eta[20];
acadoWorkspace.rk_xxx[21] = + rk_eta[21];
acadoWorkspace.rk_xxx[22] = + rk_eta[22];
acadoWorkspace.rk_xxx[23] = + rk_eta[23];
acadoWorkspace.rk_xxx[24] = + rk_eta[24];
acadoWorkspace.rk_xxx[25] = + rk_eta[25];
acadoWorkspace.rk_xxx[26] = + rk_eta[26];
acadoWorkspace.rk_xxx[27] = + rk_eta[27];
acadoWorkspace.rk_xxx[28] = + rk_eta[28];
acadoWorkspace.rk_xxx[29] = + rk_eta[29];
acadoWorkspace.rk_xxx[30] = + rk_eta[30];
acadoWorkspace.rk_xxx[31] = + rk_eta[31];
acadoWorkspace.rk_xxx[32] = + rk_eta[32];
acadoWorkspace.rk_xxx[33] = + rk_eta[33];
acadoWorkspace.rk_xxx[34] = + rk_eta[34];
acadoWorkspace.rk_xxx[35] = + rk_eta[35];
acadoWorkspace.rk_xxx[36] = + rk_eta[36];
acadoWorkspace.rk_xxx[37] = + rk_eta[37];
acadoWorkspace.rk_xxx[38] = + rk_eta[38];
acadoWorkspace.rk_xxx[39] = + rk_eta[39];
acadoWorkspace.rk_xxx[40] = + rk_eta[40];
acadoWorkspace.rk_xxx[41] = + rk_eta[41];
acadoWorkspace.rk_xxx[42] = + rk_eta[42];
acadoWorkspace.rk_xxx[43] = + rk_eta[43];
acadoWorkspace.rk_xxx[44] = + rk_eta[44];
acadoWorkspace.rk_xxx[45] = + rk_eta[45];
acadoWorkspace.rk_xxx[46] = + rk_eta[46];
acadoWorkspace.rk_xxx[47] = + rk_eta[47];
acadoWorkspace.rk_xxx[48] = + rk_eta[48];
acadoWorkspace.rk_xxx[49] = + rk_eta[49];
acadoWorkspace.rk_xxx[50] = + rk_eta[50];
acadoWorkspace.rk_xxx[51] = + rk_eta[51];
acadoWorkspace.rk_xxx[52] = + rk_eta[52];
acadoWorkspace.rk_xxx[53] = + rk_eta[53];
acadoWorkspace.rk_xxx[54] = + rk_eta[54];
acadoWorkspace.rk_xxx[55] = + rk_eta[55];
acadoWorkspace.rk_xxx[56] = + rk_eta[56];
acadoWorkspace.rk_xxx[57] = + rk_eta[57];
acadoWorkspace.rk_xxx[58] = + rk_eta[58];
acadoWorkspace.rk_xxx[59] = + rk_eta[59];
acadoWorkspace.rk_xxx[60] = + rk_eta[60];
acadoWorkspace.rk_xxx[61] = + rk_eta[61];
acadoWorkspace.rk_xxx[62] = + rk_eta[62];
acadoWorkspace.rk_xxx[63] = + rk_eta[63];
acadoWorkspace.rk_xxx[64] = + rk_eta[64];
acadoWorkspace.rk_xxx[65] = + rk_eta[65];
acadoWorkspace.rk_xxx[66] = + rk_eta[66];
acadoWorkspace.rk_xxx[67] = + rk_eta[67];
acadoWorkspace.rk_xxx[68] = + rk_eta[68];
acadoWorkspace.rk_xxx[69] = + rk_eta[69];
acadoWorkspace.rk_xxx[70] = + rk_eta[70];
acadoWorkspace.rk_xxx[71] = + rk_eta[71];
acadoWorkspace.rk_xxx[72] = + rk_eta[72];
acadoWorkspace.rk_xxx[73] = + rk_eta[73];
acadoWorkspace.rk_xxx[74] = + rk_eta[74];
acadoWorkspace.rk_xxx[75] = + rk_eta[75];
acadoWorkspace.rk_xxx[76] = + rk_eta[76];
acadoWorkspace.rk_xxx[77] = + rk_eta[77];
acadoWorkspace.rk_xxx[78] = + rk_eta[78];
acadoWorkspace.rk_xxx[79] = + rk_eta[79];
acadoWorkspace.rk_xxx[80] = + rk_eta[80];
acadoWorkspace.rk_xxx[81] = + rk_eta[81];
acadoWorkspace.rk_xxx[82] = + rk_eta[82];
acadoWorkspace.rk_xxx[83] = + rk_eta[83];
acadoWorkspace.rk_xxx[84] = + rk_eta[84];
acadoWorkspace.rk_xxx[85] = + rk_eta[85];
acadoWorkspace.rk_xxx[86] = + rk_eta[86];
acadoWorkspace.rk_xxx[87] = + rk_eta[87];
acadoWorkspace.rk_xxx[88] = + rk_eta[88];
acadoWorkspace.rk_xxx[89] = + rk_eta[89];
acadoWorkspace.rk_xxx[90] = + rk_eta[90];
acadoWorkspace.rk_xxx[91] = + rk_eta[91];
acadoWorkspace.rk_xxx[92] = + rk_eta[92];
acadoWorkspace.rk_xxx[93] = + rk_eta[93];
acadoWorkspace.rk_xxx[94] = + rk_eta[94];
acadoWorkspace.rk_xxx[95] = + rk_eta[95];
acadoWorkspace.rk_xxx[96] = + rk_eta[96];
acadoWorkspace.rk_xxx[97] = + rk_eta[97];
acadoWorkspace.rk_xxx[98] = + rk_eta[98];
acadoWorkspace.rk_xxx[99] = + rk_eta[99];
acadoWorkspace.rk_xxx[100] = + rk_eta[100];
acadoWorkspace.rk_xxx[101] = + rk_eta[101];
acadoWorkspace.rk_xxx[102] = + rk_eta[102];
acadoWorkspace.rk_xxx[103] = + rk_eta[103];
acadoWorkspace.rk_xxx[104] = + rk_eta[104];
acadoWorkspace.rk_xxx[105] = + rk_eta[105];
acadoWorkspace.rk_xxx[106] = + rk_eta[106];
acadoWorkspace.rk_xxx[107] = + rk_eta[107];
acadoWorkspace.rk_xxx[108] = + rk_eta[108];
acadoWorkspace.rk_xxx[109] = + rk_eta[109];
acadoWorkspace.rk_xxx[110] = + rk_eta[110];
acadoWorkspace.rk_xxx[111] = + rk_eta[111];
acado_rhs_forw( acadoWorkspace.rk_xxx, acadoWorkspace.rk_kkk );
acadoWorkspace.rk_xxx[0] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[0] + rk_eta[0];
acadoWorkspace.rk_xxx[1] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[1] + rk_eta[1];
acadoWorkspace.rk_xxx[2] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[2] + rk_eta[2];
acadoWorkspace.rk_xxx[3] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[3] + rk_eta[3];
acadoWorkspace.rk_xxx[4] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[4] + rk_eta[4];
acadoWorkspace.rk_xxx[5] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[5] + rk_eta[5];
acadoWorkspace.rk_xxx[6] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[6] + rk_eta[6];
acadoWorkspace.rk_xxx[7] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[7] + rk_eta[7];
acadoWorkspace.rk_xxx[8] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[8] + rk_eta[8];
acadoWorkspace.rk_xxx[9] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[9] + rk_eta[9];
acadoWorkspace.rk_xxx[10] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[10] + rk_eta[10];
acadoWorkspace.rk_xxx[11] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[11] + rk_eta[11];
acadoWorkspace.rk_xxx[12] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[12] + rk_eta[12];
acadoWorkspace.rk_xxx[13] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[13] + rk_eta[13];
acadoWorkspace.rk_xxx[14] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[14] + rk_eta[14];
acadoWorkspace.rk_xxx[15] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[15] + rk_eta[15];
acadoWorkspace.rk_xxx[16] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[16] + rk_eta[16];
acadoWorkspace.rk_xxx[17] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[17] + rk_eta[17];
acadoWorkspace.rk_xxx[18] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[18] + rk_eta[18];
acadoWorkspace.rk_xxx[19] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[19] + rk_eta[19];
acadoWorkspace.rk_xxx[20] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[20] + rk_eta[20];
acadoWorkspace.rk_xxx[21] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[21] + rk_eta[21];
acadoWorkspace.rk_xxx[22] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[22] + rk_eta[22];
acadoWorkspace.rk_xxx[23] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[23] + rk_eta[23];
acadoWorkspace.rk_xxx[24] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[24] + rk_eta[24];
acadoWorkspace.rk_xxx[25] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[25] + rk_eta[25];
acadoWorkspace.rk_xxx[26] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[26] + rk_eta[26];
acadoWorkspace.rk_xxx[27] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[27] + rk_eta[27];
acadoWorkspace.rk_xxx[28] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[28] + rk_eta[28];
acadoWorkspace.rk_xxx[29] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[29] + rk_eta[29];
acadoWorkspace.rk_xxx[30] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[30] + rk_eta[30];
acadoWorkspace.rk_xxx[31] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[31] + rk_eta[31];
acadoWorkspace.rk_xxx[32] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[32] + rk_eta[32];
acadoWorkspace.rk_xxx[33] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[33] + rk_eta[33];
acadoWorkspace.rk_xxx[34] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[34] + rk_eta[34];
acadoWorkspace.rk_xxx[35] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[35] + rk_eta[35];
acadoWorkspace.rk_xxx[36] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[36] + rk_eta[36];
acadoWorkspace.rk_xxx[37] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[37] + rk_eta[37];
acadoWorkspace.rk_xxx[38] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[38] + rk_eta[38];
acadoWorkspace.rk_xxx[39] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[39] + rk_eta[39];
acadoWorkspace.rk_xxx[40] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[40] + rk_eta[40];
acadoWorkspace.rk_xxx[41] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[41] + rk_eta[41];
acadoWorkspace.rk_xxx[42] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[42] + rk_eta[42];
acadoWorkspace.rk_xxx[43] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[43] + rk_eta[43];
acadoWorkspace.rk_xxx[44] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[44] + rk_eta[44];
acadoWorkspace.rk_xxx[45] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[45] + rk_eta[45];
acadoWorkspace.rk_xxx[46] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[46] + rk_eta[46];
acadoWorkspace.rk_xxx[47] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[47] + rk_eta[47];
acadoWorkspace.rk_xxx[48] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[48] + rk_eta[48];
acadoWorkspace.rk_xxx[49] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[49] + rk_eta[49];
acadoWorkspace.rk_xxx[50] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[50] + rk_eta[50];
acadoWorkspace.rk_xxx[51] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[51] + rk_eta[51];
acadoWorkspace.rk_xxx[52] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[52] + rk_eta[52];
acadoWorkspace.rk_xxx[53] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[53] + rk_eta[53];
acadoWorkspace.rk_xxx[54] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[54] + rk_eta[54];
acadoWorkspace.rk_xxx[55] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[55] + rk_eta[55];
acadoWorkspace.rk_xxx[56] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[56] + rk_eta[56];
acadoWorkspace.rk_xxx[57] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[57] + rk_eta[57];
acadoWorkspace.rk_xxx[58] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[58] + rk_eta[58];
acadoWorkspace.rk_xxx[59] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[59] + rk_eta[59];
acadoWorkspace.rk_xxx[60] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[60] + rk_eta[60];
acadoWorkspace.rk_xxx[61] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[61] + rk_eta[61];
acadoWorkspace.rk_xxx[62] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[62] + rk_eta[62];
acadoWorkspace.rk_xxx[63] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[63] + rk_eta[63];
acadoWorkspace.rk_xxx[64] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[64] + rk_eta[64];
acadoWorkspace.rk_xxx[65] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[65] + rk_eta[65];
acadoWorkspace.rk_xxx[66] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[66] + rk_eta[66];
acadoWorkspace.rk_xxx[67] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[67] + rk_eta[67];
acadoWorkspace.rk_xxx[68] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[68] + rk_eta[68];
acadoWorkspace.rk_xxx[69] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[69] + rk_eta[69];
acadoWorkspace.rk_xxx[70] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[70] + rk_eta[70];
acadoWorkspace.rk_xxx[71] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[71] + rk_eta[71];
acadoWorkspace.rk_xxx[72] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[72] + rk_eta[72];
acadoWorkspace.rk_xxx[73] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[73] + rk_eta[73];
acadoWorkspace.rk_xxx[74] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[74] + rk_eta[74];
acadoWorkspace.rk_xxx[75] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[75] + rk_eta[75];
acadoWorkspace.rk_xxx[76] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[76] + rk_eta[76];
acadoWorkspace.rk_xxx[77] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[77] + rk_eta[77];
acadoWorkspace.rk_xxx[78] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[78] + rk_eta[78];
acadoWorkspace.rk_xxx[79] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[79] + rk_eta[79];
acadoWorkspace.rk_xxx[80] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[80] + rk_eta[80];
acadoWorkspace.rk_xxx[81] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[81] + rk_eta[81];
acadoWorkspace.rk_xxx[82] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[82] + rk_eta[82];
acadoWorkspace.rk_xxx[83] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[83] + rk_eta[83];
acadoWorkspace.rk_xxx[84] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[84] + rk_eta[84];
acadoWorkspace.rk_xxx[85] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[85] + rk_eta[85];
acadoWorkspace.rk_xxx[86] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[86] + rk_eta[86];
acadoWorkspace.rk_xxx[87] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[87] + rk_eta[87];
acadoWorkspace.rk_xxx[88] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[88] + rk_eta[88];
acadoWorkspace.rk_xxx[89] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[89] + rk_eta[89];
acadoWorkspace.rk_xxx[90] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[90] + rk_eta[90];
acadoWorkspace.rk_xxx[91] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[91] + rk_eta[91];
acadoWorkspace.rk_xxx[92] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[92] + rk_eta[92];
acadoWorkspace.rk_xxx[93] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[93] + rk_eta[93];
acadoWorkspace.rk_xxx[94] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[94] + rk_eta[94];
acadoWorkspace.rk_xxx[95] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[95] + rk_eta[95];
acadoWorkspace.rk_xxx[96] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[96] + rk_eta[96];
acadoWorkspace.rk_xxx[97] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[97] + rk_eta[97];
acadoWorkspace.rk_xxx[98] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[98] + rk_eta[98];
acadoWorkspace.rk_xxx[99] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[99] + rk_eta[99];
acadoWorkspace.rk_xxx[100] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[100] + rk_eta[100];
acadoWorkspace.rk_xxx[101] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[101] + rk_eta[101];
acadoWorkspace.rk_xxx[102] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[102] + rk_eta[102];
acadoWorkspace.rk_xxx[103] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[103] + rk_eta[103];
acadoWorkspace.rk_xxx[104] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[104] + rk_eta[104];
acadoWorkspace.rk_xxx[105] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[105] + rk_eta[105];
acadoWorkspace.rk_xxx[106] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[106] + rk_eta[106];
acadoWorkspace.rk_xxx[107] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[107] + rk_eta[107];
acadoWorkspace.rk_xxx[108] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[108] + rk_eta[108];
acadoWorkspace.rk_xxx[109] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[109] + rk_eta[109];
acadoWorkspace.rk_xxx[110] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[110] + rk_eta[110];
acadoWorkspace.rk_xxx[111] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[111] + rk_eta[111];
acado_rhs_forw( acadoWorkspace.rk_xxx, &(acadoWorkspace.rk_kkk[ 112 ]) );
acadoWorkspace.rk_xxx[0] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[112] + rk_eta[0];
acadoWorkspace.rk_xxx[1] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[113] + rk_eta[1];
acadoWorkspace.rk_xxx[2] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[114] + rk_eta[2];
acadoWorkspace.rk_xxx[3] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[115] + rk_eta[3];
acadoWorkspace.rk_xxx[4] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[116] + rk_eta[4];
acadoWorkspace.rk_xxx[5] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[117] + rk_eta[5];
acadoWorkspace.rk_xxx[6] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[118] + rk_eta[6];
acadoWorkspace.rk_xxx[7] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[119] + rk_eta[7];
acadoWorkspace.rk_xxx[8] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[120] + rk_eta[8];
acadoWorkspace.rk_xxx[9] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[121] + rk_eta[9];
acadoWorkspace.rk_xxx[10] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[122] + rk_eta[10];
acadoWorkspace.rk_xxx[11] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[123] + rk_eta[11];
acadoWorkspace.rk_xxx[12] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[124] + rk_eta[12];
acadoWorkspace.rk_xxx[13] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[125] + rk_eta[13];
acadoWorkspace.rk_xxx[14] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[126] + rk_eta[14];
acadoWorkspace.rk_xxx[15] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[127] + rk_eta[15];
acadoWorkspace.rk_xxx[16] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[128] + rk_eta[16];
acadoWorkspace.rk_xxx[17] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[129] + rk_eta[17];
acadoWorkspace.rk_xxx[18] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[130] + rk_eta[18];
acadoWorkspace.rk_xxx[19] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[131] + rk_eta[19];
acadoWorkspace.rk_xxx[20] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[132] + rk_eta[20];
acadoWorkspace.rk_xxx[21] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[133] + rk_eta[21];
acadoWorkspace.rk_xxx[22] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[134] + rk_eta[22];
acadoWorkspace.rk_xxx[23] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[135] + rk_eta[23];
acadoWorkspace.rk_xxx[24] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[136] + rk_eta[24];
acadoWorkspace.rk_xxx[25] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[137] + rk_eta[25];
acadoWorkspace.rk_xxx[26] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[138] + rk_eta[26];
acadoWorkspace.rk_xxx[27] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[139] + rk_eta[27];
acadoWorkspace.rk_xxx[28] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[140] + rk_eta[28];
acadoWorkspace.rk_xxx[29] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[141] + rk_eta[29];
acadoWorkspace.rk_xxx[30] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[142] + rk_eta[30];
acadoWorkspace.rk_xxx[31] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[143] + rk_eta[31];
acadoWorkspace.rk_xxx[32] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[144] + rk_eta[32];
acadoWorkspace.rk_xxx[33] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[145] + rk_eta[33];
acadoWorkspace.rk_xxx[34] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[146] + rk_eta[34];
acadoWorkspace.rk_xxx[35] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[147] + rk_eta[35];
acadoWorkspace.rk_xxx[36] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[148] + rk_eta[36];
acadoWorkspace.rk_xxx[37] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[149] + rk_eta[37];
acadoWorkspace.rk_xxx[38] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[150] + rk_eta[38];
acadoWorkspace.rk_xxx[39] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[151] + rk_eta[39];
acadoWorkspace.rk_xxx[40] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[152] + rk_eta[40];
acadoWorkspace.rk_xxx[41] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[153] + rk_eta[41];
acadoWorkspace.rk_xxx[42] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[154] + rk_eta[42];
acadoWorkspace.rk_xxx[43] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[155] + rk_eta[43];
acadoWorkspace.rk_xxx[44] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[156] + rk_eta[44];
acadoWorkspace.rk_xxx[45] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[157] + rk_eta[45];
acadoWorkspace.rk_xxx[46] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[158] + rk_eta[46];
acadoWorkspace.rk_xxx[47] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[159] + rk_eta[47];
acadoWorkspace.rk_xxx[48] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[160] + rk_eta[48];
acadoWorkspace.rk_xxx[49] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[161] + rk_eta[49];
acadoWorkspace.rk_xxx[50] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[162] + rk_eta[50];
acadoWorkspace.rk_xxx[51] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[163] + rk_eta[51];
acadoWorkspace.rk_xxx[52] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[164] + rk_eta[52];
acadoWorkspace.rk_xxx[53] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[165] + rk_eta[53];
acadoWorkspace.rk_xxx[54] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[166] + rk_eta[54];
acadoWorkspace.rk_xxx[55] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[167] + rk_eta[55];
acadoWorkspace.rk_xxx[56] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[168] + rk_eta[56];
acadoWorkspace.rk_xxx[57] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[169] + rk_eta[57];
acadoWorkspace.rk_xxx[58] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[170] + rk_eta[58];
acadoWorkspace.rk_xxx[59] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[171] + rk_eta[59];
acadoWorkspace.rk_xxx[60] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[172] + rk_eta[60];
acadoWorkspace.rk_xxx[61] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[173] + rk_eta[61];
acadoWorkspace.rk_xxx[62] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[174] + rk_eta[62];
acadoWorkspace.rk_xxx[63] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[175] + rk_eta[63];
acadoWorkspace.rk_xxx[64] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[176] + rk_eta[64];
acadoWorkspace.rk_xxx[65] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[177] + rk_eta[65];
acadoWorkspace.rk_xxx[66] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[178] + rk_eta[66];
acadoWorkspace.rk_xxx[67] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[179] + rk_eta[67];
acadoWorkspace.rk_xxx[68] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[180] + rk_eta[68];
acadoWorkspace.rk_xxx[69] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[181] + rk_eta[69];
acadoWorkspace.rk_xxx[70] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[182] + rk_eta[70];
acadoWorkspace.rk_xxx[71] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[183] + rk_eta[71];
acadoWorkspace.rk_xxx[72] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[184] + rk_eta[72];
acadoWorkspace.rk_xxx[73] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[185] + rk_eta[73];
acadoWorkspace.rk_xxx[74] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[186] + rk_eta[74];
acadoWorkspace.rk_xxx[75] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[187] + rk_eta[75];
acadoWorkspace.rk_xxx[76] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[188] + rk_eta[76];
acadoWorkspace.rk_xxx[77] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[189] + rk_eta[77];
acadoWorkspace.rk_xxx[78] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[190] + rk_eta[78];
acadoWorkspace.rk_xxx[79] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[191] + rk_eta[79];
acadoWorkspace.rk_xxx[80] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[192] + rk_eta[80];
acadoWorkspace.rk_xxx[81] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[193] + rk_eta[81];
acadoWorkspace.rk_xxx[82] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[194] + rk_eta[82];
acadoWorkspace.rk_xxx[83] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[195] + rk_eta[83];
acadoWorkspace.rk_xxx[84] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[196] + rk_eta[84];
acadoWorkspace.rk_xxx[85] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[197] + rk_eta[85];
acadoWorkspace.rk_xxx[86] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[198] + rk_eta[86];
acadoWorkspace.rk_xxx[87] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[199] + rk_eta[87];
acadoWorkspace.rk_xxx[88] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[200] + rk_eta[88];
acadoWorkspace.rk_xxx[89] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[201] + rk_eta[89];
acadoWorkspace.rk_xxx[90] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[202] + rk_eta[90];
acadoWorkspace.rk_xxx[91] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[203] + rk_eta[91];
acadoWorkspace.rk_xxx[92] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[204] + rk_eta[92];
acadoWorkspace.rk_xxx[93] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[205] + rk_eta[93];
acadoWorkspace.rk_xxx[94] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[206] + rk_eta[94];
acadoWorkspace.rk_xxx[95] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[207] + rk_eta[95];
acadoWorkspace.rk_xxx[96] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[208] + rk_eta[96];
acadoWorkspace.rk_xxx[97] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[209] + rk_eta[97];
acadoWorkspace.rk_xxx[98] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[210] + rk_eta[98];
acadoWorkspace.rk_xxx[99] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[211] + rk_eta[99];
acadoWorkspace.rk_xxx[100] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[212] + rk_eta[100];
acadoWorkspace.rk_xxx[101] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[213] + rk_eta[101];
acadoWorkspace.rk_xxx[102] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[214] + rk_eta[102];
acadoWorkspace.rk_xxx[103] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[215] + rk_eta[103];
acadoWorkspace.rk_xxx[104] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[216] + rk_eta[104];
acadoWorkspace.rk_xxx[105] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[217] + rk_eta[105];
acadoWorkspace.rk_xxx[106] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[218] + rk_eta[106];
acadoWorkspace.rk_xxx[107] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[219] + rk_eta[107];
acadoWorkspace.rk_xxx[108] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[220] + rk_eta[108];
acadoWorkspace.rk_xxx[109] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[221] + rk_eta[109];
acadoWorkspace.rk_xxx[110] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[222] + rk_eta[110];
acadoWorkspace.rk_xxx[111] = + (real_t)5.0000000000000003e-02*acadoWorkspace.rk_kkk[223] + rk_eta[111];
acado_rhs_forw( acadoWorkspace.rk_xxx, &(acadoWorkspace.rk_kkk[ 224 ]) );
acadoWorkspace.rk_xxx[0] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[224] + rk_eta[0];
acadoWorkspace.rk_xxx[1] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[225] + rk_eta[1];
acadoWorkspace.rk_xxx[2] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[226] + rk_eta[2];
acadoWorkspace.rk_xxx[3] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[227] + rk_eta[3];
acadoWorkspace.rk_xxx[4] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[228] + rk_eta[4];
acadoWorkspace.rk_xxx[5] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[229] + rk_eta[5];
acadoWorkspace.rk_xxx[6] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[230] + rk_eta[6];
acadoWorkspace.rk_xxx[7] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[231] + rk_eta[7];
acadoWorkspace.rk_xxx[8] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[232] + rk_eta[8];
acadoWorkspace.rk_xxx[9] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[233] + rk_eta[9];
acadoWorkspace.rk_xxx[10] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[234] + rk_eta[10];
acadoWorkspace.rk_xxx[11] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[235] + rk_eta[11];
acadoWorkspace.rk_xxx[12] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[236] + rk_eta[12];
acadoWorkspace.rk_xxx[13] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[237] + rk_eta[13];
acadoWorkspace.rk_xxx[14] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[238] + rk_eta[14];
acadoWorkspace.rk_xxx[15] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[239] + rk_eta[15];
acadoWorkspace.rk_xxx[16] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[240] + rk_eta[16];
acadoWorkspace.rk_xxx[17] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[241] + rk_eta[17];
acadoWorkspace.rk_xxx[18] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[242] + rk_eta[18];
acadoWorkspace.rk_xxx[19] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[243] + rk_eta[19];
acadoWorkspace.rk_xxx[20] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[244] + rk_eta[20];
acadoWorkspace.rk_xxx[21] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[245] + rk_eta[21];
acadoWorkspace.rk_xxx[22] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[246] + rk_eta[22];
acadoWorkspace.rk_xxx[23] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[247] + rk_eta[23];
acadoWorkspace.rk_xxx[24] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[248] + rk_eta[24];
acadoWorkspace.rk_xxx[25] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[249] + rk_eta[25];
acadoWorkspace.rk_xxx[26] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[250] + rk_eta[26];
acadoWorkspace.rk_xxx[27] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[251] + rk_eta[27];
acadoWorkspace.rk_xxx[28] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[252] + rk_eta[28];
acadoWorkspace.rk_xxx[29] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[253] + rk_eta[29];
acadoWorkspace.rk_xxx[30] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[254] + rk_eta[30];
acadoWorkspace.rk_xxx[31] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[255] + rk_eta[31];
acadoWorkspace.rk_xxx[32] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[256] + rk_eta[32];
acadoWorkspace.rk_xxx[33] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[257] + rk_eta[33];
acadoWorkspace.rk_xxx[34] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[258] + rk_eta[34];
acadoWorkspace.rk_xxx[35] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[259] + rk_eta[35];
acadoWorkspace.rk_xxx[36] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[260] + rk_eta[36];
acadoWorkspace.rk_xxx[37] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[261] + rk_eta[37];
acadoWorkspace.rk_xxx[38] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[262] + rk_eta[38];
acadoWorkspace.rk_xxx[39] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[263] + rk_eta[39];
acadoWorkspace.rk_xxx[40] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[264] + rk_eta[40];
acadoWorkspace.rk_xxx[41] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[265] + rk_eta[41];
acadoWorkspace.rk_xxx[42] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[266] + rk_eta[42];
acadoWorkspace.rk_xxx[43] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[267] + rk_eta[43];
acadoWorkspace.rk_xxx[44] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[268] + rk_eta[44];
acadoWorkspace.rk_xxx[45] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[269] + rk_eta[45];
acadoWorkspace.rk_xxx[46] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[270] + rk_eta[46];
acadoWorkspace.rk_xxx[47] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[271] + rk_eta[47];
acadoWorkspace.rk_xxx[48] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[272] + rk_eta[48];
acadoWorkspace.rk_xxx[49] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[273] + rk_eta[49];
acadoWorkspace.rk_xxx[50] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[274] + rk_eta[50];
acadoWorkspace.rk_xxx[51] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[275] + rk_eta[51];
acadoWorkspace.rk_xxx[52] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[276] + rk_eta[52];
acadoWorkspace.rk_xxx[53] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[277] + rk_eta[53];
acadoWorkspace.rk_xxx[54] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[278] + rk_eta[54];
acadoWorkspace.rk_xxx[55] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[279] + rk_eta[55];
acadoWorkspace.rk_xxx[56] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[280] + rk_eta[56];
acadoWorkspace.rk_xxx[57] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[281] + rk_eta[57];
acadoWorkspace.rk_xxx[58] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[282] + rk_eta[58];
acadoWorkspace.rk_xxx[59] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[283] + rk_eta[59];
acadoWorkspace.rk_xxx[60] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[284] + rk_eta[60];
acadoWorkspace.rk_xxx[61] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[285] + rk_eta[61];
acadoWorkspace.rk_xxx[62] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[286] + rk_eta[62];
acadoWorkspace.rk_xxx[63] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[287] + rk_eta[63];
acadoWorkspace.rk_xxx[64] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[288] + rk_eta[64];
acadoWorkspace.rk_xxx[65] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[289] + rk_eta[65];
acadoWorkspace.rk_xxx[66] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[290] + rk_eta[66];
acadoWorkspace.rk_xxx[67] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[291] + rk_eta[67];
acadoWorkspace.rk_xxx[68] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[292] + rk_eta[68];
acadoWorkspace.rk_xxx[69] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[293] + rk_eta[69];
acadoWorkspace.rk_xxx[70] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[294] + rk_eta[70];
acadoWorkspace.rk_xxx[71] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[295] + rk_eta[71];
acadoWorkspace.rk_xxx[72] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[296] + rk_eta[72];
acadoWorkspace.rk_xxx[73] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[297] + rk_eta[73];
acadoWorkspace.rk_xxx[74] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[298] + rk_eta[74];
acadoWorkspace.rk_xxx[75] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[299] + rk_eta[75];
acadoWorkspace.rk_xxx[76] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[300] + rk_eta[76];
acadoWorkspace.rk_xxx[77] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[301] + rk_eta[77];
acadoWorkspace.rk_xxx[78] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[302] + rk_eta[78];
acadoWorkspace.rk_xxx[79] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[303] + rk_eta[79];
acadoWorkspace.rk_xxx[80] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[304] + rk_eta[80];
acadoWorkspace.rk_xxx[81] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[305] + rk_eta[81];
acadoWorkspace.rk_xxx[82] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[306] + rk_eta[82];
acadoWorkspace.rk_xxx[83] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[307] + rk_eta[83];
acadoWorkspace.rk_xxx[84] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[308] + rk_eta[84];
acadoWorkspace.rk_xxx[85] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[309] + rk_eta[85];
acadoWorkspace.rk_xxx[86] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[310] + rk_eta[86];
acadoWorkspace.rk_xxx[87] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[311] + rk_eta[87];
acadoWorkspace.rk_xxx[88] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[312] + rk_eta[88];
acadoWorkspace.rk_xxx[89] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[313] + rk_eta[89];
acadoWorkspace.rk_xxx[90] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[314] + rk_eta[90];
acadoWorkspace.rk_xxx[91] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[315] + rk_eta[91];
acadoWorkspace.rk_xxx[92] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[316] + rk_eta[92];
acadoWorkspace.rk_xxx[93] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[317] + rk_eta[93];
acadoWorkspace.rk_xxx[94] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[318] + rk_eta[94];
acadoWorkspace.rk_xxx[95] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[319] + rk_eta[95];
acadoWorkspace.rk_xxx[96] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[320] + rk_eta[96];
acadoWorkspace.rk_xxx[97] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[321] + rk_eta[97];
acadoWorkspace.rk_xxx[98] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[322] + rk_eta[98];
acadoWorkspace.rk_xxx[99] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[323] + rk_eta[99];
acadoWorkspace.rk_xxx[100] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[324] + rk_eta[100];
acadoWorkspace.rk_xxx[101] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[325] + rk_eta[101];
acadoWorkspace.rk_xxx[102] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[326] + rk_eta[102];
acadoWorkspace.rk_xxx[103] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[327] + rk_eta[103];
acadoWorkspace.rk_xxx[104] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[328] + rk_eta[104];
acadoWorkspace.rk_xxx[105] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[329] + rk_eta[105];
acadoWorkspace.rk_xxx[106] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[330] + rk_eta[106];
acadoWorkspace.rk_xxx[107] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[331] + rk_eta[107];
acadoWorkspace.rk_xxx[108] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[332] + rk_eta[108];
acadoWorkspace.rk_xxx[109] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[333] + rk_eta[109];
acadoWorkspace.rk_xxx[110] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[334] + rk_eta[110];
acadoWorkspace.rk_xxx[111] = + (real_t)1.0000000000000001e-01*acadoWorkspace.rk_kkk[335] + rk_eta[111];
acado_rhs_forw( acadoWorkspace.rk_xxx, &(acadoWorkspace.rk_kkk[ 336 ]) );
rk_eta[0] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[0] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[112] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[224] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[336];
rk_eta[1] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[1] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[113] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[225] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[337];
rk_eta[2] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[2] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[114] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[226] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[338];
rk_eta[3] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[3] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[115] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[227] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[339];
rk_eta[4] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[4] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[116] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[228] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[340];
rk_eta[5] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[5] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[117] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[229] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[341];
rk_eta[6] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[6] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[118] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[230] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[342];
rk_eta[7] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[7] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[119] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[231] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[343];
rk_eta[8] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[8] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[120] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[232] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[344];
rk_eta[9] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[9] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[121] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[233] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[345];
rk_eta[10] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[10] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[122] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[234] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[346];
rk_eta[11] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[11] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[123] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[235] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[347];
rk_eta[12] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[12] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[124] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[236] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[348];
rk_eta[13] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[13] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[125] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[237] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[349];
rk_eta[14] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[14] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[126] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[238] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[350];
rk_eta[15] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[15] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[127] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[239] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[351];
rk_eta[16] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[16] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[128] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[240] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[352];
rk_eta[17] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[17] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[129] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[241] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[353];
rk_eta[18] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[18] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[130] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[242] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[354];
rk_eta[19] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[19] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[131] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[243] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[355];
rk_eta[20] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[20] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[132] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[244] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[356];
rk_eta[21] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[21] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[133] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[245] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[357];
rk_eta[22] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[22] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[134] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[246] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[358];
rk_eta[23] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[23] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[135] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[247] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[359];
rk_eta[24] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[24] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[136] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[248] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[360];
rk_eta[25] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[25] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[137] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[249] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[361];
rk_eta[26] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[26] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[138] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[250] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[362];
rk_eta[27] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[27] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[139] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[251] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[363];
rk_eta[28] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[28] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[140] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[252] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[364];
rk_eta[29] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[29] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[141] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[253] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[365];
rk_eta[30] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[30] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[142] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[254] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[366];
rk_eta[31] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[31] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[143] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[255] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[367];
rk_eta[32] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[32] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[144] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[256] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[368];
rk_eta[33] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[33] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[145] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[257] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[369];
rk_eta[34] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[34] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[146] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[258] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[370];
rk_eta[35] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[35] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[147] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[259] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[371];
rk_eta[36] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[36] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[148] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[260] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[372];
rk_eta[37] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[37] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[149] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[261] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[373];
rk_eta[38] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[38] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[150] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[262] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[374];
rk_eta[39] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[39] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[151] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[263] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[375];
rk_eta[40] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[40] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[152] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[264] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[376];
rk_eta[41] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[41] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[153] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[265] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[377];
rk_eta[42] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[42] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[154] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[266] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[378];
rk_eta[43] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[43] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[155] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[267] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[379];
rk_eta[44] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[44] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[156] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[268] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[380];
rk_eta[45] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[45] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[157] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[269] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[381];
rk_eta[46] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[46] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[158] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[270] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[382];
rk_eta[47] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[47] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[159] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[271] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[383];
rk_eta[48] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[48] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[160] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[272] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[384];
rk_eta[49] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[49] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[161] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[273] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[385];
rk_eta[50] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[50] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[162] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[274] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[386];
rk_eta[51] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[51] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[163] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[275] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[387];
rk_eta[52] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[52] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[164] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[276] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[388];
rk_eta[53] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[53] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[165] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[277] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[389];
rk_eta[54] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[54] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[166] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[278] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[390];
rk_eta[55] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[55] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[167] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[279] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[391];
rk_eta[56] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[56] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[168] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[280] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[392];
rk_eta[57] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[57] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[169] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[281] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[393];
rk_eta[58] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[58] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[170] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[282] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[394];
rk_eta[59] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[59] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[171] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[283] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[395];
rk_eta[60] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[60] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[172] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[284] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[396];
rk_eta[61] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[61] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[173] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[285] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[397];
rk_eta[62] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[62] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[174] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[286] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[398];
rk_eta[63] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[63] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[175] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[287] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[399];
rk_eta[64] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[64] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[176] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[288] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[400];
rk_eta[65] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[65] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[177] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[289] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[401];
rk_eta[66] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[66] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[178] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[290] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[402];
rk_eta[67] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[67] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[179] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[291] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[403];
rk_eta[68] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[68] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[180] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[292] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[404];
rk_eta[69] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[69] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[181] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[293] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[405];
rk_eta[70] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[70] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[182] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[294] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[406];
rk_eta[71] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[71] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[183] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[295] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[407];
rk_eta[72] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[72] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[184] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[296] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[408];
rk_eta[73] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[73] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[185] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[297] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[409];
rk_eta[74] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[74] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[186] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[298] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[410];
rk_eta[75] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[75] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[187] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[299] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[411];
rk_eta[76] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[76] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[188] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[300] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[412];
rk_eta[77] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[77] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[189] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[301] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[413];
rk_eta[78] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[78] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[190] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[302] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[414];
rk_eta[79] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[79] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[191] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[303] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[415];
rk_eta[80] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[80] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[192] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[304] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[416];
rk_eta[81] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[81] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[193] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[305] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[417];
rk_eta[82] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[82] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[194] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[306] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[418];
rk_eta[83] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[83] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[195] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[307] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[419];
rk_eta[84] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[84] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[196] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[308] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[420];
rk_eta[85] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[85] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[197] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[309] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[421];
rk_eta[86] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[86] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[198] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[310] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[422];
rk_eta[87] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[87] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[199] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[311] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[423];
rk_eta[88] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[88] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[200] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[312] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[424];
rk_eta[89] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[89] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[201] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[313] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[425];
rk_eta[90] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[90] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[202] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[314] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[426];
rk_eta[91] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[91] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[203] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[315] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[427];
rk_eta[92] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[92] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[204] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[316] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[428];
rk_eta[93] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[93] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[205] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[317] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[429];
rk_eta[94] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[94] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[206] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[318] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[430];
rk_eta[95] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[95] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[207] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[319] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[431];
rk_eta[96] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[96] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[208] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[320] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[432];
rk_eta[97] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[97] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[209] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[321] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[433];
rk_eta[98] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[98] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[210] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[322] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[434];
rk_eta[99] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[99] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[211] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[323] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[435];
rk_eta[100] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[100] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[212] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[324] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[436];
rk_eta[101] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[101] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[213] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[325] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[437];
rk_eta[102] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[102] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[214] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[326] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[438];
rk_eta[103] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[103] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[215] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[327] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[439];
rk_eta[104] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[104] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[216] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[328] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[440];
rk_eta[105] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[105] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[217] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[329] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[441];
rk_eta[106] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[106] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[218] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[330] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[442];
rk_eta[107] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[107] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[219] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[331] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[443];
rk_eta[108] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[108] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[220] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[332] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[444];
rk_eta[109] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[109] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[221] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[333] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[445];
rk_eta[110] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[110] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[222] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[334] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[446];
rk_eta[111] += + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[111] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[223] + (real_t)3.3333333333333333e-02*acadoWorkspace.rk_kkk[335] + (real_t)1.6666666666666666e-02*acadoWorkspace.rk_kkk[447];
acadoWorkspace.rk_ttt += 1.0000000000000000e+00;
}
error = 0;
return error;
}

