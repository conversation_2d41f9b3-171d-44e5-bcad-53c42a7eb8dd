This code is modified from the original StoGO Global Optimization library
by <PERSON><PERSON> et al., downloaded from:

	http://www2.imm.dtu.dk/~km/GlobOpt/opt.html

It was modified to allow C-callable wrappers and some other niceties by
<PERSON> (<EMAIL>) in 2007.

StoGO uses a gradient-based direct-search branch-and-bound algorithm,
described in:

<PERSON><PERSON>, "Parallel Global Optimization," <PERSON><PERSON>Sc. Thesis, IMM,
	Technical University of Denmark, 1998.

<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, "Global Optimization
	using Branch-and-Bound," Submitted to the Journal of Global
	Optimization, 1998.
	[ never published, but preprint is included as paper.pdf ]

<PERSON><PERSON> and <PERSON><PERSON>, "A C++ Programme for Global Optimization,"
	IMM-REP-1998-04, Department of Mathematical Modelling,
	Technical University of Denmark, DK-2800 Lyngby, Denmark, 1998.
	[ included as techreport.pdf ]
