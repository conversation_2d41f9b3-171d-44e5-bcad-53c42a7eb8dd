***********************************************************************
*                                                                     *
*         PLIP - A LIMITED MEMORY VARIABLE METRIC ALGORITHM FOR       *
*                LARGE-SCALE OPTIMIZATION.                            *
*                                                                     *
***********************************************************************


1. Introduction:
----------------

      The double-precision FORTRAN 77 basic subroutine PLIP is designed
to find a close approximation to a local minimum of a nonlinear
function F(X) with simple bounds on variables. Here X is a vector of NF
variables and F(X) is a smooth function. We suppose that NF is large
but the sparsity pattern of the Hessian matrix is not known (or the
Hessian matrix is dense). Simple bounds are assumed in the form

               X(I) unbounded if  IX(I) = 0,
      XL(I) <= X(I)           if  IX(I) = 1,
               X(I) <= XU(I)  if  IX(I) = 2,
      XL(I) <= X(I) <= XU(I)  if  IX(I) = 3,
      XL(I)  = X(I)  = XU(I)  if  IX(I) = 5,

where 1 <= I <= NF. To simplify user's work, two additional easy to use
subroutines are added. They call the basic general subroutine PLIP:

      PLIPU - unconstrained large-scale optimization,
      PLIPS - large-scale optimization with simple bounds.

All subroutines contain a description of formal parameters and
extensive comments. Furthermore, two test programs TLIPU and TLIPS are
included, which contain several test problems (see e.g. [2]). These
test programs serve as examples for using the subroutines, verify their
correctness and demonstrate their efficiency.
      In this short guide, we describe all subroutines which can be
called from the user's program. A detailed description of the method is
given in [1]. In the description of formal parameters, we introduce a
type of the argument that specifies whether the argument must have a
value defined on entry to the subroutine (I), whether it is a value
which will be returned (O), or both (U), or whether it is an auxiliary
value (A). Note that the arguments of the type I can be changed on
output under some circumstances, especially if improper input values
were given. Besides formal parameters, we can use a COMMON /STAT/ block
containing statistical information. This block, used in each subroutine
has the following form:

      COMMON /STAT/ NRES,NDEC,NIN,NIT,NFV,NFG,NFH

The arguments have the following meaning:

 Argument  Type Significance
 ----------------------------------------------------------------------
  NRES      O   Positive INTEGER variable that indicates the number of
                restarts.
  NDEC      O   Positive INTEGER variable that indicates the number of
                matrix decompositions.
  NIN       O   Positive INTEGER variable that indicates the number of
                inner iterations (for solving linear systems).
  NIT       O   Positive INTEGER variable that indicates the number of
                iterations.
  NFV       O   Positive INTEGER variable that indicates the number of
                function evaluations.
  NFG       O   Positive INTEGER variable that specifies the number of
                gradient evaluations.
  NFH       O   Positive INTEGER variable that specifies the number of
                Hessian evaluations.


2. Subroutines PLIPU, PLIPS:
----------------------------

The calling sequences are

      CALL PLIPU(NF,X,IPAR,RPAR,F,GMAX,IPRNT,ITERM)
      CALL PLIPS(NF,X,IX,XL,XU,IPAR,RPAR,F,GMAX,IPRNT,ITERM)

The arguments have the following meaning.

 Argument  Type Significance
 ----------------------------------------------------------------------
  NF        I   Positive INTEGER variable that specifies the number of
                variables of the objective function.
  X(NF)     U   On input, DOUBLE PRECISION vector with the initial
                estimate to the solution. On output, the approximation
                to the minimum.
  IX(NF)    I   On input (significant only if NB>0) INTEGER vector
                containing the simple bounds types:
                   IX(I)=0 - the variable X(I) is unbounded,
                   IX(I)=1 - the lower bound X(I) >= XL(I),
                   IX(I)=2 - the upper bound X(I) <= XU(I),
                   IX(I)=3 - the two side bound XL(I) <= X(I) <= XU(I),
                   IX(I)=5 - the variable X(I) is fixed (given by its
                             initial estimate).
  XL(NF)    I   DOUBLE PRECISION vector with lower bounds for variables
                (significant only if NB>0).
  XU(NF)    I   DOUBLE PRECISION vector with upper bounds for variables
                (significant only if NB>0).
  IPAR(7)   I   INTEGER parameters:
                  IPAR(1)=MIT,  IPAR(2)=MFV, IPAR(3)-NONE,
                  IPAR(4)=IEST, IPAR(5)-MET, IPAR(6)-NONE,
                  IPAR(7)=MF.
                Parameters MIT, MFV, IEST, MET, MF are described
                in Section 3 together with other parameters of the
                subroutine PLIP.
  RPAR(9)   I   DOUBLE PRECISION parameters:
                  RPAR(1)=XMAX,  RPAR(2)=TOLX,  RPAR(3)=TOLF,
                  RPAR(4)=TOLB,  RPAR(5)=TOLG,  RPAR(6)=FMIN,
                  RPAR(7)-NONE,  RPAR(6)-NONE,  RPAR(9)-NONE.
                Parameters XMAX, TOLX, TOLF, TOLB, TOLG, FMIN are
                described in Section 3 together with other parameters
                of the subroutine PLIP.
  F         O   DOUBLE PRECISION value of the objective function at the
                solution X.
  GMAX      O   DOUBLE PRECISION maximum absolute value of a partial
                derivative of the Lagrangian function.
  IPRNT     I   INTEGER variable that specifies PRINT:
                  IPRNT= 0 - print is suppressed,
                  IPRNT= 1 - basic print of final results,
                  IPRNT=-1 - extended print of final results,
                  IPRNT= 2 - basic print of intermediate and final
                             results,
                  IPRNT=-2 - extended print of intermediate and final
                             results.
  ITERM     O   INTEGER variable that indicates the cause of termination:
                  ITERM= 1 - if |X - XO| was less than or equal to TOLX
                             in two subsequent iterations,
                  ITERM= 2 - if |F - FO| was less than or equal to TOLF
                             in two subsequent iterations,
                  ITERM= 3 - if F is less than or equal to TOLB,
                  ITERM= 4 - if GMAX is less than or equal to TOLG,
                  ITERM= 6 - if termination criterion was not satisfied,
                             but the solution is probably acceptable,
                  ITERM=11 - if NIT exceeded MIT,
                  ITERM=12 - if NFV exceeded MFV,
                  ITERM< 0 - if the method failed.

      The subroutines PLIPU, PLIPS require the user supplied subroutines
OBJ and DOBJ that define the objective function and its gradient and
have the form

      SUBROUTINE  OBJ(NF,X,F)
      SUBROUTINE DOBJ(NF,X,G)

The arguments of the user supplied subroutines have the following
meaning.

 Argument  Type Significance
 ----------------------------------------------------------------------
  NF        I   Positive INTEGER variable that specifies the number of
                variables of the objective function.
  X(NF)     I   DOUBLE PRECISION an estimate to the solution.
  F         O   DOUBLE PRECISION value of the objective function at the
                point X.
  G(NF)     O   DOUBLE PRECISION gradient of the objective function
                at the point X.


3. Subroutine PLIP:
-------------------

      This general subroutine is called from all subroutines described
in Section 2. The calling sequence is

      CALL PLIP(NF,NB,X,IX,XL,XU,GF,S,XO,GO,SO,XM,XR,GR,XMAX,TOLX,TOLF,
     & TOLB,TOLG,FMIN,GMAX,F,MIT,MFV,IEST,MET,MF,IPRNT,ITERM)

The arguments NF, NB, X, IX, XL, XU, GMAX, F, IPRNT, ITERM, have the
same meaning as in Section 2. Other arguments have the following meaning:

 Argument  Type Significance
 ----------------------------------------------------------------------
  GF(NF)    A   DOUBLE PRECISION gradient of the objective function.
  S(NF)     A   DOUBLE PRECISION direction vector.
  XO(NF)    A   DOUBLE PRECISION array which contains increments of
                variables.
  GO(NF)    A   DOUBLE PRECISION array which contains increments of
                gradients.
  SO(NF)    A   DOUBLE PRECISION auxiliary array.
  XM(NF*MF) A   DOUBLE PRECISION array which contains columns
                of the updated matrix stored in the product form.
  XR(MF)    A   DOUBLE PRECISION array which contains reduced
                increments of variables.
  GR(MF)    A   DOUBLE PRECISION array which contains reduced
                increments of gradients.
  XMAX      I   DOUBLE PRECISION maximum stepsize; the choice XMAX=0
                causes that the default value 1.0D+16 will be taken.
  TOLX      I   DOUBLE PRECISION tolerance for the change of the
                coordinate vector X; the choice TOLX=0 causes that the
                default value TOLX=1.0D-16 will be taken.
  TOLF      I   DOUBLE PRECISION tolerance for the change of function
                values; the choice TOLF=0 causes that the default
                value TOLF=1.0D-14 will be taken.
  TOLB      I   DOUBLE PRECISION minimum acceptable function value;
                the choice TOLB=0 causes that the default value
                TOLB=FMIN+1.0D-16 will be taken.
  TOLG      I   DOUBLE PRECISION tolerance for the Lagrangian function
                gradient; the choice TOLG=0 causes that the default
                value TOLG=1.0D-6 will be taken.
  FMIN      I   DOUBLE PRECISION lower bound for the minimum function
                value.
  MIT       I   INTEGER variable that specifies the maximum number of
                iterations; the choice MIT=0 causes that the default
                value 9000 will be taken.
  MFV       I   INTEGER variable that specifies the maximum number of
                function evaluations; the choice MFV=0 causes that
                the default value 9000 will be taken.
  IEST      I   INTEGER estimation of the minimum functiom value for
                the line search:
                  IEST=0 - estimation is not used,
                  IEST=1 - lower bound FMIN is used as an estimation
                           for the minimum function value.
  MET       I   INTEGER variable that specifies the limited-memory
                method:
                  MET=1 - rank-one method,
                  MET=2 - rank-two method.
                The choice MET=0 causes that the default value MET=2
                will be taken.
  MF        I   The number of limited-memory variable metric updates
                in each iteration (they use MF stored vectors).

The choice of parameter XMAX can be sensitive in many cases. First, the
objective function can be evaluated only in a relatively small region
(if it contains exponentials) so that the maximum stepsize is necessary.
Secondly, the problem can be very ill-conditioned far from the solution
point so that large steps can be unsuitable. Finally, if the problem has
more local solutions, a suitably chosen maximum stepsize can lead to
obtaining a better local solution.
      The subroutine PLIP requires the user supplied subroutines OBJ
and DOBJ which are described in Section 2.

4. Verification of the subroutines:
-----------------------------------

      Subroutine PLIPU can be verified and tested using the program
TLIPU. This program calls the subroutines TIUD14 (initiation), TFFU14
(function evaluation) and TFGU14 (gradient evaluation) containing
22 unconstrained test problems with at most 1000 variables [2]. The
results obtained by the program TLIPU on a PC computer with Microsoft
Power Station Fortran compiler have the following form.

NIT= 5383  NFV= 5417  NFG= 5417  F= 0.601022658E-13  G= 0.599E-06  ITERM=  4
NIT=  530  NFV=  557  NFG=  557  F=  3.57276719      G= 0.124E-05  ITERM=  2
NIT=  125  NFV=  128  NFG=  128  F= 0.338270284E-12  G= 0.518E-06  ITERM=  4
NIT=  109  NFV=  114  NFG=  114  F=  269.499543      G= 0.669E-06  ITERM=  4
NIT=   26  NFV=   27  NFG=   27  F= 0.710072396E-11  G= 0.951E-06  ITERM=  4
NIT=   35  NFV=   36  NFG=   36  F= 0.142942272E-10  G= 0.737E-06  ITERM=  4
NIT=   36  NFV=   41  NFG=   41  F=  336.937181      G= 0.956E-06  ITERM=  4
NIT=   33  NFV=   36  NFG=   36  F=  761774.954      G= 0.192E-02  ITERM=  2
NIT=   15  NFV=   18  NFG=   18  F=  316.436141      G= 0.264E-06  ITERM=  4
NIT= 2003  NFV= 2030  NFG= 2030  F= -124.950000      G= 0.116E-04  ITERM=  2
NIT=  157  NFV=  175  NFG=  175  F=  10.7765879      G= 0.299E-06  ITERM=  4
NIT=  337  NFV=  350  NFG=  350  F=  982.273617      G= 0.145E-04  ITERM=  2
NIT=    9  NFV=   10  NFG=   10  F= 0.230414406E-14  G= 0.642E-07  ITERM=  4
NIT=    8  NFV=   10  NFG=   10  F= 0.128834241E-08  G= 0.977E-06  ITERM=  4
NIT= 1226  NFV= 1256  NFG= 1256  F=  1.92401599      G= 0.970E-06  ITERM=  4
NIT=  237  NFV=  246  NFG=  246  F= -427.404476      G= 0.501E-04  ITERM=  2
NIT=  598  NFV=  604  NFG=  604  F=-0.379921091E-01  G= 0.908E-06  ITERM=  4
NIT=  989  NFV=  998  NFG=  998  F=-0.245741193E-01  G= 0.975E-06  ITERM=  4
NIT= 1261  NFV= 1272  NFG= 1272  F=  59.5986241      G= 0.410E-05  ITERM=  2
NIT= 2045  NFV= 2058  NFG= 2058  F= -1.00013520      G= 0.911E-06  ITERM=  4
NIT= 2175  NFV= 2196  NFG= 2196  F=  2.13866377      G= 0.996E-06  ITERM=  4
NIT= 1261  NFV= 1292  NFG= 1292  F=  1.00000000      G= 0.927E-06  ITERM=  4
NITER =18598    NFVAL =18871    NSUCC =   22
TIME= 0:00:10.63

The rows corresponding to individual test problems contain the number of
iterations NIT, the number of function evaluations NFV, the number of
gradient evaluations NFG, the final value of the objective function F,
the norm of gradient G and the cause of termination ITERM.
      Subroutine PLIPS can be verified and tested using the program
TLIPS. This program calls the subroutines TIUD14 (initiation), TFFU14
(function evaluation), TFGU14 (gradient evaluation) containing 22 box
constrained test problems with at most 1000 variables [2]. The results
obtained by the program TLIPS on a PC computer with Microsoft Power
Station Fortran compiler have the following form.

NIT= 5263  NFV= 5321  NFG= 5321  F= 0.530131995E-13  G= 0.370E-05  ITERM=  2
NIT= 2293  NFV= 2447  NFG= 2447  F=  3930.43962      G= 0.251E-04  ITERM=  2
NIT=  127  NFV=  132  NFG=  132  F= 0.210550150E-12  G= 0.437E-06  ITERM=  4
NIT=   70  NFV=   72  NFG=   72  F=  269.522686      G= 0.794E-06  ITERM=  4
NIT=   26  NFV=   27  NFG=   27  F= 0.710072396E-11  G= 0.951E-06  ITERM=  4
NIT=   35  NFV=   36  NFG=   36  F= 0.142942272E-10  G= 0.737E-06  ITERM=  4
NIT=   37  NFV=   43  NFG=   43  F=  336.937181      G= 0.133E-05  ITERM=  2
NIT=   59  NFV=   65  NFG=   65  F=  761925.725      G= 0.399E-03  ITERM=  2
NIT=  508  NFV=  510  NFG=  510  F=  428.056916      G= 0.776E-06  ITERM=  4
NIT= 1253  NFV= 1277  NFG= 1277  F= -82.5400568      G= 0.120E-04  ITERM=  2
NIT=   13  NFV=   19  NFG=   19  F=  96517.2947      G= 0.150E-04  ITERM=  2
NIT=   95  NFV=  102  NFG=  102  F=  4994.21410      G= 0.790E-04  ITERM=  2
NIT=    9  NFV=   10  NFG=   10  F= 0.230414406E-14  G= 0.642E-07  ITERM=  4
NIT=    8  NFV=   10  NFG=   10  F= 0.128834241E-08  G= 0.977E-06  ITERM=  4
NIT= 1226  NFV= 1256  NFG= 1256  F=  1.92401599      G= 0.970E-06  ITERM=  4
NIT=  227  NFV=  228  NFG=  228  F= -427.391653      G= 0.952E-05  ITERM=  2
NIT=  598  NFV=  604  NFG=  604  F=-0.379921091E-01  G= 0.908E-06  ITERM=  4
NIT=  989  NFV=  998  NFG=  998  F=-0.245741193E-01  G= 0.975E-06  ITERM=  4
NIT= 1367  NFV= 1383  NFG= 1383  F=  1654.94525      G= 0.105E-04  ITERM=  2
NIT= 2274  NFV= 2303  NFG= 2303  F= -1.00013520      G= 0.798E-06  ITERM=  4
NIT= 1196  NFV= 1211  NFG= 1211  F=  2.41354873      G= 0.975E-06  ITERM=  4
NIT= 1361  NFV= 1381  NFG= 1381  F=  1.00000000      G= 0.962E-06  ITERM=  4
NITER =19034    NFVAL =19435    NSUCC =   22
TIME= 0:00:11.09

References:
-----------

[1] Luksan L., Matonoha C., Vlcek J.: LSA: Algorithms for large-scale
    unconstrained and box constrained optimization Technical Report V-896.
    Prague, ICS AS CR, 2004.

[2] Luksan L., Vlcek J.: Sparse and partially separable test problems
    for unconstrained and equality constrained optimization. Research
    Report V-767, Institute of Computer Science, Academy of Sciences
    of the Czech Republic, Prague, Czech Republic, 1998.
