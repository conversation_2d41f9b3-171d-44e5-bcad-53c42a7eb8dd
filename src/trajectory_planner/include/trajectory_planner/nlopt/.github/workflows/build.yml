name: Build
on: [push, pull_request]
jobs:
  linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Build
        run: |
          sudo apt-get update -y && sudo apt-get install python3-dev python3-numpy guile-3.0-dev octave-dev cmake gfortran g++-mingw-w64-x86-64 gfortran-mingw-w64-x86-64
          pip install mkdocs python-markdown-math --user
          PATH=$PATH:~/.local/bin mkdocs build
          mkdir build && pushd build
          cmake -DCMAKE_INSTALL_PREFIX=~/.local -DNLOPT_FORTRAN=ON -DCMAKE_C_FLAGS='-std=c89 -pedantic -D_POSIX_C_SOURCE=200809L -Wall -Wextra -Wshadow' -DCMAKE_CXX_FLAGS='-Wall -Wextra -pedantic -Wshadow' -DSWIG_COMPILE_FLAGS="-Wno-shadow -Wno-unused-parameter -Wno-missing-field-initializers" -DNLOPT_TESTS=ON ..
          make install -j4 && ctest -j4 --output-on-failure
          rm -rf * ~/.local
          cmake -DCMAKE_INSTALL_PREFIX=~/.local -DNLOPT_PYTHON=OFF -DNLOPT_OCTAVE=OFF -DNLOPT_GUILE=OFF -DNLOPT_FORTRAN=ON -DCMAKE_TOOLCHAIN_FILE=$PWD/../cmake/toolchain-x86_64-w64-mingw32.cmake -DNLOPT_TESTS=ON ..
          make install -j4

  macos:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - name: Build
        run: |
          brew install swig guile
          mkdir build && pushd build
          cmake -DCMAKE_INSTALL_PREFIX=~/.local -DNLOPT_FORTRAN=OFF -DNLOPT_TESTS=ON ..
          make install -j3 && ctest --output-on-failure

  windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Build
        shell: cmd
        run: |
          pip install numpy
          cmake -DCMAKE_INSTALL_PREFIX=%cd:\=/%/install -DNLOPT_TESTS=ON .
          cmake --build . --config Release --target install
          xcopy %cd%\install\bin\nlopt.dll %cd%\src\swig\python
          xcopy %cd%\install\Lib\site-packages\_nlopt*.pyd %cd%\src\swig\python
          xcopy %cd%\install\bin\nlopt.dll %cd%\src\swig\Release
          dir /p %cd%\src\swig
          ctest -C Release --output-on-failure --timeout 100
