The system is: Linux - 5.15.0-138-generic - x86_64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /usr/bin/cc 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"

The C compiler identification is GNU, found in "/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/3.16.3/CompilerIdC/a.out"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/c++ 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"

The CXX compiler identification is GNU, found in "/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/3.16.3/CompilerIdCXX/a.out"

Determining if the C compiler works passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_61ecc/fast && /usr/bin/make -f CMakeFiles/cmTC_61ecc.dir/build.make CMakeFiles/cmTC_61ecc.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_61ecc.dir/testCCompiler.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_61ecc.dir/testCCompiler.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/testCCompiler.c
Linking C executable cmTC_61ecc
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_61ecc.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_61ecc.dir/testCCompiler.c.o  -o cmTC_61ecc 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Detecting C compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_f24c5/fast && /usr/bin/make -f CMakeFiles/cmTC_f24c5.dir/build.make CMakeFiles/cmTC_f24c5.dir/build
make[1]: Entering directory '/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o
/usr/bin/cc   -v -o CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.16/Modules/CMakeCCompilerABI.c
Using built-in specs.
COLLECT_GCC=/usr/bin/cc
OFFLOAD_TARGET_NAMES=nvptx-none:hsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) 
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/9/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/share/cmake-3.16/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cc1itTPJ.s
GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)
	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"
ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"
#include "..." search starts here:
#include <...> search starts here:
 /usr/lib/gcc/x86_64-linux-gnu/9/include
 /usr/local/include
 /usr/include/x86_64-linux-gnu
 /usr/include
End of search list.
GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)
	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
Compiler executable checksum: d50f9baaf86b1ab1ea2b5e982c779df7
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
 as -v --64 -o CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o /tmp/cc1itTPJ.s
GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
Linking C executable cmTC_f24c5
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f24c5.dir/link.txt --verbose=1
/usr/bin/cc     -v CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o  -o cmTC_f24c5 
Using built-in specs.
COLLECT_GCC=/usr/bin/cc
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:hsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) 
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_f24c5' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccjDXvle.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_f24c5 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_f24c5' '-mtune=generic' '-march=x86-64'
make[1]: Leaving directory '/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp'



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/usr/lib/gcc/x86_64-linux-gnu/9/include]
    add: [/usr/local/include]
    add: [/usr/include/x86_64-linux-gnu]
    add: [/usr/include]
  end of search list found
  collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/9/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/9/include]
  collapse include dir [/usr/local/include] ==> [/usr/local/include]
  collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
  collapse include dir [/usr/include] ==> [/usr/include]
  implicit include dirs: [/usr/lib/gcc/x86_64-linux-gnu/9/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/make cmTC_f24c5/fast && /usr/bin/make -f CMakeFiles/cmTC_f24c5.dir/build.make CMakeFiles/cmTC_f24c5.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o]
  ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o   -c /usr/share/cmake-3.16/Modules/CMakeCCompilerABI.c]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/cc]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
  ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/share/cmake-3.16/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cc1itTPJ.s]
  ignore line: [GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)]
  ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
  ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"]
  ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/include]
  ignore line: [ /usr/local/include]
  ignore line: [ /usr/include/x86_64-linux-gnu]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)]
  ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: d50f9baaf86b1ab1ea2b5e982c779df7]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
  ignore line: [ as -v --64 -o CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o /tmp/cc1itTPJ.s]
  ignore line: [GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
  ignore line: [Linking C executable cmTC_f24c5]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f24c5.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/cc     -v CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o  -o cmTC_f24c5 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/cc]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_f24c5' '-mtune=generic' '-march=x86-64']
  link line: [ /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccjDXvle.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_f24c5 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccjDXvle.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-pie] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_f24c5] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o] ==> ignore
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..]
    arg [CMakeFiles/cmTC_f24c5.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> ignore
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/usr/lib/gcc/x86_64-linux-gnu/9]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> [/usr/lib]
  implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
  implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/9;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
  implicit fwks: []


Determining if the CXX compiler works passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_bc167/fast && /usr/bin/make -f CMakeFiles/cmTC_bc167.dir/build.make CMakeFiles/cmTC_bc167.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building CXX object CMakeFiles/cmTC_bc167.dir/testCXXCompiler.cxx.o
/usr/bin/c++     -o CMakeFiles/cmTC_bc167.dir/testCXXCompiler.cxx.o -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/testCXXCompiler.cxx
Linking CXX executable cmTC_bc167
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bc167.dir/link.txt --verbose=1
/usr/bin/c++       CMakeFiles/cmTC_bc167.dir/testCXXCompiler.cxx.o  -o cmTC_bc167 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_d30cf/fast && /usr/bin/make -f CMakeFiles/cmTC_d30cf.dir/build.make CMakeFiles/cmTC_d30cf.dir/build
make[1]: Entering directory '/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o
/usr/bin/c++    -v -o CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp
Using built-in specs.
COLLECT_GCC=/usr/bin/c++
OFFLOAD_TARGET_NAMES=nvptx-none:hsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) 
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/9/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccE7jooH.s
GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)
	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/9"
ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"
ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"
#include "..." search starts here:
#include <...> search starts here:
 /usr/include/c++/9
 /usr/include/x86_64-linux-gnu/c++/9
 /usr/include/c++/9/backward
 /usr/lib/gcc/x86_64-linux-gnu/9/include
 /usr/local/include
 /usr/include/x86_64-linux-gnu
 /usr/include
End of search list.
GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)
	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP

GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
Compiler executable checksum: 83d7bb95fd72194d0a05edf92f8757fb
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 as -v --64 -o CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccE7jooH.s
GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
Linking CXX executable cmTC_d30cf
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d30cf.dir/link.txt --verbose=1
/usr/bin/c++      -v CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_d30cf 
Using built-in specs.
COLLECT_GCC=/usr/bin/c++
COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:hsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
Thread model: posix
gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) 
COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d30cf' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
 /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/cc2W3Dad.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_d30cf /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d30cf' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
make[1]: Leaving directory '/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp'



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/usr/include/c++/9]
    add: [/usr/include/x86_64-linux-gnu/c++/9]
    add: [/usr/include/c++/9/backward]
    add: [/usr/lib/gcc/x86_64-linux-gnu/9/include]
    add: [/usr/local/include]
    add: [/usr/include/x86_64-linux-gnu]
    add: [/usr/include]
  end of search list found
  collapse include dir [/usr/include/c++/9] ==> [/usr/include/c++/9]
  collapse include dir [/usr/include/x86_64-linux-gnu/c++/9] ==> [/usr/include/x86_64-linux-gnu/c++/9]
  collapse include dir [/usr/include/c++/9/backward] ==> [/usr/include/c++/9/backward]
  collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/9/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/9/include]
  collapse include dir [/usr/local/include] ==> [/usr/local/include]
  collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
  collapse include dir [/usr/include] ==> [/usr/include]
  implicit include dirs: [/usr/include/c++/9;/usr/include/x86_64-linux-gnu/c++/9;/usr/include/c++/9/backward;/usr/lib/gcc/x86_64-linux-gnu/9/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/make cmTC_d30cf/fast && /usr/bin/make -f CMakeFiles/cmTC_d30cf.dir/build.make CMakeFiles/cmTC_d30cf.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/usr/bin/c++    -v -o CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/c++]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccE7jooH.s]
  ignore line: [GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)]
  ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/9"]
  ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
  ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"]
  ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /usr/include/c++/9]
  ignore line: [ /usr/include/x86_64-linux-gnu/c++/9]
  ignore line: [ /usr/include/c++/9/backward]
  ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/include]
  ignore line: [ /usr/local/include]
  ignore line: [ /usr/include/x86_64-linux-gnu]
  ignore line: [ /usr/include]
  ignore line: [End of search list.]
  ignore line: [GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.3) version 9.4.0 (x86_64-linux-gnu)]
  ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
  ignore line: []
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: 83d7bb95fd72194d0a05edf92f8757fb]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  ignore line: [ as -v --64 -o CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccE7jooH.s]
  ignore line: [GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  ignore line: [Linking CXX executable cmTC_d30cf]
  ignore line: [/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_d30cf.dir/link.txt --verbose=1]
  ignore line: [/usr/bin/c++      -v CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o  -o cmTC_d30cf ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/usr/bin/c++]
  ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper]
  ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
  ignore line: [OFFLOAD_TARGET_DEFAULT=1]
  ignore line: [Target: x86_64-linux-gnu]
  ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.3' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-05ho5U/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.3) ]
  ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
  ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d30cf' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
  link line: [ /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/cc2W3Dad.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_d30cf /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/cc2W3Dad.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [--build-id] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_x86_64] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
    arg [-pie] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_d30cf] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o] ==> ignore
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib]
    arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
    arg [-L/lib/../lib] ==> dir [/lib/../lib]
    arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
    arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
    arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..]
    arg [CMakeFiles/cmTC_d30cf.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o] ==> ignore
    arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> ignore
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/usr/lib/gcc/x86_64-linux-gnu/9]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> [/usr/lib]
  collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
  collapse library dir [/lib/../lib] ==> [/lib]
  collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
  collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
  collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> [/usr/lib]
  implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
  implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/9;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
  implicit fwks: []


Determining if the include file getopt.h exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_76684/fast && /usr/bin/make -f CMakeFiles/cmTC_76684.dir/build.make CMakeFiles/cmTC_76684.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_76684.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_76684.dir/CheckIncludeFile.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_76684
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_76684.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_76684.dir/CheckIncludeFile.c.o  -o cmTC_76684 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the include file unistd.h exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_f4ec2/fast && /usr/bin/make -f CMakeFiles/cmTC_f4ec2.dir/build.make CMakeFiles/cmTC_f4ec2.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_f4ec2.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_f4ec2.dir/CheckIncludeFile.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_f4ec2
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f4ec2.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_f4ec2.dir/CheckIncludeFile.c.o  -o cmTC_f4ec2 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the include file stdint.h exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_ea8be/fast && /usr/bin/make -f CMakeFiles/cmTC_ea8be.dir/build.make CMakeFiles/cmTC_ea8be.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_ea8be.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_ea8be.dir/CheckIncludeFile.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_ea8be
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ea8be.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_ea8be.dir/CheckIncludeFile.c.o  -o cmTC_ea8be 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the include file time.h exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_e701a/fast && /usr/bin/make -f CMakeFiles/cmTC_e701a.dir/build.make CMakeFiles/cmTC_e701a.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_e701a.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_e701a.dir/CheckIncludeFile.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_e701a
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e701a.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_e701a.dir/CheckIncludeFile.c.o  -o cmTC_e701a 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the include file sys/time.h exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_f0941/fast && /usr/bin/make -f CMakeFiles/cmTC_f0941.dir/build.make CMakeFiles/cmTC_f0941.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_f0941.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_f0941.dir/CheckIncludeFile.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_f0941
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f0941.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_f0941.dir/CheckIncludeFile.c.o  -o cmTC_f0941 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function getpid exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_452dd/fast && /usr/bin/make -f CMakeFiles/cmTC_452dd.dir/build.make CMakeFiles/cmTC_452dd.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_452dd.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getpid   -o CMakeFiles/cmTC_452dd.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_452dd
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_452dd.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getpid    CMakeFiles/cmTC_452dd.dir/CheckFunctionExists.c.o  -o cmTC_452dd 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function syscall exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_767ff/fast && /usr/bin/make -f CMakeFiles/cmTC_767ff.dir/build.make CMakeFiles/cmTC_767ff.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_767ff.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=syscall   -o CMakeFiles/cmTC_767ff.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_767ff
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_767ff.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=syscall    CMakeFiles/cmTC_767ff.dir/CheckFunctionExists.c.o  -o cmTC_767ff 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function isinf exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_8e818/fast && /usr/bin/make -f CMakeFiles/cmTC_8e818.dir/build.make CMakeFiles/cmTC_8e818.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_8e818.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=isinf   -o CMakeFiles/cmTC_8e818.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
<command-line>: warning: conflicting types for built-in function ‘isinf’; expected ‘int()’ [-Wbuiltin-declaration-mismatch]
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:7:3: note: in expansion of macro ‘CHECK_FUNCTION_EXISTS’
    7 |   CHECK_FUNCTION_EXISTS(void);
      |   ^~~~~~~~~~~~~~~~~~~~~
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:1:1: note: ‘isinf’ is declared in header ‘<math.h>’
  +++ |+#include <math.h>
    1 | #ifdef CHECK_FUNCTION_EXISTS
Linking C executable cmTC_8e818
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_8e818.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=isinf    CMakeFiles/cmTC_8e818.dir/CheckFunctionExists.c.o  -o cmTC_8e818 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function isnan exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_04a5d/fast && /usr/bin/make -f CMakeFiles/cmTC_04a5d.dir/build.make CMakeFiles/cmTC_04a5d.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_04a5d.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=isnan   -o CMakeFiles/cmTC_04a5d.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
<command-line>: warning: conflicting types for built-in function ‘isnan’; expected ‘int()’ [-Wbuiltin-declaration-mismatch]
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:7:3: note: in expansion of macro ‘CHECK_FUNCTION_EXISTS’
    7 |   CHECK_FUNCTION_EXISTS(void);
      |   ^~~~~~~~~~~~~~~~~~~~~
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:1:1: note: ‘isnan’ is declared in header ‘<math.h>’
  +++ |+#include <math.h>
    1 | #ifdef CHECK_FUNCTION_EXISTS
Linking C executable cmTC_04a5d
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_04a5d.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=isnan    CMakeFiles/cmTC_04a5d.dir/CheckFunctionExists.c.o  -o cmTC_04a5d 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function gettimeofday exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_bbf4e/fast && /usr/bin/make -f CMakeFiles/cmTC_bbf4e.dir/build.make CMakeFiles/cmTC_bbf4e.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_bbf4e.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=gettimeofday   -o CMakeFiles/cmTC_bbf4e.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_bbf4e
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_bbf4e.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=gettimeofday    CMakeFiles/cmTC_bbf4e.dir/CheckFunctionExists.c.o  -o cmTC_bbf4e 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function qsort_r exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_08999/fast && /usr/bin/make -f CMakeFiles/cmTC_08999.dir/build.make CMakeFiles/cmTC_08999.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_08999.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=qsort_r   -o CMakeFiles/cmTC_08999.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_08999
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_08999.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=qsort_r    CMakeFiles/cmTC_08999.dir/CheckFunctionExists.c.o  -o cmTC_08999 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function time exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_abe4b/fast && /usr/bin/make -f CMakeFiles/cmTC_abe4b.dir/build.make CMakeFiles/cmTC_abe4b.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_abe4b.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=time   -o CMakeFiles/cmTC_abe4b.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_abe4b
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_abe4b.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=time    CMakeFiles/cmTC_abe4b.dir/CheckFunctionExists.c.o  -o cmTC_abe4b 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function copysign exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_666cf/fast && /usr/bin/make -f CMakeFiles/cmTC_666cf.dir/build.make CMakeFiles/cmTC_666cf.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_666cf.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=copysign   -o CMakeFiles/cmTC_666cf.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
<command-line>: warning: conflicting types for built-in function ‘copysign’; expected ‘double(double,  double)’ [-Wbuiltin-declaration-mismatch]
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:7:3: note: in expansion of macro ‘CHECK_FUNCTION_EXISTS’
    7 |   CHECK_FUNCTION_EXISTS(void);
      |   ^~~~~~~~~~~~~~~~~~~~~
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:1:1: note: ‘copysign’ is declared in header ‘<math.h>’
  +++ |+#include <math.h>
    1 | #ifdef CHECK_FUNCTION_EXISTS
Linking C executable cmTC_666cf
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_666cf.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=copysign    CMakeFiles/cmTC_666cf.dir/CheckFunctionExists.c.o  -o cmTC_666cf 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function getopt exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_98cc8/fast && /usr/bin/make -f CMakeFiles/cmTC_98cc8.dir/build.make CMakeFiles/cmTC_98cc8.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_98cc8.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=getopt   -o CMakeFiles/cmTC_98cc8.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_98cc8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_98cc8.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=getopt    CMakeFiles/cmTC_98cc8.dir/CheckFunctionExists.c.o  -o cmTC_98cc8 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the include file sys/types.h exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_856f4/fast && /usr/bin/make -f CMakeFiles/cmTC_856f4.dir/build.make CMakeFiles/cmTC_856f4.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_856f4.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_856f4.dir/CheckIncludeFile.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_856f4
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_856f4.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_856f4.dir/CheckIncludeFile.c.o  -o cmTC_856f4 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the include file stddef.h exists passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_c2cbc/fast && /usr/bin/make -f CMakeFiles/cmTC_c2cbc.dir/build.make CMakeFiles/cmTC_c2cbc.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_c2cbc.dir/CheckIncludeFile.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_c2cbc.dir/CheckIncludeFile.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckIncludeFile.c
Linking C executable cmTC_c2cbc
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c2cbc.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_c2cbc.dir/CheckIncludeFile.c.o  -o cmTC_c2cbc 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining size of uint32_t passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_75ffb/fast && /usr/bin/make -f CMakeFiles/cmTC_75ffb.dir/build.make CMakeFiles/cmTC_75ffb.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_75ffb.dir/SIZEOF_UINT32_T.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_75ffb.dir/SIZEOF_UINT32_T.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CheckTypeSize/SIZEOF_UINT32_T.c
Linking C executable cmTC_75ffb
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_75ffb.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_75ffb.dir/SIZEOF_UINT32_T.c.o  -o cmTC_75ffb 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining size of unsigned int passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_75f4e/fast && /usr/bin/make -f CMakeFiles/cmTC_75f4e.dir/build.make CMakeFiles/cmTC_75f4e.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_75f4e.dir/SIZEOF_UNSIGNED_INT.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_75f4e.dir/SIZEOF_UNSIGNED_INT.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CheckTypeSize/SIZEOF_UNSIGNED_INT.c
Linking C executable cmTC_75f4e
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_75f4e.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_75f4e.dir/SIZEOF_UNSIGNED_INT.c.o  -o cmTC_75f4e 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining size of unsigned long passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_81c2c/fast && /usr/bin/make -f CMakeFiles/cmTC_81c2c.dir/build.make CMakeFiles/cmTC_81c2c.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_81c2c.dir/SIZEOF_UNSIGNED_LONG.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_81c2c.dir/SIZEOF_UNSIGNED_LONG.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CheckTypeSize/SIZEOF_UNSIGNED_LONG.c
Linking C executable cmTC_81c2c
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_81c2c.dir/link.txt --verbose=1
/usr/bin/cc      CMakeFiles/cmTC_81c2c.dir/SIZEOF_UNSIGNED_LONG.c.o  -o cmTC_81c2c 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Determining if the function sqrt exists in the m passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_3bbb8/fast && /usr/bin/make -f CMakeFiles/cmTC_3bbb8.dir/build.make CMakeFiles/cmTC_3bbb8.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_3bbb8.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=sqrt   -o CMakeFiles/cmTC_3bbb8.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
<command-line>: warning: conflicting types for built-in function ‘sqrt’; expected ‘double(double)’ [-Wbuiltin-declaration-mismatch]
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:7:3: note: in expansion of macro ‘CHECK_FUNCTION_EXISTS’
    7 |   CHECK_FUNCTION_EXISTS(void);
      |   ^~~~~~~~~~~~~~~~~~~~~
/usr/share/cmake-3.16/Modules/CheckFunctionExists.c:1:1: note: ‘sqrt’ is declared in header ‘<math.h>’
  +++ |+#include <math.h>
    1 | #ifdef CHECK_FUNCTION_EXISTS
Linking C executable cmTC_3bbb8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_3bbb8.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=sqrt    CMakeFiles/cmTC_3bbb8.dir/CheckFunctionExists.c.o  -o cmTC_3bbb8  -lm 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”



Performing C SOURCE FILE Test HAVE_THREAD_LOCAL_STORAGE succeeded with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_dc1e8/fast && /usr/bin/make -f CMakeFiles/cmTC_dc1e8.dir/build.make CMakeFiles/cmTC_dc1e8.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_dc1e8.dir/src.c.o
/usr/bin/cc   -DHAVE_THREAD_LOCAL_STORAGE   -o CMakeFiles/cmTC_dc1e8.dir/src.c.o   -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_dc1e8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_dc1e8.dir/link.txt --verbose=1
/usr/bin/cc  -DHAVE_THREAD_LOCAL_STORAGE    CMakeFiles/cmTC_dc1e8.dir/src.c.o  -o cmTC_dc1e8 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”


Source file was:

    __thread int tls;

    int main(void) {
        return 0;
    }
Determining if the __cplusplus exist passed with the following output:
Change Dir: /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_676e7/fast && /usr/bin/make -f CMakeFiles/cmTC_676e7.dir/build.make CMakeFiles/cmTC_676e7.dir/build
make[1]: 进入目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”
Building CXX object CMakeFiles/cmTC_676e7.dir/CheckSymbolExists.cxx.o
/usr/bin/c++     -o CMakeFiles/cmTC_676e7.dir/CheckSymbolExists.cxx.o -c /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckSymbolExists.cxx
Linking CXX executable cmTC_676e7
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_676e7.dir/link.txt --verbose=1
/usr/bin/c++       CMakeFiles/cmTC_676e7.dir/CheckSymbolExists.cxx.o  -o cmTC_676e7 
make[1]: 离开目录“/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp”


File /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/CMakeTmp/CheckSymbolExists.cxx:
/* */
#include <ciso646>

int main(int argc, char** argv)
{
  (void)argv;
#ifndef __cplusplus
  return ((int*)(&__cplusplus))[argc];
#else
  (void)argc;
  return 0;
#endif
}
