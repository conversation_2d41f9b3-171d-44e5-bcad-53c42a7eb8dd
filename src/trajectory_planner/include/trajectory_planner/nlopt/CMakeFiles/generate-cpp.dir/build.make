# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt

# Utility rule file for generate-cpp.

# Include the progress variables for this target.
include CMakeFiles/generate-cpp.dir/progress.make

CMakeFiles/generate-cpp: nlopt.hpp


nlopt.hpp: src/api/nlopt-in.hpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating nlopt.hpp"
	/usr/bin/cmake -DAPI_SOURCE_DIR=/home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/src/api -P /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/cmake/generate-cpp.cmake

generate-cpp: CMakeFiles/generate-cpp
generate-cpp: nlopt.hpp
generate-cpp: CMakeFiles/generate-cpp.dir/build.make

.PHONY : generate-cpp

# Rule to build all files generated by this target.
CMakeFiles/generate-cpp.dir/build: generate-cpp

.PHONY : CMakeFiles/generate-cpp.dir/build

CMakeFiles/generate-cpp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/generate-cpp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/generate-cpp.dir/clean

CMakeFiles/generate-cpp.dir/depend:
	cd /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt /home/<USER>/lxy_new_ws/src/Intent-MPC-main/trajectory_planner/include/trajectory_planner/nlopt/CMakeFiles/generate-cpp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/generate-cpp.dir/depend

