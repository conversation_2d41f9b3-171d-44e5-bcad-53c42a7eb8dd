/*
	FILE: obstacleClustering.cpp
	------------------------
	function implementation of static obstacle clustering
*/

#include <trajectory_planner/clustering/obstacleClustering.h>

obstacleClustering::obstacleClustering(double res){
	this->res_ = res;
}


void obstacleClustering::run(const std::vector<Eigen::Vector3d>& localCloud) {
    if (localCloud.size() != 0) { // 如果局部点云不为空
        // 执行DBSCAN聚类
        this->runDBSCAN(localCloud, this->initialClusters_);

        // 对每个DBSCAN聚类结果执行KMeans聚类
        std::vector<pointCluster> refinedCluster = this->initialClusters_; // 初始化精细化聚类结果
        std::vector<bool> refineComplete(int(refinedCluster.size()), false); // 标记每个聚类是否完成精细化
        std::vector<bboxVertex> rotatedInitialBBoxes; // 存储初始旋转包围盒

        for (int level = 0; level < this->treeLevel_; ++level) { // 遍历树的层级
            std::vector<pointCluster> newRefinedCluster; // 存储新的精细化聚类结果
            std::vector<bool> newRefinedComplete; // 存储新的精细化完成标记

            for (int n = 0; n < int(refinedCluster.size()); ++n) { // 遍历每个聚类
                if (not refineComplete[n]) { // 如果当前聚类未完成精细化
                    bboxVertex vertex; // 定义包围盒顶点
                    double density = this->getOrientation(refinedCluster[n], vertex); // 获取包围盒方向和密度
                    rotatedInitialBBoxes.push_back(vertex); // 保存初始旋转包围盒

                    if (density >= this->densityThresh_) { // 如果密度大于阈值，标记为完成
                        newRefinedCluster.push_back(refinedCluster[n]);
                        newRefinedComplete.push_back(true);
                    } else { // 否则，将其分割为两个子聚类
                        std::vector<pointCluster> subCloudClusters; // 存储子聚类
                        this->runKmeans(refinedCluster[n], subCloudClusters); // 执行KMeans聚类

                        // 评估新聚类
                        std::vector<bboxVertex> subVertices; // 存储子包围盒
                        double avgDensity = 0.0; // 平均密度
                        for (int i = 0; i < int(subCloudClusters.size()); ++i) {
                            bboxVertex subVertex;
                            double subDensity = this->getOrientation(subCloudClusters[i], subVertex); // 获取子聚类的方向和密度
                            avgDensity += subDensity / int(subCloudClusters.size()); // 累加密度
                            subVertices.push_back(subVertex); // 保存子包围盒
                        }

                        // 将子聚类添加到新的精细化结果中
                        for (int i = 0; i < int(subCloudClusters.size()); ++i) {
                            newRefinedCluster.push_back(subCloudClusters[i]);
                            newRefinedComplete.push_back(false); // 标记为未完成
                        }
                    }
                } else { // 如果当前聚类已完成精细化
                    newRefinedCluster.push_back(refinedCluster[n]); // 直接添加到新的精细化结果中
                    newRefinedComplete.push_back(true); // 标记为完成
                }
            }

            refinedCluster = newRefinedCluster; // 更新精细化聚类结果
            refineComplete = newRefinedComplete; // 更新精细化完成标记
        }

        std::vector<bboxVertex> refinedRotatedBBoxes; // 存储精细化后的旋转包围盒
        for (int i = 0; i < int(refinedCluster.size()); ++i) { // 遍历每个精细化聚类
            bboxVertex vertex;
            this->getOrientation(refinedCluster[i], vertex); // 获取包围盒方向
            refinedRotatedBBoxes.push_back(vertex); // 保存精细化后的旋转包围盒
        }

        this->refinedRotatedBBoxes_ = refinedRotatedBBoxes; // 保存精细化后的旋转包围盒
        this->rotatedInitialBBoxes_ = rotatedInitialBBoxes; // 保存初始旋转包围盒
    }
}

void obstacleClustering::runDBSCAN(const std::vector<Eigen::Vector3d>& localCloud, std::vector<pointCluster>& cloudClusters) {
    std::vector<Point> points; // 定义点列表
    for (int i = 0; i < int(localCloud.size()); ++i) { // 遍历局部点云
        points.push_back(Point(localCloud[i](0), localCloud[i](1), localCloud[i](2))); // 将点云转换为DBSCAN点格式
    }

    this->dbscan_.reset(new DBSCAN(this->eps_, this->minPts_, points)); // 初始化DBSCAN对象
    this->dbscan_->run(); // 执行DBSCAN聚类

    std::vector<std::vector<int>> cluster = this->dbscan_->getCluster(); // 获取聚类结果
    cloudClusters.clear(); // 清空聚类结果
    for (int i = 0; i < int(cluster.size()); ++i) { // 遍历每个聚类
        Eigen::Vector3d clusterMin = localCloud[cluster[i][0]]; // 初始化聚类的最小点
        Eigen::Vector3d clusterMax = localCloud[cluster[i][0]]; // 初始化聚类的最大点
        std::vector<Eigen::Vector3d> cloudCluster; // 存储当前聚类的点

        for (int j = 0; j < int(cluster[i].size()); ++j) { // 遍历聚类中的每个点
            Eigen::Vector3d p = localCloud[cluster[i][j]]; // 获取点
            cloudCluster.push_back(p); // 添加到当前聚类中

            // 更新聚类的最小点和最大点
            if (p(0) < clusterMin(0)) {
                clusterMin(0) = p(0);
            } else if (p(0) > clusterMax(0)) {
                clusterMax(0) = p(0);
            }

            if (p(1) < clusterMin(1)) {
                clusterMin(1) = p(1);
            } else if (p(1) > clusterMax(1)) {
                clusterMax(1) = p(1);
            }

            if (p(2) < clusterMin(2)) {
                clusterMin(2) = p(2);
            } else if (p(2) > clusterMax(2)) {
                clusterMax(2) = p(2);
            }
        }

        Eigen::Vector3d centroid = (clusterMin + clusterMax) / 2.0; // 计算聚类的质心
        cloudClusters.push_back(pointCluster(cloudCluster, i, centroid, clusterMin, clusterMax)); // 保存聚类结果
    }
}

void obstacleClustering::runKmeans(const pointCluster& cluster, std::vector<pointCluster>& cloudClusters) {
    int dimension = 3; // 数据维度为3（x, y, z）
    int clusterNum = 2; // 目标聚类数为2

    // 初始化质心
    double** data = new double*[int(cluster.points.size())]; // 分配存储点云数据的二维数组
    double maxDist = 0.0; // 初始化最大距离
    Eigen::Vector3d fpoint; // 定义第一个质心点

    for (int i = 0; i < int(cluster.points.size()); ++i) { // 遍历聚类中的每个点
        Eigen::Vector3d diff = cluster.points[i] - cluster.centroid; // 计算点到质心的向量
        double dist = diff.norm(); // 计算点到质心的欧几里得距离

        if (dist > maxDist) { // 如果当前点距离大于最大距离
            maxDist = dist; // 更新最大距离
            fpoint = cluster.points[i]; // 更新第一个质心点
        }

        // 转换点云数据为二维数组格式
        data[i] = new double[dimension];
        data[i][0] = cluster.points[i](0); // x 坐标
        data[i][1] = cluster.points[i](1); // y 坐标
        data[i][2] = cluster.points[i](2); // z 坐标
    }

    // 找到距离第一个质心点最远的点作为第二个质心点
    maxDist = 0.0; // 重置最大距离
    Eigen::Vector3d ffpoint; // 定义第二个质心点
    for (int i = 0; i < int(cluster.points.size()); ++i) {
        Eigen::Vector3d diff = cluster.points[i] - fpoint; // 计算点到第一个质心点的向量
        double dist = diff.norm(); // 计算距离
        if (dist > maxDist) { // 如果当前点距离大于最大距离
            maxDist = dist; // 更新最大距离
            ffpoint = cluster.points[i]; // 更新第二个质心点
        }
    }

    std::vector<Eigen::Vector3d> initPoints{fpoint, ffpoint}; // 初始化质心点集合

    // 初始化 KMeans 算法
    this->kmeans_.reset(new KMeans(dimension, clusterNum)); // 创建 KMeans 对象
    this->kmeans_->centroid = new double*[clusterNum]; // 分配存储质心的二维数组
    for (int i = 0; i < clusterNum; ++i) {
        this->kmeans_->centroid[i] = new double[dimension];
        this->kmeans_->centroid[i][0] = initPoints[i](0); // 设置质心的 x 坐标
        this->kmeans_->centroid[i][1] = initPoints[i](1); // 设置质心的 y 坐标
        this->kmeans_->centroid[i][2] = initPoints[i](2); // 设置质心的 z 坐标
    }

    // 运行 KMeans 算法
    for (int i = 0; i < this->kmeansIterNum_; ++i) {
        this->kmeans_->Cluster(int(cluster.points.size()), data); // 对点云数据进行聚类
    }

    // 获取聚类结果
    cloudClusters.resize(clusterNum); // 调整输出聚类的大小
    for (int i = 0; i < int(cluster.points.size()); ++i) {
        int clusterIdx = this->kmeans_->Classify(data[i]); // 获取点所属的聚类索引
        cloudClusters[clusterIdx].id = clusterIdx; // 设置聚类 ID
        cloudClusters[clusterIdx].points.push_back(cluster.points[i]); // 将点添加到对应的聚类中
    }

    // 计算每个子聚类的质心和边界
    for (int i = 0; i < int(cloudClusters.size()); ++i) {
        Eigen::Vector3d clusterMin = cloudClusters[i].points[0]; // 初始化最小点
        Eigen::Vector3d clusterMax = cloudClusters[i].points[0]; // 初始化最大点
        for (int j = 0; j < int(cloudClusters[i].points.size()); ++j) {
            Eigen::Vector3d p = cloudClusters[i].points[j]; // 获取点

            // 更新最小点和最大点
            if (p(0) < clusterMin(0)) clusterMin(0) = p(0);
            else if (p(0) > clusterMax(0)) clusterMax(0) = p(0);

            if (p(1) < clusterMin(1)) clusterMin(1) = p(1);
            else if (p(1) > clusterMax(1)) clusterMax(1) = p(1);

            if (p(2) < clusterMin(2)) clusterMin(2) = p(2);
            else if (p(2) > clusterMax(2)) clusterMax(2) = p(2);
        }
        cloudClusters[i].centroid = (clusterMin + clusterMax) / 2.0; // 计算质心
        cloudClusters[i].clusterMin = clusterMin; // 设置最小点
        cloudClusters[i].clusterMax = clusterMax; // 设置最大点
    }

    delete data; // 释放动态分配的内存
}

double obstacleClustering::getOrientation(const pointCluster& cluster, bboxVertex& vertex) {
    double maxDensity = 0.0; // 初始化最大密度
    double bestAngle = 0.0; // 初始化最佳角度
    bboxVertex bestVertex; // 初始化最佳包围盒

    for (int i = 0; i < this->angleDiscreteNum_; ++i) { // 遍历离散的旋转角度
        double angle = M_PI * double(i) / double(this->angleDiscreteNum_); // 计算当前角度
        Eigen::Matrix3d rot; // 定义旋转矩阵
        rot << cos(angle), -sin(angle), 0, sin(angle), cos(angle), 0, 0, 0, 1; // 构造绕 z 轴的旋转矩阵

        Eigen::Vector3d rotClusterMin = cluster.points[0]; // 初始化旋转后的最小点
        Eigen::Vector3d rotClusterMax = cluster.points[0]; // 初始化旋转后的最大点

        for (int n = 0; n < int(cluster.points.size()); ++n) { // 遍历聚类中的每个点
            Eigen::Vector3d p = cluster.points[n]; // 获取点
            Eigen::Vector3d pNew = rot * (p - cluster.centroid) + cluster.centroid; // 旋转点并平移回原位置

            // 更新旋转后的最小点和最大点
            if (pNew(0) < rotClusterMin(0)) rotClusterMin(0) = pNew(0);
            else if (pNew(0) > rotClusterMax(0)) rotClusterMax(0) = pNew(0);

            if (pNew(1) < rotClusterMin(1)) rotClusterMin(1) = pNew(1);
            else if (pNew(1) > rotClusterMax(1)) rotClusterMax(1) = pNew(1);

            if (pNew(2) < rotClusterMin(2)) rotClusterMin(2) = pNew(2);
            else if (pNew(2) > rotClusterMax(2)) rotClusterMax(2) = pNew(2);
        }

        bboxVertex currVertex(rotClusterMin, rotClusterMax, -angle); // 构造当前包围盒
        double lengthNum = (rotClusterMax(0) - rotClusterMin(0)) / this->res_ + 1; // 计算包围盒的长度网格数
        double widthNum = (rotClusterMax(1) - rotClusterMin(1)) / this->res_ + 1; // 计算包围盒的宽度网格数
        double heightNum = (rotClusterMax(2) - rotClusterMin(2)) / this->res_ + 1; // 计算包围盒的高度网格数
        double density = double(cluster.points.size()) / (lengthNum * widthNum * heightNum); // 计算密度

        if (density > maxDensity) { // 如果当前密度大于最大密度
            maxDensity = density; // 更新最大密度
            bestAngle = angle; // 更新最佳角度
            bestVertex = currVertex; // 更新最佳包围盒
        }
    }

    // 将最佳包围盒的顶点旋转回原始方向
    for (int i = 0; i < int(bestVertex.vert.size()); ++i) {
        Eigen::Matrix3d rot;
        rot << cos(-bestAngle), -sin(-bestAngle), 0, sin(-bestAngle), cos(-bestAngle), 0, 0, 0, 1; // 构造逆旋转矩阵
        Eigen::Vector3d v = bestVertex.vert[i]; // 获取顶点
        Eigen::Vector3d newVertex = rot * (v - cluster.centroid) + cluster.centroid; // 旋转顶点并平移回原位置
        bestVertex.vert[i] = newVertex; // 更新顶点
    }

    vertex = bestVertex; // 设置输出的最佳包围盒
    return maxDensity; // 返回最大密度
}


// 获取初始聚类结果
std::vector<pointCluster> obstacleClustering::getInitialCluster() {
    // 返回初始聚类结果
    return this->initialClusters_; // 返回存储的初始聚类结果
}

// 获取初始旋转包围盒
std::vector<bboxVertex> obstacleClustering::getRotatedInitialBBoxes() {
    // 返回初始旋转包围盒
    return this->rotatedInitialBBoxes_; // 返回存储的初始旋转包围盒
}

// 获取精细化后的旋转包围盒
std::vector<bboxVertex> obstacleClustering::getRefinedBBoxes() {
    // 返回精细化后的旋转包围盒
    return this->refinedRotatedBBoxes_; // 返回存储的精细化旋转包围盒
}

// 获取静态障碍物信息
std::vector<staticObstacle> obstacleClustering::getStaticObstacles() {
    std::vector<staticObstacle> staticObstacles; // 定义存储静态障碍物的容器
    for (int i = 0; i < int(this->refinedRotatedBBoxes_.size()); ++i) { // 遍历每个精细化后的旋转包围盒
        staticObstacle sob; // 定义一个静态障碍物对象
        sob.centroid = this->refinedRotatedBBoxes_[i].centroid; // 设置障碍物的质心
        sob.size = this->refinedRotatedBBoxes_[i].dimension; // 设置障碍物的尺寸
        sob.yaw = this->refinedRotatedBBoxes_[i].angle; // 设置障碍物的偏航角
        staticObstacles.push_back(sob); // 将静态障碍物添加到容器中
    }
    return staticObstacles; // 返回静态障碍物列表
}