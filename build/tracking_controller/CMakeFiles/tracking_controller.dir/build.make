# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

# Include any dependencies generated for this target.
include tracking_controller/CMakeFiles/tracking_controller.dir/depend.make

# Include the progress variables for this target.
include tracking_controller/CMakeFiles/tracking_controller.dir/progress.make

# Include the compile flags for this target's objects.
include tracking_controller/CMakeFiles/tracking_controller.dir/flags.make

tracking_controller/CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.o: tracking_controller/CMakeFiles/tracking_controller.dir/flags.make
tracking_controller/CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.o: /home/<USER>/lxy_ws/src/tracking_controller/include/tracking_controller/trackingController.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object tracking_controller/CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.o"
	cd /home/<USER>/lxy_ws/build/tracking_controller && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.o -c /home/<USER>/lxy_ws/src/tracking_controller/include/tracking_controller/trackingController.cpp

tracking_controller/CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.i"
	cd /home/<USER>/lxy_ws/build/tracking_controller && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/src/tracking_controller/include/tracking_controller/trackingController.cpp > CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.i

tracking_controller/CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.s"
	cd /home/<USER>/lxy_ws/build/tracking_controller && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/src/tracking_controller/include/tracking_controller/trackingController.cpp -o CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.s

# Object files for target tracking_controller
tracking_controller_OBJECTS = \
"CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.o"

# External object files for target tracking_controller
tracking_controller_EXTERNAL_OBJECTS =

/home/<USER>/lxy_ws/devel/lib/libtracking_controller.so: tracking_controller/CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.o
/home/<USER>/lxy_ws/devel/lib/libtracking_controller.so: tracking_controller/CMakeFiles/tracking_controller.dir/build.make
/home/<USER>/lxy_ws/devel/lib/libtracking_controller.so: tracking_controller/CMakeFiles/tracking_controller.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library /home/<USER>/lxy_ws/devel/lib/libtracking_controller.so"
	cd /home/<USER>/lxy_ws/build/tracking_controller && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tracking_controller.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
tracking_controller/CMakeFiles/tracking_controller.dir/build: /home/<USER>/lxy_ws/devel/lib/libtracking_controller.so

.PHONY : tracking_controller/CMakeFiles/tracking_controller.dir/build

tracking_controller/CMakeFiles/tracking_controller.dir/clean:
	cd /home/<USER>/lxy_ws/build/tracking_controller && $(CMAKE_COMMAND) -P CMakeFiles/tracking_controller.dir/cmake_clean.cmake
.PHONY : tracking_controller/CMakeFiles/tracking_controller.dir/clean

tracking_controller/CMakeFiles/tracking_controller.dir/depend:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lxy_ws/src /home/<USER>/lxy_ws/src/tracking_controller /home/<USER>/lxy_ws/build /home/<USER>/lxy_ws/build/tracking_controller /home/<USER>/lxy_ws/build/tracking_controller/CMakeFiles/tracking_controller.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : tracking_controller/CMakeFiles/tracking_controller.dir/depend

