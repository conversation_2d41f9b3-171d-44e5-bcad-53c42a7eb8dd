# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/lxy_ws/src/tracking_controller/msg/Target.msg"
services_str = ""
pkg_name = "tracking_controller"
dependencies_str = "std_msgs;geometry_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "tracking_controller;/home/<USER>/lxy_ws/src/tracking_controller/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg;geometry_msgs;/opt/ros/noetic/share/geometry_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
