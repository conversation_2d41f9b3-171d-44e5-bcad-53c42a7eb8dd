# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile CXX with /usr/bin/c++
CXX_FLAGS =  -mavx -mfma -ffast-math -march=native -O3 -DNDEBUG   -std=gnu++14

CXX_DEFINES = -DDISABLE_LIBUSB_1_0 -DDISABLE_PCAP -DDISABLE_PNG -DLINUX -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"planner\"

CXX_INCLUDES = -I/home/<USER>/lxy_ws/src/planner/./third -I/usr/lib -I/home/<USER>/lxy_ws/devel/include -I/home/<USER>/lxy_ws/src/tracking_controller/include -I/home/<USER>/lxy_ws/src/dynamic_predictor/include -I/home/<USER>/lxy_ws/src/trajectory_planner/include -I/home/<USER>/lxy_ws/src/map_manager/include -I/home/<USER>/lxy_ws/src/onboard_detector/include -I/home/<USER>/lxy_ws/src/global_planner/include -I/home/<USER>/lxy_ws/src/GCOPTER/gcopter/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/home/<USER>/lxy_ws/src/planner/src -I/home/<USER>/lxy_ws/src/planner/modules -I/home/<USER>/lxy_ws/src/planner/../kr_opt_sfc/opt_sfc/include -isystem /usr/include/eigen3 -isystem /usr/include/pcl-1.10 -isystem /usr/include/vtk-7.1 -isystem /usr/include/freetype2 -isystem /usr/include/ni -isystem /usr/include/openni2 -isystem /usr/include/opencv4 -isystem /usr/include/python3.8 -isystem /home/<USER>/.local/lib/python3.8/site-packages/numpy/core/include 

