set(_CATKIN_CURRENT_PACKAGE "planner")
set(planner_VERSION "0.0.0")
set(planner_MAINTAINER "<PERSON>ang<PERSON><PERSON>o <<EMAIL>>")
set(planner_PACKAGE_FORMAT "2")
set(planner_BUILD_DEPENDS "roslib" "roscpp" "tf" "tracking_controller" "map_manager" "onboard_detector" "dynamic_predictor" "global_planner" "trajectory_planner" "opt_sfc" "gcopter")
set(planner_BUILD_EXPORT_DEPENDS "roslib" "roscpp" "tf" "tracking_controller" "map_manager" "onboard_detector" "dynamic_predictor" "global_planner" "trajectory_planner" "opt_sfc" "gcopter")
set(planner_BUILDTOOL_DEPENDS "catkin")
set(planner_BUILDTOOL_EXPORT_DEPENDS )
set(planner_EXEC_DEPENDS "roslib" "roscpp" "tf" "tracking_controller" "map_manager" "onboard_detector" "dynamic_predictor" "global_planner" "trajectory_planner" "opt_sfc" "gcopter")
set(planner_RUN_DEPENDS "roslib" "roscpp" "tf" "tracking_controller" "map_manager" "onboard_detector" "dynamic_predictor" "global_planner" "trajectory_planner" "opt_sfc" "gcopter")
set(planner_TEST_DEPENDS )
set(planner_DOC_DEPENDS )
set(planner_URL_WEBSITE "")
set(planner_URL_BUGTRACKER "")
set(planner_URL_REPOSITORY "")
set(planner_DEPRECATED "")