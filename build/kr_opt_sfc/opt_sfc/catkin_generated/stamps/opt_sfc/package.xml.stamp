<package>
  <name>opt_sfc</name>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <version>0.1.0</version>
  <description>the opt sfc</description>
  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>gcopter</build_depend>
  <build_depend>message_generation</build_depend>
  
  <run_depend>roscpp</run_depend>
  <run_depend>std_msgs</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>visualization_msgs</run_depend>
  <run_depend>nav_msgs</run_depend>
  <run_depend>gcopter</run_depend>
  <run_depend>message_runtime</run_depend>


</package>
