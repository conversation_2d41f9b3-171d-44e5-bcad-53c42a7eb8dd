set(_CATKIN_CURRENT_PACKAGE "opt_sfc")
set(opt_sfc_VERSION "0.1.0")
set(opt_sfc_MAINTAINER "<PERSON><PERSON> <<EMAIL>>")
set(opt_sfc_PACKAGE_FORMAT "1")
set(opt_sfc_BUILD_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs" "nav_msgs" "gcopter" "message_generation")
set(opt_sfc_BUILD_EXPORT_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs" "nav_msgs" "gcopter" "message_runtime")
set(opt_sfc_BUILDTOOL_DEPENDS "catkin")
set(opt_sfc_BUILDTOOL_EXPORT_DEPENDS )
set(opt_sfc_EXEC_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs" "nav_msgs" "gcopter" "message_runtime")
set(opt_sfc_RUN_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs" "nav_msgs" "gcopter" "message_runtime")
set(opt_sfc_TEST_DEPENDS )
set(opt_sfc_DOC_DEPENDS )
set(opt_sfc_URL_WEBSITE "")
set(opt_sfc_URL_BUGTRACKER "")
set(opt_sfc_URL_REPOSITORY "")
set(opt_sfc_DEPRECATED "")