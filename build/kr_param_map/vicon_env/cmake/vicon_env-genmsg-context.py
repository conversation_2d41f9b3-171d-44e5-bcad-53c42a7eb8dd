# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/Point2d.msg;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/Point3d.msg;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/Ellipse.msg;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/Circle.msg;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/Polygon.msg;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/Cylinder.msg;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/Polyhedron.msg;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg/SemanticArray.msg"
services_str = ""
pkg_name = "vicon_env"
dependencies_str = "std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "vicon_env;/home/<USER>/lxy_ws/src/kr_param_map/vicon_env/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
