# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/lxy_ws/devel/include".split(';') if "/home/<USER>/lxy_ws/devel/include" != "" else []
PROJECT_CATKIN_DEPENDS = "message_runtime".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "vicon_env"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/devel"
PROJECT_VERSION = "1.3.0"
