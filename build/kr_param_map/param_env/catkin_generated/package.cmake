set(_CATKIN_CURRENT_PACKAGE "param_env")
set(param_env_VERSION "1.0.0")
set(param_env_MAINTAINER "Yu<PERSON> <<EMAIL>>")
set(param_env_PACKAGE_FORMAT "2")
set(param_env_BUILD_DEPENDS "rosbag" "roscpp" "std_msgs" "sdl" "sdl-image")
set(param_env_BUILD_EXPORT_DEPENDS "rosbag" "roscpp" "std_msgs" "sdl" "sdl-image")
set(param_env_BUILDTOOL_DEPENDS "catkin")
set(param_env_BUILDTOOL_EXPORT_DEPENDS )
set(param_env_EXEC_DEPENDS "rosbag" "roscpp" "std_msgs" "sdl" "sdl-image")
set(param_env_RUN_DEPENDS "rosbag" "roscpp" "std_msgs" "sdl" "sdl-image")
set(param_env_TEST_DEPENDS )
set(param_env_DOC_DEPENDS )
set(param_env_URL_WEBSITE "")
set(param_env_URL_BUGTRACKER "")
set(param_env_URL_REPOSITORY "")
set(param_env_DEPRECATED "")