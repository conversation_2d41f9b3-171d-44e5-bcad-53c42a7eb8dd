# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/lxy_ws/src/kr_param_map/param_env/include".split(';') if "/home/<USER>/lxy_ws/src/kr_param_map/param_env/include" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lparam_env".split(';') if "-lparam_env" != "" else []
PROJECT_NAME = "param_env"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/devel"
PROJECT_VERSION = "1.0.0"
