# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "${prefix}/include".split(';') if "${prefix}/include" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lparam_env".split(';') if "-lparam_env" != "" else []
PROJECT_NAME = "param_env"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/install"
PROJECT_VERSION = "1.0.0"
