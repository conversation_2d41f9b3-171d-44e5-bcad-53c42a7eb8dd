-I/usr/include/vtk-7.1
-I/usr/include/freetype2
-I/usr/include
-I/usr/include/x86_64-linux-gnu
-I/usr/include
-I/usr/include
-I/usr/include
-I/usr/include
-I/usr/include
-DROS_BUILD_SHARED_LIBS=1
-DROS_BUILD_SHARED_LIBS=1
-DROS_PACKAGE_NAME="uav_simulator"
-DROSCONSOLE_BACKEND_LOG4CXX
-DLIBBULLET_VERSION=2.88
-DLIBBULLET_VERSION_GT_282
-DBOOST_TEST_DYN_LINK
-DvtkRenderingContext2D_AUTOINIT=1(vtkRenderingContextOpenGL2)
-DvtkRenderingCore_AUTOINIT=3(vtkInteractionStyle,vtkRenderingFreeType,vtkRenderingOpenGL2)
-Dqh_QHpointer
-o
/home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp
/home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/DialogKeyboard.h
