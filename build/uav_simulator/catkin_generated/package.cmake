set(_CATKIN_CURRENT_PACKAGE "uav_simulator")
set(uav_simulator_VERSION "1.0.0")
set(uav_simulator_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(uav_simulator_PACKAGE_FORMAT "2")
set(uav_simulator_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "sensor_msgs" "message_generation" "gazebo_ros" "mavros" "mavros_extras")
set(uav_simulator_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_generation" "gazebo_ros")
set(uav_simulator_BUILDTOOL_DEPENDS "catkin")
set(uav_simulator_BUILDTOOL_EXPORT_DEPENDS )
set(uav_simulator_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "sensor_msgs" "message_runtime" "gazebo_ros")
set(uav_simulator_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "sensor_msgs" "message_runtime" "gazebo_ros" "message_generation")
set(uav_simulator_TEST_DEPENDS )
set(uav_simulator_DOC_DEPENDS )
set(uav_simulator_URL_WEBSITE "")
set(uav_simulator_URL_BUGTRACKER "")
set(uav_simulator_URL_REPOSITORY "")
set(uav_simulator_DEPRECATED "")