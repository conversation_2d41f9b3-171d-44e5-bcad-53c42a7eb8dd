# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/lxy_ws/devel/include;/home/<USER>/lxy_ws/src/uav_simulator/include".split(';') if "/home/<USER>/lxy_ws/devel/include;/home/<USER>/lxy_ws/src/uav_simulator/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;rospy;std_msgs;sensor_msgs;geometry_msgs;message_runtime;gazebo_ros".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "uav_simulator"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/devel"
PROJECT_VERSION = "1.0.0"
