# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

# Utility rule file for quadcopterPlugin_autogen.

# Include the progress variables for this target.
include uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/progress.make

uav_simulator/CMakeFiles/quadcopterPlugin_autogen:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic UIC for target quadcopterPlugin"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/cmake -E cmake_autogen /home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/AutogenInfo.json ""

quadcopterPlugin_autogen: uav_simulator/CMakeFiles/quadcopterPlugin_autogen
quadcopterPlugin_autogen: uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/build.make

.PHONY : quadcopterPlugin_autogen

# Rule to build all files generated by this target.
uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/build: quadcopterPlugin_autogen

.PHONY : uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/build

uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/clean:
	cd /home/<USER>/lxy_ws/build/uav_simulator && $(CMAKE_COMMAND) -P CMakeFiles/quadcopterPlugin_autogen.dir/cmake_clean.cmake
.PHONY : uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/clean

uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/depend:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lxy_ws/src /home/<USER>/lxy_ws/src/uav_simulator /home/<USER>/lxy_ws/build /home/<USER>/lxy_ws/build/uav_simulator /home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/depend

