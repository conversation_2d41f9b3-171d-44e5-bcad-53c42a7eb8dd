{"BUILD_DIR": "/home/<USER>/lxy_ws/build/uav_simulator/obstaclePathPlugin_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/lxy_ws/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/lxy_ws/build/uav_simulator", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/lxy_ws/src/uav_simulator", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_SOURCE_DIR": "/home/<USER>/lxy_ws/src", "HEADERS": [], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/lxy_ws/build/uav_simulator/obstaclePathPlugin_autogen/include", "MULTI_CONFIG": false, "PARALLEL": 12, "PARSE_CACHE_FILE": "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "", "QT_UIC_EXECUTABLE": "/usr/lib/qt5/bin/uic", "QT_VERSION_MAJOR": 5, "SETTINGS_FILE": "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/lxy_ws/src/uav_simulator/src/obstaclePathPlugin.cc", "mU"]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}