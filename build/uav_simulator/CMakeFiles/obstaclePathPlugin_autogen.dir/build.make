# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

# Utility rule file for obstaclePathPlugin_autogen.

# Include the progress variables for this target.
include uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/progress.make

uav_simulator/CMakeFiles/obstaclePathPlugin_autogen:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic UIC for target obstaclePathPlugin"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/cmake -E cmake_autogen /home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/AutogenInfo.json ""

obstaclePathPlugin_autogen: uav_simulator/CMakeFiles/obstaclePathPlugin_autogen
obstaclePathPlugin_autogen: uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/build.make

.PHONY : obstaclePathPlugin_autogen

# Rule to build all files generated by this target.
uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/build: obstaclePathPlugin_autogen

.PHONY : uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/build

uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/clean:
	cd /home/<USER>/lxy_ws/build/uav_simulator && $(CMAKE_COMMAND) -P CMakeFiles/obstaclePathPlugin_autogen.dir/cmake_clean.cmake
.PHONY : uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/clean

uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/depend:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lxy_ws/src /home/<USER>/lxy_ws/src/uav_simulator /home/<USER>/lxy_ws/build /home/<USER>/lxy_ws/build/uav_simulator /home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/depend

