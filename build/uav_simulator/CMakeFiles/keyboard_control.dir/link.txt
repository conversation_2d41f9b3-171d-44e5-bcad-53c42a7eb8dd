/usr/bin/c++       -rdynamic CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o  -o /home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control   -L/usr/lib/x86_64-linux-gnu/gazebo-11/plugins  -Wl,-rpath,/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/noetic/lib /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.12.8 /opt/ros/noetic/lib/libtf2_ros.so /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/libtf2.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.12.8 /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.12.8 
