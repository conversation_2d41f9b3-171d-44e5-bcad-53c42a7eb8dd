# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o
 /home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp
 /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/DialogKeyboard.h
 /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/droneObjectRos.h
 /opt/ros/noetic/include/geometry_msgs/Twist.h
 /opt/ros/noetic/include/geometry_msgs/TwistStamped.h
 /opt/ros/noetic/include/geometry_msgs/Vector3.h
 /opt/ros/noetic/include/ros/advertise_options.h
 /opt/ros/noetic/include/ros/advertise_service_options.h
 /opt/ros/noetic/include/ros/assert.h
 /opt/ros/noetic/include/ros/builtin_message_traits.h
 /opt/ros/noetic/include/ros/common.h
 /opt/ros/noetic/include/ros/console.h
 /opt/ros/noetic/include/ros/console_backend.h
 /opt/ros/noetic/include/ros/datatypes.h
 /opt/ros/noetic/include/ros/duration.h
 /opt/ros/noetic/include/ros/exception.h
 /opt/ros/noetic/include/ros/exceptions.h
 /opt/ros/noetic/include/ros/forwards.h
 /opt/ros/noetic/include/ros/init.h
 /opt/ros/noetic/include/ros/macros.h
 /opt/ros/noetic/include/ros/master.h
 /opt/ros/noetic/include/ros/message.h
 /opt/ros/noetic/include/ros/message_event.h
 /opt/ros/noetic/include/ros/message_forward.h
 /opt/ros/noetic/include/ros/message_operations.h
 /opt/ros/noetic/include/ros/message_traits.h
 /opt/ros/noetic/include/ros/names.h
 /opt/ros/noetic/include/ros/node_handle.h
 /opt/ros/noetic/include/ros/param.h
 /opt/ros/noetic/include/ros/parameter_adapter.h
 /opt/ros/noetic/include/ros/platform.h
 /opt/ros/noetic/include/ros/publisher.h
 /opt/ros/noetic/include/ros/rate.h
 /opt/ros/noetic/include/ros/ros.h
 /opt/ros/noetic/include/ros/roscpp_serialization_macros.h
 /opt/ros/noetic/include/ros/rostime_decl.h
 /opt/ros/noetic/include/ros/serialization.h
 /opt/ros/noetic/include/ros/serialized_message.h
 /opt/ros/noetic/include/ros/service.h
 /opt/ros/noetic/include/ros/service_callback_helper.h
 /opt/ros/noetic/include/ros/service_client.h
 /opt/ros/noetic/include/ros/service_client_options.h
 /opt/ros/noetic/include/ros/service_server.h
 /opt/ros/noetic/include/ros/service_traits.h
 /opt/ros/noetic/include/ros/single_subscriber_publisher.h
 /opt/ros/noetic/include/ros/spinner.h
 /opt/ros/noetic/include/ros/static_assert.h
 /opt/ros/noetic/include/ros/steady_timer.h
 /opt/ros/noetic/include/ros/steady_timer_options.h
 /opt/ros/noetic/include/ros/subscribe_options.h
 /opt/ros/noetic/include/ros/subscriber.h
 /opt/ros/noetic/include/ros/subscription_callback_helper.h
 /opt/ros/noetic/include/ros/this_node.h
 /opt/ros/noetic/include/ros/time.h
 /opt/ros/noetic/include/ros/timer.h
 /opt/ros/noetic/include/ros/timer_options.h
 /opt/ros/noetic/include/ros/topic.h
 /opt/ros/noetic/include/ros/transport_hints.h
 /opt/ros/noetic/include/ros/types.h
 /opt/ros/noetic/include/ros/wall_timer.h
 /opt/ros/noetic/include/ros/wall_timer_options.h
 /opt/ros/noetic/include/rosconsole/macros_generated.h
 /opt/ros/noetic/include/std_msgs/Bool.h
 /opt/ros/noetic/include/std_msgs/Empty.h
 /opt/ros/noetic/include/std_msgs/Header.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcDecl.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcValue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringliteral.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringview.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o
 /home/<USER>/lxy_ws/build/uav_simulator/qrc_droneKeyboard.cpp
uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o
 /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/DialogKeyboard.h
 /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/droneObjectRos.h
 /home/<USER>/lxy_ws/src/uav_simulator/src/DialogKeyboard.cpp
 /opt/ros/noetic/include/geometry_msgs/Twist.h
 /opt/ros/noetic/include/geometry_msgs/TwistStamped.h
 /opt/ros/noetic/include/geometry_msgs/Vector3.h
 /opt/ros/noetic/include/ros/advertise_options.h
 /opt/ros/noetic/include/ros/advertise_service_options.h
 /opt/ros/noetic/include/ros/assert.h
 /opt/ros/noetic/include/ros/builtin_message_traits.h
 /opt/ros/noetic/include/ros/common.h
 /opt/ros/noetic/include/ros/console.h
 /opt/ros/noetic/include/ros/console_backend.h
 /opt/ros/noetic/include/ros/datatypes.h
 /opt/ros/noetic/include/ros/duration.h
 /opt/ros/noetic/include/ros/exception.h
 /opt/ros/noetic/include/ros/exceptions.h
 /opt/ros/noetic/include/ros/forwards.h
 /opt/ros/noetic/include/ros/init.h
 /opt/ros/noetic/include/ros/macros.h
 /opt/ros/noetic/include/ros/master.h
 /opt/ros/noetic/include/ros/message.h
 /opt/ros/noetic/include/ros/message_event.h
 /opt/ros/noetic/include/ros/message_forward.h
 /opt/ros/noetic/include/ros/message_operations.h
 /opt/ros/noetic/include/ros/message_traits.h
 /opt/ros/noetic/include/ros/names.h
 /opt/ros/noetic/include/ros/node_handle.h
 /opt/ros/noetic/include/ros/param.h
 /opt/ros/noetic/include/ros/parameter_adapter.h
 /opt/ros/noetic/include/ros/platform.h
 /opt/ros/noetic/include/ros/publisher.h
 /opt/ros/noetic/include/ros/rate.h
 /opt/ros/noetic/include/ros/ros.h
 /opt/ros/noetic/include/ros/roscpp_serialization_macros.h
 /opt/ros/noetic/include/ros/rostime_decl.h
 /opt/ros/noetic/include/ros/serialization.h
 /opt/ros/noetic/include/ros/serialized_message.h
 /opt/ros/noetic/include/ros/service.h
 /opt/ros/noetic/include/ros/service_callback_helper.h
 /opt/ros/noetic/include/ros/service_client.h
 /opt/ros/noetic/include/ros/service_client_options.h
 /opt/ros/noetic/include/ros/service_server.h
 /opt/ros/noetic/include/ros/service_traits.h
 /opt/ros/noetic/include/ros/single_subscriber_publisher.h
 /opt/ros/noetic/include/ros/spinner.h
 /opt/ros/noetic/include/ros/static_assert.h
 /opt/ros/noetic/include/ros/steady_timer.h
 /opt/ros/noetic/include/ros/steady_timer_options.h
 /opt/ros/noetic/include/ros/subscribe_options.h
 /opt/ros/noetic/include/ros/subscriber.h
 /opt/ros/noetic/include/ros/subscription_callback_helper.h
 /opt/ros/noetic/include/ros/this_node.h
 /opt/ros/noetic/include/ros/time.h
 /opt/ros/noetic/include/ros/timer.h
 /opt/ros/noetic/include/ros/timer_options.h
 /opt/ros/noetic/include/ros/topic.h
 /opt/ros/noetic/include/ros/transport_hints.h
 /opt/ros/noetic/include/ros/types.h
 /opt/ros/noetic/include/ros/wall_timer.h
 /opt/ros/noetic/include/ros/wall_timer_options.h
 /opt/ros/noetic/include/rosconsole/macros_generated.h
 /opt/ros/noetic/include/std_msgs/Bool.h
 /opt/ros/noetic/include/std_msgs/Empty.h
 /opt/ros/noetic/include/std_msgs/Header.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcDecl.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcValue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QEvent
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QList
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMargins
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMetaType
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QPointF
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QRect
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QScopedPointer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSharedDataPointer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSizeF
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QVariant
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QtCore
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QtCoreDepends
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstracteventdispatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractnativeeventfilter.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractproxymodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractstate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstracttransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qanimationgroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydataops.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydatapointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbitarray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbuffer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraymatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborarray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborcommon.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcbormap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborvalue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcollator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcommandlineoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcommandlineparser.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcryptographichash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdeadlinetimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdiriterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeasingcurve.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qendian.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventtransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qexception.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfactoryinterface.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileselector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfilesystemwatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfinalstate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfloat16.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfuture.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfutureinterface.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfuturesynchronizer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfuturewatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhistorystate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qidentityproxymodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qisenum.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsonarray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsondocument.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsonobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsonvalue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlibrary.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlibraryinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlinkedlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlockfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qloggingcategory.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmath.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmessageauthenticationcode.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetaobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedatabase.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimetype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectcleanuphandler.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qoperatingsystemversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qparallelanimationgroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpauseanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpluginloader.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocess.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpropertyanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrandom.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qreadwritelock.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qresource.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qresultstore.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsavefile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedvaluerollback.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopeguard.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsemaphore.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsequentialanimationgroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsettings.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedmemory.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsignalmapper.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsignaltransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsocketnotifier.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsortfilterproxymodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstack.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstatemachine.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstorageinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlistmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringliteral.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringview.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemsemaphore.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qt_windows.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcoreversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtemporarydir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtemporaryfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextboundaryfinder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextcodec.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadstorage.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimeline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimezone.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtranslator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypetraits.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/quuid.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariantanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversionnumber.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwineventnotifier.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qxmlstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QImage
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QMatrix3x3
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QMatrix4x4
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QOpenGLContext
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPaintDevice
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPaintDeviceWindow
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QSurfaceFormat
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QTransform
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QVector3D
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QWindow
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QtGui
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QtGuiDepends
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qabstracttextdocumentlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessible.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessiblebridge.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessibleobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessibleplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbackingstore.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbitmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qclipboard.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qdesktopservices.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qdrag.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontdatabase.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qgenericmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qgenericplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qgenericpluginfactory.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qglyphrun.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qiconengine.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qiconengineplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimageiohandler.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimagereader.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimagewriter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix4x4.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmovie.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qoffscreensurface.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengl.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglbuffer.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglcontext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengldebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengles2ext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglextrafunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglframebufferobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglpixeltransferoptions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglshaderprogram.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengltexture.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengltextureblitter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengltimerquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglversionfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglvertexarrayobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpagedpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpagelayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpagesize.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevicewindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintengine.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpdfwriter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpicture.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpictureformatplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmapcache.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qquaternion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrasterwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrawfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qscreen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsessionmanager.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qstandarditemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qstatictext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qstylehints.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsurface.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsurfaceformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsyntaxhighlighter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocumentfragment.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocumentwriter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtexttable.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector3d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector4d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QApplication
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCommonStyle
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLabel
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollerProperties
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QVBoxLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QtWidgets
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QtWidgetsDepends
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qaccessiblewidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qaction.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qactiongroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qbuttongroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcalendarwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcolordialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcolormap.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcolumnview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcommandlinkbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcommonstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcompleter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdatawidgetmapper.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdatetimeedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdial.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialogbuttonbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdirmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdockwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdrawutil.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qerrormessage.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfileiconprovider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfilesystemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfocusframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfontcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfontdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qformlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgesture.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgesturerecognizer.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsanchorlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicseffect.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsitemanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicslayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicslayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicslinearlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsproxywidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsscene.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicssceneevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicstransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicswidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qheaderview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qinputdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qitemeditorfactory.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qkeyeventtransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qkeysequenceedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlcdnumber.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmdiarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmdisubwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenu.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenubar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmouseeventtransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qopenglwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qplaintextedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qproxystyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qradiobutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscroller.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollerproperties.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qshortcut.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizegrip.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplashscreen.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstatusbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleditemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstylefactory.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstylepainter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsystemtrayicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtableview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtablewidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextbrowser.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtoolbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtoolbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtoolbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtooltip.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtreeview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtreewidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtreewidgetitemiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qundogroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qundostack.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qundoview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwhatsthis.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidgetaction.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwizard.h
 uav_simulator/keyboard_control_autogen/include/ui_DialogKeyboard.h
uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o
 /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/DialogKeyboard.h
 /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/droneObjectRos.h
 /home/<USER>/lxy_ws/src/uav_simulator/src/droneKeyboard.cpp
 /opt/ros/noetic/include/geometry_msgs/Twist.h
 /opt/ros/noetic/include/geometry_msgs/TwistStamped.h
 /opt/ros/noetic/include/geometry_msgs/Vector3.h
 /opt/ros/noetic/include/ros/advertise_options.h
 /opt/ros/noetic/include/ros/advertise_service_options.h
 /opt/ros/noetic/include/ros/assert.h
 /opt/ros/noetic/include/ros/builtin_message_traits.h
 /opt/ros/noetic/include/ros/common.h
 /opt/ros/noetic/include/ros/console.h
 /opt/ros/noetic/include/ros/console_backend.h
 /opt/ros/noetic/include/ros/datatypes.h
 /opt/ros/noetic/include/ros/duration.h
 /opt/ros/noetic/include/ros/exception.h
 /opt/ros/noetic/include/ros/exceptions.h
 /opt/ros/noetic/include/ros/forwards.h
 /opt/ros/noetic/include/ros/init.h
 /opt/ros/noetic/include/ros/macros.h
 /opt/ros/noetic/include/ros/master.h
 /opt/ros/noetic/include/ros/message.h
 /opt/ros/noetic/include/ros/message_event.h
 /opt/ros/noetic/include/ros/message_forward.h
 /opt/ros/noetic/include/ros/message_operations.h
 /opt/ros/noetic/include/ros/message_traits.h
 /opt/ros/noetic/include/ros/names.h
 /opt/ros/noetic/include/ros/node_handle.h
 /opt/ros/noetic/include/ros/param.h
 /opt/ros/noetic/include/ros/parameter_adapter.h
 /opt/ros/noetic/include/ros/platform.h
 /opt/ros/noetic/include/ros/publisher.h
 /opt/ros/noetic/include/ros/rate.h
 /opt/ros/noetic/include/ros/ros.h
 /opt/ros/noetic/include/ros/roscpp_serialization_macros.h
 /opt/ros/noetic/include/ros/rostime_decl.h
 /opt/ros/noetic/include/ros/serialization.h
 /opt/ros/noetic/include/ros/serialized_message.h
 /opt/ros/noetic/include/ros/service.h
 /opt/ros/noetic/include/ros/service_callback_helper.h
 /opt/ros/noetic/include/ros/service_client.h
 /opt/ros/noetic/include/ros/service_client_options.h
 /opt/ros/noetic/include/ros/service_server.h
 /opt/ros/noetic/include/ros/service_traits.h
 /opt/ros/noetic/include/ros/single_subscriber_publisher.h
 /opt/ros/noetic/include/ros/spinner.h
 /opt/ros/noetic/include/ros/static_assert.h
 /opt/ros/noetic/include/ros/steady_timer.h
 /opt/ros/noetic/include/ros/steady_timer_options.h
 /opt/ros/noetic/include/ros/subscribe_options.h
 /opt/ros/noetic/include/ros/subscriber.h
 /opt/ros/noetic/include/ros/subscription_callback_helper.h
 /opt/ros/noetic/include/ros/this_node.h
 /opt/ros/noetic/include/ros/time.h
 /opt/ros/noetic/include/ros/timer.h
 /opt/ros/noetic/include/ros/timer_options.h
 /opt/ros/noetic/include/ros/topic.h
 /opt/ros/noetic/include/ros/transport_hints.h
 /opt/ros/noetic/include/ros/types.h
 /opt/ros/noetic/include/ros/wall_timer.h
 /opt/ros/noetic/include/ros/wall_timer_options.h
 /opt/ros/noetic/include/rosconsole/macros_generated.h
 /opt/ros/noetic/include/std_msgs/Bool.h
 /opt/ros/noetic/include/std_msgs/Empty.h
 /opt/ros/noetic/include/std_msgs/Header.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcDecl.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcValue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QEvent
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QList
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMargins
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QMetaType
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QObject
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QPointF
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QRect
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QScopedPointer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSharedDataPointer
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSize
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QSizeF
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QStringList
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QVariant
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QtCore
 /usr/include/x86_64-linux-gnu/qt5/QtCore/QtCoreDepends
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstracteventdispatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractitemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractnativeeventfilter.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractproxymodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstractstate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qabstracttransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qanimationgroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydataops.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qarraydatapointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_bootstrap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_cxx11.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qatomic_msvc.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasicatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbasictimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbitarray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbuffer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraylist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qbytearraymatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborarray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborcommon.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcbormap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcborvalue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qchar.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcollator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcommandlineoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcommandlineparser.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcompilerdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig-bootstrapped.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qconfig.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontainerfwd.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcontiguouscache.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcoreevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qcryptographichash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatastream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdatetime.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdeadlinetimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qdiriterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeasingcurve.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qelapsedtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qendian.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventloop.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qeventtransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qexception.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfactoryinterface.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfiledevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfileselector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfilesystemwatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfinalstate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qflags.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfloat16.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfuture.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfutureinterface.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfuturesynchronizer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qfuturewatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qgenericatomic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qglobalstatic.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhash.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhashfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qhistorystate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qidentityproxymodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiodevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qisenum.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qitemselectionmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsonarray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsondocument.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsonobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qjsonvalue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlibrary.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlibraryinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlinkedlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlocale.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlockfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qlogging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qloggingcategory.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmargins.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmath.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmessageauthenticationcode.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetaobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmetatype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimedatabase.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmimetype.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qmutex.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnamespace.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qnumeric.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobject_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectcleanuphandler.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qobjectdefs_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qoperatingsystemversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpair.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qparallelanimationgroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpauseanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpluginloader.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpoint.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocess.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qprocessordetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qpropertyanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qqueue.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrandom.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qreadwritelock.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrect.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrefcount.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregexp.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qregularexpression.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qresource.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qresultstore.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qrunnable.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsavefile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopedvaluerollback.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qscopeguard.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsemaphore.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsequentialanimationgroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qset.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsettings.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qshareddata.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedmemory.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsharedpointer_impl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsignalmapper.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsignaltransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsize.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsocketnotifier.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsortfilterproxymodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstack.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstandardpaths.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstate.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstatemachine.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstorageinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstring.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringalgorithms.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringbuilder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringlistmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringliteral.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringmatcher.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qstringview.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsysinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemdetection.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qsystemsemaphore.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qt_windows.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcore-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtcoreversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtemporarydir.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtemporaryfile.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextboundaryfinder.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextcodec.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtextstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthread.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadpool.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qthreadstorage.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimeline.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimer.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtimezone.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtranslator.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypeinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qtypetraits.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurl.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qurlquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/quuid.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariant.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvariantanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvarlengtharray.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qvector.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversionnumber.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qversiontagging.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwaitcondition.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qwineventnotifier.h
 /usr/include/x86_64-linux-gnu/qt5/QtCore/qxmlstream.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QImage
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QMatrix3x3
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QMatrix4x4
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QOpenGLContext
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPaintDevice
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QPaintDeviceWindow
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QSurfaceFormat
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QTransform
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QVector3D
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QWindow
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QtGui
 /usr/include/x86_64-linux-gnu/qt5/QtGui/QtGuiDepends
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qabstracttextdocumentlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessible.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessiblebridge.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessibleobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qaccessibleplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbackingstore.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbitmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qbrush.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qclipboard.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcolor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qdesktopservices.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qdrag.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontdatabase.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontinfo.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qfontmetrics.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qgenericmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qgenericplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qgenericpluginfactory.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qglyphrun.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qguiapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qiconengine.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qiconengineplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimage.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimageiohandler.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimagereader.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qimagewriter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qinputmethod.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qkeysequence.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmatrix4x4.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qmovie.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qoffscreensurface.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengl.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglbuffer.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglcontext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengldebug.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengles2ext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglextrafunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglframebufferobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglpixeltransferoptions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglshaderprogram.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengltexture.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengltextureblitter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopengltimerquery.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglversionfunctions.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglvertexarrayobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qopenglwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpagedpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpagelayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpagesize.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintdevicewindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpaintengine.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpainterpath.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpalette.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpdfwriter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpicture.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpictureformatplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixelformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmap.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpixmapcache.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qpolygon.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qquaternion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrasterwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrawfont.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qregion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgb.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qrgba64.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qscreen.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsessionmanager.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qstandarditemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qstatictext.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qstylehints.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsurface.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsurfaceformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qsyntaxhighlighter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextcursor.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocument.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocumentfragment.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextdocumentwriter.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextformat.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextlist.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextobject.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtextoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtexttable.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtgui-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtguiversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtouchdevice.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qtransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvalidator.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector2d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector3d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qvector4d.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs.h
 /usr/include/x86_64-linux-gnu/qt5/QtGui/qwindowdefs_win.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QCommonStyle
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QDialog
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QLayout
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QScrollerProperties
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QWidget
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QtWidgets
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/QtWidgetsDepends
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractitemview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qabstractspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qaccessiblewidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qaction.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qactiongroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qapplication.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qboxlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qbuttongroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcalendarwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcheckbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcolordialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcolormap.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcolumnview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcommandlinkbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcommonstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qcompleter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdatawidgetmapper.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdatetimeedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdesktopwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdial.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdialogbuttonbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdirmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdockwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qdrawutil.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qerrormessage.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfiledialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfileiconprovider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfilesystemmodel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfocusframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfontcombobox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qfontdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qformlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qframe.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgesture.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgesturerecognizer.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsanchorlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicseffect.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsitemanimation.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicslayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicslayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicslinearlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsproxywidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsscene.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicssceneevent.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicstransform.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicsview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgraphicswidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgridlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qgroupbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qheaderview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qinputdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qitemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qitemeditorfactory.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qkeyeventtransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qkeysequenceedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlabel.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlayoutitem.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlcdnumber.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlineedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qlistwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmainwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmdiarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmdisubwindow.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenu.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmenubar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmessagebox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qmouseeventtransition.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qopenglwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qplaintextedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qprogressdialog.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qproxystyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qpushbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qradiobutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qrubberband.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollarea.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscroller.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qscrollerproperties.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qshortcut.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizegrip.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsizepolicy.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qslider.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qspinbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplashscreen.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsplitter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedlayout.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstackedwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstatusbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyle.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleditemdelegate.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstylefactory.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleoption.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstylepainter.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qstyleplugin.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qsystemtrayicon.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtableview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtablewidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtabwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextbrowser.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtextedit.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtoolbar.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtoolbox.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtoolbutton.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtooltip.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtreeview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtreewidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtreewidgetitemiterator.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgets-config.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsglobal.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qtwidgetsversion.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qundogroup.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qundostack.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qundoview.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwhatsthis.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidget.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwidgetaction.h
 /usr/include/x86_64-linux-gnu/qt5/QtWidgets/qwizard.h
uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o
 /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/droneObjectRos.h
 /home/<USER>/lxy_ws/src/uav_simulator/src/droneObjectRos.cpp
 /opt/ros/noetic/include/geometry_msgs/Twist.h
 /opt/ros/noetic/include/geometry_msgs/TwistStamped.h
 /opt/ros/noetic/include/geometry_msgs/Vector3.h
 /opt/ros/noetic/include/ros/advertise_options.h
 /opt/ros/noetic/include/ros/advertise_service_options.h
 /opt/ros/noetic/include/ros/assert.h
 /opt/ros/noetic/include/ros/builtin_message_traits.h
 /opt/ros/noetic/include/ros/common.h
 /opt/ros/noetic/include/ros/console.h
 /opt/ros/noetic/include/ros/console_backend.h
 /opt/ros/noetic/include/ros/datatypes.h
 /opt/ros/noetic/include/ros/duration.h
 /opt/ros/noetic/include/ros/exception.h
 /opt/ros/noetic/include/ros/exceptions.h
 /opt/ros/noetic/include/ros/forwards.h
 /opt/ros/noetic/include/ros/init.h
 /opt/ros/noetic/include/ros/macros.h
 /opt/ros/noetic/include/ros/master.h
 /opt/ros/noetic/include/ros/message.h
 /opt/ros/noetic/include/ros/message_event.h
 /opt/ros/noetic/include/ros/message_forward.h
 /opt/ros/noetic/include/ros/message_operations.h
 /opt/ros/noetic/include/ros/message_traits.h
 /opt/ros/noetic/include/ros/names.h
 /opt/ros/noetic/include/ros/node_handle.h
 /opt/ros/noetic/include/ros/param.h
 /opt/ros/noetic/include/ros/parameter_adapter.h
 /opt/ros/noetic/include/ros/platform.h
 /opt/ros/noetic/include/ros/publisher.h
 /opt/ros/noetic/include/ros/rate.h
 /opt/ros/noetic/include/ros/ros.h
 /opt/ros/noetic/include/ros/roscpp_serialization_macros.h
 /opt/ros/noetic/include/ros/rostime_decl.h
 /opt/ros/noetic/include/ros/serialization.h
 /opt/ros/noetic/include/ros/serialized_message.h
 /opt/ros/noetic/include/ros/service.h
 /opt/ros/noetic/include/ros/service_callback_helper.h
 /opt/ros/noetic/include/ros/service_client.h
 /opt/ros/noetic/include/ros/service_client_options.h
 /opt/ros/noetic/include/ros/service_server.h
 /opt/ros/noetic/include/ros/service_traits.h
 /opt/ros/noetic/include/ros/single_subscriber_publisher.h
 /opt/ros/noetic/include/ros/spinner.h
 /opt/ros/noetic/include/ros/static_assert.h
 /opt/ros/noetic/include/ros/steady_timer.h
 /opt/ros/noetic/include/ros/steady_timer_options.h
 /opt/ros/noetic/include/ros/subscribe_options.h
 /opt/ros/noetic/include/ros/subscriber.h
 /opt/ros/noetic/include/ros/subscription_callback_helper.h
 /opt/ros/noetic/include/ros/this_node.h
 /opt/ros/noetic/include/ros/time.h
 /opt/ros/noetic/include/ros/timer.h
 /opt/ros/noetic/include/ros/timer_options.h
 /opt/ros/noetic/include/ros/topic.h
 /opt/ros/noetic/include/ros/transport_hints.h
 /opt/ros/noetic/include/ros/types.h
 /opt/ros/noetic/include/ros/wall_timer.h
 /opt/ros/noetic/include/ros/wall_timer_options.h
 /opt/ros/noetic/include/rosconsole/macros_generated.h
 /opt/ros/noetic/include/std_msgs/Bool.h
 /opt/ros/noetic/include/std_msgs/Empty.h
 /opt/ros/noetic/include/std_msgs/Header.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcDecl.h
 /opt/ros/noetic/include/xmlrpcpp/XmlRpcValue.h
