# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp" "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o"
  "/home/<USER>/lxy_ws/build/uav_simulator/qrc_droneKeyboard.cpp" "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o"
  "/home/<USER>/lxy_ws/src/uav_simulator/src/DialogKeyboard.cpp" "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o"
  "/home/<USER>/lxy_ws/src/uav_simulator/src/droneKeyboard.cpp" "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o"
  "/home/<USER>/lxy_ws/src/uav_simulator/src/droneObjectRos.cpp" "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "BOOST_TEST_DYN_LINK"
  "LIBBULLET_VERSION=2.88"
  "LIBBULLET_VERSION_GT_282"
  "QT_CORE_LIB"
  "QT_GUI_LIB"
  "QT_NO_DEBUG"
  "QT_WIDGETS_LIB"
  "ROSCONSOLE_BACKEND_LOG4CXX"
  "ROS_BUILD_SHARED_LIBS=1"
  "ROS_PACKAGE_NAME=\"uav_simulator\""
  "qh_QHpointer"
  "vtkRenderingContext2D_AUTOINIT=1(vtkRenderingContextOpenGL2)"
  "vtkRenderingCore_AUTOINIT=3(vtkInteractionStyle,vtkRenderingFreeType,vtkRenderingOpenGL2)"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "uav_simulator/keyboard_control_autogen/include"
  "/home/<USER>/lxy_ws/devel/include"
  "/usr/include/vtk-7.1"
  "/usr/include/freetype2"
  "/home/<USER>/lxy_ws/src/uav_simulator/include"
  "/opt/ros/noetic/include"
  "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"
  "/usr/include/gazebo-11"
  "/usr/include/bullet"
  "/usr/include/simbody"
  "/usr/include/sdformat-9.10"
  "/usr/include/ignition/math6"
  "/usr/include/OGRE"
  "/usr/include/OGRE/Terrain"
  "/usr/include/OGRE/Paging"
  "/usr/include/ignition/transport8"
  "/usr/include/ignition/msgs5"
  "/usr/include/ignition/common3"
  "/usr/include/ignition/fuel_tools4"
  "/usr/include/pcl-1.10"
  "/usr/include/eigen3"
  "/usr/include/ni"
  "/usr/include/openni2"
  "/usr/include/x86_64-linux-gnu/qt5"
  "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"
  "/usr/include/x86_64-linux-gnu/qt5/QtGui"
  "/usr/include/x86_64-linux-gnu/qt5/QtCore"
  "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
