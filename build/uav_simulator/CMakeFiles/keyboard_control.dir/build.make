# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

# Include any dependencies generated for this target.
include uav_simulator/CMakeFiles/keyboard_control.dir/depend.make

# Include the progress variables for this target.
include uav_simulator/CMakeFiles/keyboard_control.dir/progress.make

# Include the compile flags for this target's objects.
include uav_simulator/CMakeFiles/keyboard_control.dir/flags.make

uav_simulator/qrc_droneKeyboard.cpp: /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/droneKeyboard.png
uav_simulator/qrc_droneKeyboard.cpp: uav_simulator/include/uav_simulator/droneKeyboard.qrc.depends
uav_simulator/qrc_droneKeyboard.cpp: /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/droneKeyboard.qrc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating qrc_droneKeyboard.cpp"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/lib/qt5/bin/rcc --name droneKeyboard --output /home/<USER>/lxy_ws/build/uav_simulator/qrc_droneKeyboard.cpp /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/droneKeyboard.qrc

uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp: /home/<USER>/lxy_ws/src/uav_simulator/include/uav_simulator/DialogKeyboard.h
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating include/uav_simulator/moc_DialogKeyboard.cpp"
	cd /home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator && /usr/lib/qt5/bin/moc @/home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp_parameters

uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o: uav_simulator/CMakeFiles/keyboard_control.dir/flags.make
uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o: /home/<USER>/lxy_ws/src/uav_simulator/src/DialogKeyboard.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o -c /home/<USER>/lxy_ws/src/uav_simulator/src/DialogKeyboard.cpp

uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.i"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/src/uav_simulator/src/DialogKeyboard.cpp > CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.i

uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.s"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/src/uav_simulator/src/DialogKeyboard.cpp -o CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.s

uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o: uav_simulator/CMakeFiles/keyboard_control.dir/flags.make
uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o: /home/<USER>/lxy_ws/src/uav_simulator/src/droneObjectRos.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o -c /home/<USER>/lxy_ws/src/uav_simulator/src/droneObjectRos.cpp

uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.i"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/src/uav_simulator/src/droneObjectRos.cpp > CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.i

uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.s"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/src/uav_simulator/src/droneObjectRos.cpp -o CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.s

uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o: uav_simulator/CMakeFiles/keyboard_control.dir/flags.make
uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o: /home/<USER>/lxy_ws/src/uav_simulator/src/droneKeyboard.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o -c /home/<USER>/lxy_ws/src/uav_simulator/src/droneKeyboard.cpp

uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.i"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/src/uav_simulator/src/droneKeyboard.cpp > CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.i

uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.s"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/src/uav_simulator/src/droneKeyboard.cpp -o CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.s

uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o: uav_simulator/CMakeFiles/keyboard_control.dir/flags.make
uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o: uav_simulator/qrc_droneKeyboard.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o -c /home/<USER>/lxy_ws/build/uav_simulator/qrc_droneKeyboard.cpp

uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.i"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/build/uav_simulator/qrc_droneKeyboard.cpp > CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.i

uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.s"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/build/uav_simulator/qrc_droneKeyboard.cpp -o CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.s

uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o: uav_simulator/CMakeFiles/keyboard_control.dir/flags.make
uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o: uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o -c /home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp

uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.i"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp > CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.i

uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.s"
	cd /home/<USER>/lxy_ws/build/uav_simulator && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/build/uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp -o CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.s

# Object files for target keyboard_control
keyboard_control_OBJECTS = \
"CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o" \
"CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o" \
"CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o" \
"CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o" \
"CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o"

# External object files for target keyboard_control
keyboard_control_EXTERNAL_OBJECTS =

/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/build.make
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.12.8
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libtf2_ros.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libactionlib.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libmessage_filters.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libroscpp.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libpthread.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/librosconsole.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/librosconsole_log4cxx.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/librosconsole_backend_interface.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/liblog4cxx.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libxmlrpcpp.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libtf2.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libroscpp_serialization.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/librostime.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /opt/ros/noetic/lib/libcpp_common.so
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.12.8
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.12.8
/home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX executable /home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control"
	cd /home/<USER>/lxy_ws/build/uav_simulator && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/keyboard_control.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
uav_simulator/CMakeFiles/keyboard_control.dir/build: /home/<USER>/lxy_ws/devel/lib/uav_simulator/keyboard_control

.PHONY : uav_simulator/CMakeFiles/keyboard_control.dir/build

uav_simulator/CMakeFiles/keyboard_control.dir/clean:
	cd /home/<USER>/lxy_ws/build/uav_simulator && $(CMAKE_COMMAND) -P CMakeFiles/keyboard_control.dir/cmake_clean.cmake
.PHONY : uav_simulator/CMakeFiles/keyboard_control.dir/clean

uav_simulator/CMakeFiles/keyboard_control.dir/depend: uav_simulator/qrc_droneKeyboard.cpp
uav_simulator/CMakeFiles/keyboard_control.dir/depend: uav_simulator/include/uav_simulator/moc_DialogKeyboard.cpp
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lxy_ws/src /home/<USER>/lxy_ws/src/uav_simulator /home/<USER>/lxy_ws/build /home/<USER>/lxy_ws/build/uav_simulator /home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/keyboard_control.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : uav_simulator/CMakeFiles/keyboard_control.dir/depend

