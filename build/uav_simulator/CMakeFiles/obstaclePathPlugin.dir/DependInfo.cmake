# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/lxy_ws/src/uav_simulator/src/obstaclePathPlugin.cc" "/home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/obstaclePathPlugin.dir/src/obstaclePathPlugin.cc.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "BOOST_ALL_NO_LIB"
  "BOOST_ATOMIC_DYN_LINK"
  "BOOST_DATE_TIME_DYN_LINK"
  "BOOST_FILESYSTEM_DYN_LINK"
  "BOOST_IOSTREAMS_DYN_LINK"
  "BOOST_PROGRAM_OPTIONS_DYN_LINK"
  "BOOST_REGEX_DYN_LINK"
  "BOOST_SYSTEM_DYN_LINK"
  "BOOST_TEST_DYN_LINK"
  "BOOST_THREAD_DYN_LINK"
  "LIBBULLET_VERSION=2.88"
  "LIBBULLET_VERSION_GT_282"
  "ROSCONSOLE_BACKEND_LOG4CXX"
  "ROS_BUILD_SHARED_LIBS=1"
  "ROS_PACKAGE_NAME=\"uav_simulator\""
  "obstaclePathPlugin_EXPORTS"
  "qh_QHpointer"
  "vtkRenderingContext2D_AUTOINIT=1(vtkRenderingContextOpenGL2)"
  "vtkRenderingCore_AUTOINIT=3(vtkInteractionStyle,vtkRenderingFreeType,vtkRenderingOpenGL2)"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "uav_simulator/obstaclePathPlugin_autogen/include"
  "/home/<USER>/lxy_ws/devel/include"
  "/usr/include/vtk-7.1"
  "/usr/include/freetype2"
  "/home/<USER>/lxy_ws/src/uav_simulator/include"
  "/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp"
  "/usr/include/gazebo-11"
  "/usr/include/bullet"
  "/usr/include/sdformat-9.10"
  "/usr/include/OGRE"
  "/usr/include/OGRE/Terrain"
  "/usr/include/OGRE/Paging"
  "/usr/include/pcl-1.10"
  "/usr/include/ni"
  "/usr/include/openni2"
  "/opt/ros/noetic/include"
  "/usr/include/simbody"
  "/usr/include/ignition/math6"
  "/usr/include/ignition/transport8"
  "/usr/include/ignition/msgs5"
  "/usr/include/ignition/common3"
  "/usr/include/ignition/fuel_tools4"
  "/usr/include/eigen3"
  "/usr/include/sdformat-9.10/sdf/.."
  "/usr/include/ignition/cmake2"
  "/usr/include/uuid"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
