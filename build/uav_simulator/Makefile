# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/cmake-gui -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lxy_ws/build/CMakeFiles /home/<USER>/lxy_ws/build/uav_simulator/CMakeFiles/progress.marks
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lxy_ws/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/rule
.PHONY : uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/rule

# Convenience name for target.
quadcopterTFBroadcaster: uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/rule

.PHONY : quadcopterTFBroadcaster

# fast build rule for target.
quadcopterTFBroadcaster/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/build.make uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/build
.PHONY : quadcopterTFBroadcaster/fast

# Convenience name for target.
uav_simulator/CMakeFiles/keyboard_control.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/keyboard_control.dir/rule
.PHONY : uav_simulator/CMakeFiles/keyboard_control.dir/rule

# Convenience name for target.
keyboard_control: uav_simulator/CMakeFiles/keyboard_control.dir/rule

.PHONY : keyboard_control

# fast build rule for target.
keyboard_control/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/build
.PHONY : keyboard_control/fast

# Convenience name for target.
uav_simulator/CMakeFiles/livox_laser.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/livox_laser.dir/rule
.PHONY : uav_simulator/CMakeFiles/livox_laser.dir/rule

# Convenience name for target.
livox_laser: uav_simulator/CMakeFiles/livox_laser.dir/rule

.PHONY : livox_laser

# fast build rule for target.
livox_laser/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser.dir/build.make uav_simulator/CMakeFiles/livox_laser.dir/build
.PHONY : livox_laser/fast

# Convenience name for target.
uav_simulator/CMakeFiles/obstaclePathPlugin.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/obstaclePathPlugin.dir/rule
.PHONY : uav_simulator/CMakeFiles/obstaclePathPlugin.dir/rule

# Convenience name for target.
obstaclePathPlugin: uav_simulator/CMakeFiles/obstaclePathPlugin.dir/rule

.PHONY : obstaclePathPlugin

# fast build rule for target.
obstaclePathPlugin/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/obstaclePathPlugin.dir/build.make uav_simulator/CMakeFiles/obstaclePathPlugin.dir/build
.PHONY : obstaclePathPlugin/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_generate_messages.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_generate_messages.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_generate_messages.dir/rule

# Convenience name for target.
uav_simulator_generate_messages: uav_simulator/CMakeFiles/uav_simulator_generate_messages.dir/rule

.PHONY : uav_simulator_generate_messages

# fast build rule for target.
uav_simulator_generate_messages/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_generate_messages.dir/build.make uav_simulator/CMakeFiles/uav_simulator_generate_messages.dir/build
.PHONY : uav_simulator_generate_messages/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_gennodejs.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_gennodejs.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_gennodejs.dir/rule

# Convenience name for target.
uav_simulator_gennodejs: uav_simulator/CMakeFiles/uav_simulator_gennodejs.dir/rule

.PHONY : uav_simulator_gennodejs

# fast build rule for target.
uav_simulator_gennodejs/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_gennodejs.dir/build.make uav_simulator/CMakeFiles/uav_simulator_gennodejs.dir/build
.PHONY : uav_simulator_gennodejs/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_genpy.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_genpy.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_genpy.dir/rule

# Convenience name for target.
uav_simulator_genpy: uav_simulator/CMakeFiles/uav_simulator_genpy.dir/rule

.PHONY : uav_simulator_genpy

# fast build rule for target.
uav_simulator_genpy/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_genpy.dir/build.make uav_simulator/CMakeFiles/uav_simulator_genpy.dir/build
.PHONY : uav_simulator_genpy/fast

# Convenience name for target.
uav_simulator/CMakeFiles/quadcopterPlugin.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/quadcopterPlugin.dir/rule
.PHONY : uav_simulator/CMakeFiles/quadcopterPlugin.dir/rule

# Convenience name for target.
quadcopterPlugin: uav_simulator/CMakeFiles/quadcopterPlugin.dir/rule

.PHONY : quadcopterPlugin

# fast build rule for target.
quadcopterPlugin/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin.dir/build
.PHONY : quadcopterPlugin/fast

# Convenience name for target.
uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_LivoxCustomMsg.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_LivoxCustomMsg.dir/rule
.PHONY : uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_LivoxCustomMsg.dir/rule

# Convenience name for target.
_uav_simulator_generate_messages_check_deps_LivoxCustomMsg: uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_LivoxCustomMsg.dir/rule

.PHONY : _uav_simulator_generate_messages_check_deps_LivoxCustomMsg

# fast build rule for target.
_uav_simulator_generate_messages_check_deps_LivoxCustomMsg/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_LivoxCustomMsg.dir/build.make uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_LivoxCustomMsg.dir/build
.PHONY : _uav_simulator_generate_messages_check_deps_LivoxCustomMsg/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_generate_messages_cpp.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_generate_messages_cpp.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_generate_messages_cpp.dir/rule

# Convenience name for target.
uav_simulator_generate_messages_cpp: uav_simulator/CMakeFiles/uav_simulator_generate_messages_cpp.dir/rule

.PHONY : uav_simulator_generate_messages_cpp

# fast build rule for target.
uav_simulator_generate_messages_cpp/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_generate_messages_cpp.dir/build.make uav_simulator/CMakeFiles/uav_simulator_generate_messages_cpp.dir/build
.PHONY : uav_simulator_generate_messages_cpp/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_gencpp.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_gencpp.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_gencpp.dir/rule

# Convenience name for target.
uav_simulator_gencpp: uav_simulator/CMakeFiles/uav_simulator_gencpp.dir/rule

.PHONY : uav_simulator_gencpp

# fast build rule for target.
uav_simulator_gencpp/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_gencpp.dir/build.make uav_simulator/CMakeFiles/uav_simulator_gencpp.dir/build
.PHONY : uav_simulator_gencpp/fast

# Convenience name for target.
uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_CustomPoint.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_CustomPoint.dir/rule
.PHONY : uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_CustomPoint.dir/rule

# Convenience name for target.
_uav_simulator_generate_messages_check_deps_CustomPoint: uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_CustomPoint.dir/rule

.PHONY : _uav_simulator_generate_messages_check_deps_CustomPoint

# fast build rule for target.
_uav_simulator_generate_messages_check_deps_CustomPoint/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_CustomPoint.dir/build.make uav_simulator/CMakeFiles/_uav_simulator_generate_messages_check_deps_CustomPoint.dir/build
.PHONY : _uav_simulator_generate_messages_check_deps_CustomPoint/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_generate_messages_eus.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_generate_messages_eus.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_generate_messages_eus.dir/rule

# Convenience name for target.
uav_simulator_generate_messages_eus: uav_simulator/CMakeFiles/uav_simulator_generate_messages_eus.dir/rule

.PHONY : uav_simulator_generate_messages_eus

# fast build rule for target.
uav_simulator_generate_messages_eus/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_generate_messages_eus.dir/build.make uav_simulator/CMakeFiles/uav_simulator_generate_messages_eus.dir/build
.PHONY : uav_simulator_generate_messages_eus/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_genlisp.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_genlisp.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_genlisp.dir/rule

# Convenience name for target.
uav_simulator_genlisp: uav_simulator/CMakeFiles/uav_simulator_genlisp.dir/rule

.PHONY : uav_simulator_genlisp

# fast build rule for target.
uav_simulator_genlisp/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_genlisp.dir/build.make uav_simulator/CMakeFiles/uav_simulator_genlisp.dir/build
.PHONY : uav_simulator_genlisp/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_generate_messages_nodejs.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_generate_messages_nodejs.dir/rule

# Convenience name for target.
uav_simulator_generate_messages_nodejs: uav_simulator/CMakeFiles/uav_simulator_generate_messages_nodejs.dir/rule

.PHONY : uav_simulator_generate_messages_nodejs

# fast build rule for target.
uav_simulator_generate_messages_nodejs/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_generate_messages_nodejs.dir/build.make uav_simulator/CMakeFiles/uav_simulator_generate_messages_nodejs.dir/build
.PHONY : uav_simulator_generate_messages_nodejs/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_geneus.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_geneus.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_geneus.dir/rule

# Convenience name for target.
uav_simulator_geneus: uav_simulator/CMakeFiles/uav_simulator_geneus.dir/rule

.PHONY : uav_simulator_geneus

# fast build rule for target.
uav_simulator_geneus/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_geneus.dir/build.make uav_simulator/CMakeFiles/uav_simulator_geneus.dir/build
.PHONY : uav_simulator_geneus/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_generate_messages_lisp.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_generate_messages_lisp.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_generate_messages_lisp.dir/rule

# Convenience name for target.
uav_simulator_generate_messages_lisp: uav_simulator/CMakeFiles/uav_simulator_generate_messages_lisp.dir/rule

.PHONY : uav_simulator_generate_messages_lisp

# fast build rule for target.
uav_simulator_generate_messages_lisp/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_generate_messages_lisp.dir/build.make uav_simulator/CMakeFiles/uav_simulator_generate_messages_lisp.dir/build
.PHONY : uav_simulator_generate_messages_lisp/fast

# Convenience name for target.
uav_simulator/CMakeFiles/uav_simulator_generate_messages_py.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/uav_simulator_generate_messages_py.dir/rule
.PHONY : uav_simulator/CMakeFiles/uav_simulator_generate_messages_py.dir/rule

# Convenience name for target.
uav_simulator_generate_messages_py: uav_simulator/CMakeFiles/uav_simulator_generate_messages_py.dir/rule

.PHONY : uav_simulator_generate_messages_py

# fast build rule for target.
uav_simulator_generate_messages_py/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/uav_simulator_generate_messages_py.dir/build.make uav_simulator/CMakeFiles/uav_simulator_generate_messages_py.dir/build
.PHONY : uav_simulator_generate_messages_py/fast

# Convenience name for target.
uav_simulator/CMakeFiles/quadcopterTFBroadcaster_autogen.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/quadcopterTFBroadcaster_autogen.dir/rule
.PHONY : uav_simulator/CMakeFiles/quadcopterTFBroadcaster_autogen.dir/rule

# Convenience name for target.
quadcopterTFBroadcaster_autogen: uav_simulator/CMakeFiles/quadcopterTFBroadcaster_autogen.dir/rule

.PHONY : quadcopterTFBroadcaster_autogen

# fast build rule for target.
quadcopterTFBroadcaster_autogen/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterTFBroadcaster_autogen.dir/build.make uav_simulator/CMakeFiles/quadcopterTFBroadcaster_autogen.dir/build
.PHONY : quadcopterTFBroadcaster_autogen/fast

# Convenience name for target.
uav_simulator/CMakeFiles/keyboard_control_autogen.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/keyboard_control_autogen.dir/rule
.PHONY : uav_simulator/CMakeFiles/keyboard_control_autogen.dir/rule

# Convenience name for target.
keyboard_control_autogen: uav_simulator/CMakeFiles/keyboard_control_autogen.dir/rule

.PHONY : keyboard_control_autogen

# fast build rule for target.
keyboard_control_autogen/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control_autogen.dir/build.make uav_simulator/CMakeFiles/keyboard_control_autogen.dir/build
.PHONY : keyboard_control_autogen/fast

# Convenience name for target.
uav_simulator/CMakeFiles/livox_laser_autogen.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/livox_laser_autogen.dir/rule
.PHONY : uav_simulator/CMakeFiles/livox_laser_autogen.dir/rule

# Convenience name for target.
livox_laser_autogen: uav_simulator/CMakeFiles/livox_laser_autogen.dir/rule

.PHONY : livox_laser_autogen

# fast build rule for target.
livox_laser_autogen/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser_autogen.dir/build.make uav_simulator/CMakeFiles/livox_laser_autogen.dir/build
.PHONY : livox_laser_autogen/fast

# Convenience name for target.
uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/rule
.PHONY : uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/rule

# Convenience name for target.
obstaclePathPlugin_autogen: uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/rule

.PHONY : obstaclePathPlugin_autogen

# fast build rule for target.
obstaclePathPlugin_autogen/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/build.make uav_simulator/CMakeFiles/obstaclePathPlugin_autogen.dir/build
.PHONY : obstaclePathPlugin_autogen/fast

# Convenience name for target.
uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/rule
.PHONY : uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/rule

# Convenience name for target.
quadcopterPlugin_autogen: uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/rule

.PHONY : quadcopterPlugin_autogen

# fast build rule for target.
quadcopterPlugin_autogen/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin_autogen.dir/build
.PHONY : quadcopterPlugin_autogen/fast

include/uav_simulator/moc_DialogKeyboard.o: include/uav_simulator/moc_DialogKeyboard.cpp.o

.PHONY : include/uav_simulator/moc_DialogKeyboard.o

# target to build an object file
include/uav_simulator/moc_DialogKeyboard.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.o
.PHONY : include/uav_simulator/moc_DialogKeyboard.cpp.o

include/uav_simulator/moc_DialogKeyboard.i: include/uav_simulator/moc_DialogKeyboard.cpp.i

.PHONY : include/uav_simulator/moc_DialogKeyboard.i

# target to preprocess a source file
include/uav_simulator/moc_DialogKeyboard.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.i
.PHONY : include/uav_simulator/moc_DialogKeyboard.cpp.i

include/uav_simulator/moc_DialogKeyboard.s: include/uav_simulator/moc_DialogKeyboard.cpp.s

.PHONY : include/uav_simulator/moc_DialogKeyboard.s

# target to generate assembly for a file
include/uav_simulator/moc_DialogKeyboard.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/include/uav_simulator/moc_DialogKeyboard.cpp.s
.PHONY : include/uav_simulator/moc_DialogKeyboard.cpp.s

qrc_droneKeyboard.o: qrc_droneKeyboard.cpp.o

.PHONY : qrc_droneKeyboard.o

# target to build an object file
qrc_droneKeyboard.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.o
.PHONY : qrc_droneKeyboard.cpp.o

qrc_droneKeyboard.i: qrc_droneKeyboard.cpp.i

.PHONY : qrc_droneKeyboard.i

# target to preprocess a source file
qrc_droneKeyboard.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.i
.PHONY : qrc_droneKeyboard.cpp.i

qrc_droneKeyboard.s: qrc_droneKeyboard.cpp.s

.PHONY : qrc_droneKeyboard.s

# target to generate assembly for a file
qrc_droneKeyboard.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/qrc_droneKeyboard.cpp.s
.PHONY : qrc_droneKeyboard.cpp.s

src/DialogKeyboard.o: src/DialogKeyboard.cpp.o

.PHONY : src/DialogKeyboard.o

# target to build an object file
src/DialogKeyboard.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.o
.PHONY : src/DialogKeyboard.cpp.o

src/DialogKeyboard.i: src/DialogKeyboard.cpp.i

.PHONY : src/DialogKeyboard.i

# target to preprocess a source file
src/DialogKeyboard.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.i
.PHONY : src/DialogKeyboard.cpp.i

src/DialogKeyboard.s: src/DialogKeyboard.cpp.s

.PHONY : src/DialogKeyboard.s

# target to generate assembly for a file
src/DialogKeyboard.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/DialogKeyboard.cpp.s
.PHONY : src/DialogKeyboard.cpp.s

src/droneKeyboard.o: src/droneKeyboard.cpp.o

.PHONY : src/droneKeyboard.o

# target to build an object file
src/droneKeyboard.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.o
.PHONY : src/droneKeyboard.cpp.o

src/droneKeyboard.i: src/droneKeyboard.cpp.i

.PHONY : src/droneKeyboard.i

# target to preprocess a source file
src/droneKeyboard.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.i
.PHONY : src/droneKeyboard.cpp.i

src/droneKeyboard.s: src/droneKeyboard.cpp.s

.PHONY : src/droneKeyboard.s

# target to generate assembly for a file
src/droneKeyboard.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/droneKeyboard.cpp.s
.PHONY : src/droneKeyboard.cpp.s

src/droneObjectRos.o: src/droneObjectRos.cpp.o

.PHONY : src/droneObjectRos.o

# target to build an object file
src/droneObjectRos.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.o
.PHONY : src/droneObjectRos.cpp.o

src/droneObjectRos.i: src/droneObjectRos.cpp.i

.PHONY : src/droneObjectRos.i

# target to preprocess a source file
src/droneObjectRos.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.i
.PHONY : src/droneObjectRos.cpp.i

src/droneObjectRos.s: src/droneObjectRos.cpp.s

.PHONY : src/droneObjectRos.s

# target to generate assembly for a file
src/droneObjectRos.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/keyboard_control.dir/build.make uav_simulator/CMakeFiles/keyboard_control.dir/src/droneObjectRos.cpp.s
.PHONY : src/droneObjectRos.cpp.s

src/livox_lidar/livox_ode_multiray_shape.o: src/livox_lidar/livox_ode_multiray_shape.cpp.o

.PHONY : src/livox_lidar/livox_ode_multiray_shape.o

# target to build an object file
src/livox_lidar/livox_ode_multiray_shape.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser.dir/build.make uav_simulator/CMakeFiles/livox_laser.dir/src/livox_lidar/livox_ode_multiray_shape.cpp.o
.PHONY : src/livox_lidar/livox_ode_multiray_shape.cpp.o

src/livox_lidar/livox_ode_multiray_shape.i: src/livox_lidar/livox_ode_multiray_shape.cpp.i

.PHONY : src/livox_lidar/livox_ode_multiray_shape.i

# target to preprocess a source file
src/livox_lidar/livox_ode_multiray_shape.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser.dir/build.make uav_simulator/CMakeFiles/livox_laser.dir/src/livox_lidar/livox_ode_multiray_shape.cpp.i
.PHONY : src/livox_lidar/livox_ode_multiray_shape.cpp.i

src/livox_lidar/livox_ode_multiray_shape.s: src/livox_lidar/livox_ode_multiray_shape.cpp.s

.PHONY : src/livox_lidar/livox_ode_multiray_shape.s

# target to generate assembly for a file
src/livox_lidar/livox_ode_multiray_shape.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser.dir/build.make uav_simulator/CMakeFiles/livox_laser.dir/src/livox_lidar/livox_ode_multiray_shape.cpp.s
.PHONY : src/livox_lidar/livox_ode_multiray_shape.cpp.s

src/livox_lidar/livox_points_plugin.o: src/livox_lidar/livox_points_plugin.cpp.o

.PHONY : src/livox_lidar/livox_points_plugin.o

# target to build an object file
src/livox_lidar/livox_points_plugin.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser.dir/build.make uav_simulator/CMakeFiles/livox_laser.dir/src/livox_lidar/livox_points_plugin.cpp.o
.PHONY : src/livox_lidar/livox_points_plugin.cpp.o

src/livox_lidar/livox_points_plugin.i: src/livox_lidar/livox_points_plugin.cpp.i

.PHONY : src/livox_lidar/livox_points_plugin.i

# target to preprocess a source file
src/livox_lidar/livox_points_plugin.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser.dir/build.make uav_simulator/CMakeFiles/livox_laser.dir/src/livox_lidar/livox_points_plugin.cpp.i
.PHONY : src/livox_lidar/livox_points_plugin.cpp.i

src/livox_lidar/livox_points_plugin.s: src/livox_lidar/livox_points_plugin.cpp.s

.PHONY : src/livox_lidar/livox_points_plugin.s

# target to generate assembly for a file
src/livox_lidar/livox_points_plugin.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/livox_laser.dir/build.make uav_simulator/CMakeFiles/livox_laser.dir/src/livox_lidar/livox_points_plugin.cpp.s
.PHONY : src/livox_lidar/livox_points_plugin.cpp.s

src/obstaclePathPlugin.o: src/obstaclePathPlugin.cc.o

.PHONY : src/obstaclePathPlugin.o

# target to build an object file
src/obstaclePathPlugin.cc.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/obstaclePathPlugin.dir/build.make uav_simulator/CMakeFiles/obstaclePathPlugin.dir/src/obstaclePathPlugin.cc.o
.PHONY : src/obstaclePathPlugin.cc.o

src/obstaclePathPlugin.i: src/obstaclePathPlugin.cc.i

.PHONY : src/obstaclePathPlugin.i

# target to preprocess a source file
src/obstaclePathPlugin.cc.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/obstaclePathPlugin.dir/build.make uav_simulator/CMakeFiles/obstaclePathPlugin.dir/src/obstaclePathPlugin.cc.i
.PHONY : src/obstaclePathPlugin.cc.i

src/obstaclePathPlugin.s: src/obstaclePathPlugin.cc.s

.PHONY : src/obstaclePathPlugin.s

# target to generate assembly for a file
src/obstaclePathPlugin.cc.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/obstaclePathPlugin.dir/build.make uav_simulator/CMakeFiles/obstaclePathPlugin.dir/src/obstaclePathPlugin.cc.s
.PHONY : src/obstaclePathPlugin.cc.s

src/pidController.o: src/pidController.cpp.o

.PHONY : src/pidController.o

# target to build an object file
src/pidController.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin.dir/src/pidController.cpp.o
.PHONY : src/pidController.cpp.o

src/pidController.i: src/pidController.cpp.i

.PHONY : src/pidController.i

# target to preprocess a source file
src/pidController.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin.dir/src/pidController.cpp.i
.PHONY : src/pidController.cpp.i

src/pidController.s: src/pidController.cpp.s

.PHONY : src/pidController.s

# target to generate assembly for a file
src/pidController.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin.dir/src/pidController.cpp.s
.PHONY : src/pidController.cpp.s

src/quadcopterPlugin.o: src/quadcopterPlugin.cpp.o

.PHONY : src/quadcopterPlugin.o

# target to build an object file
src/quadcopterPlugin.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin.dir/src/quadcopterPlugin.cpp.o
.PHONY : src/quadcopterPlugin.cpp.o

src/quadcopterPlugin.i: src/quadcopterPlugin.cpp.i

.PHONY : src/quadcopterPlugin.i

# target to preprocess a source file
src/quadcopterPlugin.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin.dir/src/quadcopterPlugin.cpp.i
.PHONY : src/quadcopterPlugin.cpp.i

src/quadcopterPlugin.s: src/quadcopterPlugin.cpp.s

.PHONY : src/quadcopterPlugin.s

# target to generate assembly for a file
src/quadcopterPlugin.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterPlugin.dir/build.make uav_simulator/CMakeFiles/quadcopterPlugin.dir/src/quadcopterPlugin.cpp.s
.PHONY : src/quadcopterPlugin.cpp.s

src/quadcopterTFBroadcaster.o: src/quadcopterTFBroadcaster.cpp.o

.PHONY : src/quadcopterTFBroadcaster.o

# target to build an object file
src/quadcopterTFBroadcaster.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/build.make uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/src/quadcopterTFBroadcaster.cpp.o
.PHONY : src/quadcopterTFBroadcaster.cpp.o

src/quadcopterTFBroadcaster.i: src/quadcopterTFBroadcaster.cpp.i

.PHONY : src/quadcopterTFBroadcaster.i

# target to preprocess a source file
src/quadcopterTFBroadcaster.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/build.make uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/src/quadcopterTFBroadcaster.cpp.i
.PHONY : src/quadcopterTFBroadcaster.cpp.i

src/quadcopterTFBroadcaster.s: src/quadcopterTFBroadcaster.cpp.s

.PHONY : src/quadcopterTFBroadcaster.s

# target to generate assembly for a file
src/quadcopterTFBroadcaster.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/build.make uav_simulator/CMakeFiles/quadcopterTFBroadcaster.dir/src/quadcopterTFBroadcaster.cpp.s
.PHONY : src/quadcopterTFBroadcaster.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... install/strip"
	@echo "... quadcopterTFBroadcaster"
	@echo "... keyboard_control"
	@echo "... livox_laser"
	@echo "... obstaclePathPlugin"
	@echo "... uav_simulator_generate_messages"
	@echo "... uav_simulator_gennodejs"
	@echo "... uav_simulator_genpy"
	@echo "... quadcopterPlugin"
	@echo "... _uav_simulator_generate_messages_check_deps_LivoxCustomMsg"
	@echo "... uav_simulator_generate_messages_cpp"
	@echo "... uav_simulator_gencpp"
	@echo "... _uav_simulator_generate_messages_check_deps_CustomPoint"
	@echo "... uav_simulator_generate_messages_eus"
	@echo "... uav_simulator_genlisp"
	@echo "... install/local"
	@echo "... uav_simulator_generate_messages_nodejs"
	@echo "... uav_simulator_geneus"
	@echo "... install"
	@echo "... uav_simulator_generate_messages_lisp"
	@echo "... uav_simulator_generate_messages_py"
	@echo "... quadcopterTFBroadcaster_autogen"
	@echo "... keyboard_control_autogen"
	@echo "... livox_laser_autogen"
	@echo "... obstaclePathPlugin_autogen"
	@echo "... quadcopterPlugin_autogen"
	@echo "... include/uav_simulator/moc_DialogKeyboard.o"
	@echo "... include/uav_simulator/moc_DialogKeyboard.i"
	@echo "... include/uav_simulator/moc_DialogKeyboard.s"
	@echo "... qrc_droneKeyboard.o"
	@echo "... qrc_droneKeyboard.i"
	@echo "... qrc_droneKeyboard.s"
	@echo "... src/DialogKeyboard.o"
	@echo "... src/DialogKeyboard.i"
	@echo "... src/DialogKeyboard.s"
	@echo "... src/droneKeyboard.o"
	@echo "... src/droneKeyboard.i"
	@echo "... src/droneKeyboard.s"
	@echo "... src/droneObjectRos.o"
	@echo "... src/droneObjectRos.i"
	@echo "... src/droneObjectRos.s"
	@echo "... src/livox_lidar/livox_ode_multiray_shape.o"
	@echo "... src/livox_lidar/livox_ode_multiray_shape.i"
	@echo "... src/livox_lidar/livox_ode_multiray_shape.s"
	@echo "... src/livox_lidar/livox_points_plugin.o"
	@echo "... src/livox_lidar/livox_points_plugin.i"
	@echo "... src/livox_lidar/livox_points_plugin.s"
	@echo "... src/obstaclePathPlugin.o"
	@echo "... src/obstaclePathPlugin.i"
	@echo "... src/obstaclePathPlugin.s"
	@echo "... src/pidController.o"
	@echo "... src/pidController.i"
	@echo "... src/pidController.s"
	@echo "... src/quadcopterPlugin.o"
	@echo "... src/quadcopterPlugin.i"
	@echo "... src/quadcopterPlugin.s"
	@echo "... src/quadcopterTFBroadcaster.o"
	@echo "... src/quadcopterTFBroadcaster.i"
	@echo "... src/quadcopterTFBroadcaster.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

