# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/lxy_ws/src/uav_simulator/msg/CustomPoint.msg;/home/<USER>/lxy_ws/src/uav_simulator/msg/LivoxCustomMsg.msg"
services_str = ""
pkg_name = "uav_simulator"
dependencies_str = "std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "uav_simulator;/home/<USER>/lxy_ws/src/uav_simulator/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
