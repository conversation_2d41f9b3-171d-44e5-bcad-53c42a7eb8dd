# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/cmake-gui -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lxy_ws/build/CMakeFiles /home/<USER>/lxy_ws/build/GCOPTER/gcopter/CMakeFiles/progress.marks
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lxy_ws/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_cpp.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_cpp.dir/rule
.PHONY : GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_cpp.dir/rule

# Convenience name for target.
visualization_msgs_generate_messages_cpp: GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_cpp.dir/rule

.PHONY : visualization_msgs_generate_messages_cpp

# fast build rule for target.
visualization_msgs_generate_messages_cpp/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_cpp.dir/build.make GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_cpp.dir/build
.PHONY : visualization_msgs_generate_messages_cpp/fast

# Convenience name for target.
GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_eus.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_eus.dir/rule
.PHONY : GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_eus.dir/rule

# Convenience name for target.
visualization_msgs_generate_messages_eus: GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_eus.dir/rule

.PHONY : visualization_msgs_generate_messages_eus

# fast build rule for target.
visualization_msgs_generate_messages_eus/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_eus.dir/build.make GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_eus.dir/build
.PHONY : visualization_msgs_generate_messages_eus/fast

# Convenience name for target.
GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_nodejs.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_nodejs.dir/rule
.PHONY : GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_nodejs.dir/rule

# Convenience name for target.
visualization_msgs_generate_messages_nodejs: GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_nodejs.dir/rule

.PHONY : visualization_msgs_generate_messages_nodejs

# fast build rule for target.
visualization_msgs_generate_messages_nodejs/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_nodejs.dir/build.make GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_nodejs.dir/build
.PHONY : visualization_msgs_generate_messages_nodejs/fast

# Convenience name for target.
GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_py.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_py.dir/rule
.PHONY : GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_py.dir/rule

# Convenience name for target.
visualization_msgs_generate_messages_py: GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_py.dir/rule

.PHONY : visualization_msgs_generate_messages_py

# fast build rule for target.
visualization_msgs_generate_messages_py/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_py.dir/build.make GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_py.dir/build
.PHONY : visualization_msgs_generate_messages_py/fast

# Convenience name for target.
GCOPTER/gcopter/CMakeFiles/global_planning.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/CMakeFiles/global_planning.dir/rule
.PHONY : GCOPTER/gcopter/CMakeFiles/global_planning.dir/rule

# Convenience name for target.
global_planning: GCOPTER/gcopter/CMakeFiles/global_planning.dir/rule

.PHONY : global_planning

# fast build rule for target.
global_planning/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/global_planning.dir/build.make GCOPTER/gcopter/CMakeFiles/global_planning.dir/build
.PHONY : global_planning/fast

# Convenience name for target.
GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_lisp.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_lisp.dir/rule
.PHONY : GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_lisp.dir/rule

# Convenience name for target.
visualization_msgs_generate_messages_lisp: GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_lisp.dir/rule

.PHONY : visualization_msgs_generate_messages_lisp

# fast build rule for target.
visualization_msgs_generate_messages_lisp/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_lisp.dir/build.make GCOPTER/gcopter/CMakeFiles/visualization_msgs_generate_messages_lisp.dir/build
.PHONY : visualization_msgs_generate_messages_lisp/fast

# Convenience name for target.
GCOPTER/gcopter/CMakeFiles/gcopter.dir/rule:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f CMakeFiles/Makefile2 GCOPTER/gcopter/CMakeFiles/gcopter.dir/rule
.PHONY : GCOPTER/gcopter/CMakeFiles/gcopter.dir/rule

# Convenience name for target.
gcopter: GCOPTER/gcopter/CMakeFiles/gcopter.dir/rule

.PHONY : gcopter

# fast build rule for target.
gcopter/fast:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/gcopter.dir/build.make GCOPTER/gcopter/CMakeFiles/gcopter.dir/build
.PHONY : gcopter/fast

src/global_planning.o: src/global_planning.cpp.o

.PHONY : src/global_planning.o

# target to build an object file
src/global_planning.cpp.o:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/global_planning.dir/build.make GCOPTER/gcopter/CMakeFiles/global_planning.dir/src/global_planning.cpp.o
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/gcopter.dir/build.make GCOPTER/gcopter/CMakeFiles/gcopter.dir/src/global_planning.cpp.o
.PHONY : src/global_planning.cpp.o

src/global_planning.i: src/global_planning.cpp.i

.PHONY : src/global_planning.i

# target to preprocess a source file
src/global_planning.cpp.i:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/global_planning.dir/build.make GCOPTER/gcopter/CMakeFiles/global_planning.dir/src/global_planning.cpp.i
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/gcopter.dir/build.make GCOPTER/gcopter/CMakeFiles/gcopter.dir/src/global_planning.cpp.i
.PHONY : src/global_planning.cpp.i

src/global_planning.s: src/global_planning.cpp.s

.PHONY : src/global_planning.s

# target to generate assembly for a file
src/global_planning.cpp.s:
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/global_planning.dir/build.make GCOPTER/gcopter/CMakeFiles/global_planning.dir/src/global_planning.cpp.s
	cd /home/<USER>/lxy_ws/build && $(MAKE) -f GCOPTER/gcopter/CMakeFiles/gcopter.dir/build.make GCOPTER/gcopter/CMakeFiles/gcopter.dir/src/global_planning.cpp.s
.PHONY : src/global_planning.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... visualization_msgs_generate_messages_cpp"
	@echo "... visualization_msgs_generate_messages_eus"
	@echo "... visualization_msgs_generate_messages_nodejs"
	@echo "... install/local"
	@echo "... visualization_msgs_generate_messages_py"
	@echo "... global_planning"
	@echo "... visualization_msgs_generate_messages_lisp"
	@echo "... gcopter"
	@echo "... test"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... list_install_components"
	@echo "... install"
	@echo "... src/global_planning.o"
	@echo "... src/global_planning.i"
	@echo "... src/global_planning.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

