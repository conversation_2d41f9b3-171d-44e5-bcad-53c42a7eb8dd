# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/lxy_ws/src/GCOPTER/gcopter/include;/usr/include;/usr/include/eigen3".split(';') if "/home/<USER>/lxy_ws/src/GCOPTER/gcopter/include;/usr/include;/usr/include/eigen3" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lgcopter;/usr/lib/x86_64-linux-gnu/libompl.so".split(';') if "-lgcopter;/usr/lib/x86_64-linux-gnu/libompl.so" != "" else []
PROJECT_NAME = "gcopter"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/devel"
PROJECT_VERSION = "0.1.0"
