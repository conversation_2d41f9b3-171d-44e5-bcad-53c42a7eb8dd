set(_CATKIN_CURRENT_PACKAGE "gcopter")
set(gcopter_VERSION "0.1.0")
set(gcopter_MAINTAINER "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>")
set(gcopter_PACKAGE_FORMAT "1")
set(gcopter_BUILD_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs")
set(gcopter_BUILD_EXPORT_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs")
set(gcopter_BUILDTOOL_DEPENDS "catkin")
set(gcopter_BUILDTOOL_EXPORT_DEPENDS )
set(gcopter_EXEC_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs")
set(gcopter_RUN_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "visualization_msgs")
set(gcopter_TEST_DEPENDS )
set(gcopter_DOC_DEPENDS )
set(gcopter_URL_WEBSITE "")
set(gcopter_URL_BUGTRACKER "")
set(gcopter_URL_REPOSITORY "")
set(gcopter_DEPRECATED "")