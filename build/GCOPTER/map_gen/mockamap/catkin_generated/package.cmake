set(_CATKIN_CURRENT_PACKAGE "mockamap")
set(mockamap_VERSION "0.1.0")
set(mockamap_MAINTAINER "<PERSON><PERSON><PERSON> <<PERSON>.<PERSON>@todo.todo>")
set(mockamap_PACKAGE_FORMAT "1")
set(mockamap_BUILD_DEPENDS "roscpp" "pcl_ros" "pcl_conversions")
set(mockamap_BUILD_EXPORT_DEPENDS "roscpp" "pcl_ros" "pcl_conversions")
set(mockamap_BUILDTOOL_DEPENDS "catkin")
set(mockamap_BUILDTOOL_EXPORT_DEPENDS )
set(mockamap_EXEC_DEPENDS "roscpp" "pcl_ros" "pcl_conversions")
set(mockamap_RUN_DEPENDS "roscpp" "pcl_ros" "pcl_conversions")
set(mockamap_TEST_DEPENDS )
set(mockamap_DOC_DEPENDS )
set(mockamap_URL_WEBSITE "")
set(mockamap_URL_BUGTRACKER "")
set(mockamap_URL_REPOSITORY "")
set(mockamap_DEPRECATED "")