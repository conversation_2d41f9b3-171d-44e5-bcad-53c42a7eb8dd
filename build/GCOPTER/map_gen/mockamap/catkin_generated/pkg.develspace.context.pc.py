# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/lxy_ws/src/GCOPTER/map_gen/mockamap/include".split(';') if "/home/<USER>/lxy_ws/src/GCOPTER/map_gen/mockamap/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;pcl_ros;pcl_conversions".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "mockamap"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/devel"
PROJECT_VERSION = "0.1.0"
