set(_CATKIN_CURRENT_PACKAGE "onboard_detector")
set(onboard_detector_VERSION "1.0.0")
set(onboard_detector_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(onboard_detector_PACKAGE_FORMAT "2")
set(onboard_detector_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "cv_bridge" "message_filters" "image_transport" "vision_msgs" "message_generation")
set(onboard_detector_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs")
set(onboard_detector_BUILDTOOL_DEPENDS "catkin")
set(onboard_detector_BUILDTOOL_EXPORT_DEPENDS )
set(onboard_detector_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "message_runtime" "geometry_msgs" "message_generation")
set(onboard_detector_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "message_runtime" "geometry_msgs" "message_generation")
set(onboard_detector_TEST_DEPENDS )
set(onboard_detector_DOC_DEPENDS )
set(onboard_detector_URL_WEBSITE "")
set(onboard_detector_URL_BUGTRACKER "")
set(onboard_detector_URL_REPOSITORY "")
set(onboard_detector_DEPRECATED "")