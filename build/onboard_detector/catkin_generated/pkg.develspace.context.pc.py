# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/lxy_ws/devel/include;/home/<USER>/lxy_ws/src/onboard_detector/include".split(';') if "/home/<USER>/lxy_ws/devel/include;/home/<USER>/lxy_ws/src/onboard_detector/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;rospy;std_msgs;message_generation;geometry_msgs".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lonboard_detector".split(';') if "-lonboard_detector" != "" else []
PROJECT_NAME = "onboard_detector"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/devel"
PROJECT_VERSION = "1.0.0"
