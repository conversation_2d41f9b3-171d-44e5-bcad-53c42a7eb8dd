#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export LD_LIBRARY_PATH='/home/<USER>/lxy_ws/devel/lib:/home/<USER>/catkin_ws/devel/lib:/opt/ros/noetic/lib:/usr/local/cuda-12.1/lib64:/home/<USER>/PX4_Firmware/build/px4_sitl_default/build_gazebo'
export PATH='/opt/ros/noetic/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'
export PWD='/home/<USER>/lxy_ws/build'
export ROS_PACKAGE_PATH='/home/<USER>/lxy_ws/src:/home/<USER>/catkin_ws/src:/opt/ros/noetic/share'