# generated from catkin/cmake/template/order_packages.context.py.in
source_root_dir = '/home/<USER>/lxy_ws/src'
whitelisted_packages = ''.split(';') if '' != '' else []
blacklisted_packages = ''.split(';') if '' != '' else []
underlay_workspaces = '/home/<USER>/lxy_ws/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic'.split(';') if '/home/<USER>/lxy_ws/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic' != '' else []
