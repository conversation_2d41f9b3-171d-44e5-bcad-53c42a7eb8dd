# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

# Include any dependencies generated for this target.
include trajectory_planner/CMakeFiles/mpc_solver_setup.dir/depend.make

# Include the progress variables for this target.
include trajectory_planner/CMakeFiles/mpc_solver_setup.dir/progress.make

# Include the compile flags for this target's objects.
include trajectory_planner/CMakeFiles/mpc_solver_setup.dir/flags.make

trajectory_planner/CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.o: trajectory_planner/CMakeFiles/mpc_solver_setup.dir/flags.make
trajectory_planner/CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.o: /home/<USER>/lxy_ws/src/trajectory_planner/src/mpc_solver_setup.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object trajectory_planner/CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.o"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.o -c /home/<USER>/lxy_ws/src/trajectory_planner/src/mpc_solver_setup.cpp

trajectory_planner/CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.i"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/src/trajectory_planner/src/mpc_solver_setup.cpp > CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.i

trajectory_planner/CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.s"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/src/trajectory_planner/src/mpc_solver_setup.cpp -o CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.s

# Object files for target mpc_solver_setup
mpc_solver_setup_OBJECTS = \
"CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.o"

# External object files for target mpc_solver_setup
mpc_solver_setup_EXTERNAL_OBJECTS =

/home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup: trajectory_planner/CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.o
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup: trajectory_planner/CMakeFiles/mpc_solver_setup.dir/build.make
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt/libnlopt.so.1.0.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/ACADO/libacado_toolkit_s.so.1.2.2beta
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup: trajectory_planner/CMakeFiles/mpc_solver_setup.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable /home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mpc_solver_setup.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
trajectory_planner/CMakeFiles/mpc_solver_setup.dir/build: /home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup

.PHONY : trajectory_planner/CMakeFiles/mpc_solver_setup.dir/build

trajectory_planner/CMakeFiles/mpc_solver_setup.dir/clean:
	cd /home/<USER>/lxy_ws/build/trajectory_planner && $(CMAKE_COMMAND) -P CMakeFiles/mpc_solver_setup.dir/cmake_clean.cmake
.PHONY : trajectory_planner/CMakeFiles/mpc_solver_setup.dir/clean

trajectory_planner/CMakeFiles/mpc_solver_setup.dir/depend:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lxy_ws/src /home/<USER>/lxy_ws/src/trajectory_planner /home/<USER>/lxy_ws/build /home/<USER>/lxy_ws/build/trajectory_planner /home/<USER>/lxy_ws/build/trajectory_planner/CMakeFiles/mpc_solver_setup.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : trajectory_planner/CMakeFiles/mpc_solver_setup.dir/depend

