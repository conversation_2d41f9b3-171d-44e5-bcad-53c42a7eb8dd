# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lxy_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lxy_ws/build

# Include any dependencies generated for this target.
include trajectory_planner/CMakeFiles/bspline_node.dir/depend.make

# Include the progress variables for this target.
include trajectory_planner/CMakeFiles/bspline_node.dir/progress.make

# Include the compile flags for this target's objects.
include trajectory_planner/CMakeFiles/bspline_node.dir/flags.make

trajectory_planner/CMakeFiles/bspline_node.dir/src/bspline_node.cpp.o: trajectory_planner/CMakeFiles/bspline_node.dir/flags.make
trajectory_planner/CMakeFiles/bspline_node.dir/src/bspline_node.cpp.o: /home/<USER>/lxy_ws/src/trajectory_planner/src/bspline_node.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object trajectory_planner/CMakeFiles/bspline_node.dir/src/bspline_node.cpp.o"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/bspline_node.dir/src/bspline_node.cpp.o -c /home/<USER>/lxy_ws/src/trajectory_planner/src/bspline_node.cpp

trajectory_planner/CMakeFiles/bspline_node.dir/src/bspline_node.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bspline_node.dir/src/bspline_node.cpp.i"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/lxy_ws/src/trajectory_planner/src/bspline_node.cpp > CMakeFiles/bspline_node.dir/src/bspline_node.cpp.i

trajectory_planner/CMakeFiles/bspline_node.dir/src/bspline_node.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bspline_node.dir/src/bspline_node.cpp.s"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/lxy_ws/src/trajectory_planner/src/bspline_node.cpp -o CMakeFiles/bspline_node.dir/src/bspline_node.cpp.s

# Object files for target bspline_node
bspline_node_OBJECTS = \
"CMakeFiles/bspline_node.dir/src/bspline_node.cpp.o"

# External object files for target bspline_node
bspline_node_EXTERNAL_OBJECTS =

/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: trajectory_planner/CMakeFiles/bspline_node.dir/src/bspline_node.cpp.o
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: trajectory_planner/CMakeFiles/bspline_node.dir/build.make
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libroslib.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librospack.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpython3.8.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libtinyxml2.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/liboctomap_ros.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/liboctomap.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/liboctomath.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libtf.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libtf2_ros.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libactionlib.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libmessage_filters.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libtf2.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_common.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_octree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_search.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_filters.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_io.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_features.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_ml.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_surface.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_registration.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_apps.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_people.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_system.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_date_time.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_iostreams.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_regex.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libqhull.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/libOpenNI.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/libOpenNI2.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkChartsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtksys-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMath-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMisc-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonSystem-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInfovisCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersStatistics-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingFourier-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkalglib-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libfreetype.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libz.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOImage-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkDICOMParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkmetaio-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libjpeg.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpng.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libtiff.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingVolume-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXML-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXMLParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libexpat.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOLegacy-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOPLY-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContextOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libflann_cpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libcv_bridge.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libroscpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpthread.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librosconsole.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librosconsole_log4cxx.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librosconsole_backend_interface.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/liblog4cxx.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libxmlrpcpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libroscpp_serialization.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librostime.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libcpp_common.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/devel/lib/libtrajectory_planner.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/devel/lib/libglobal_planner.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/liboctomap_ros.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/liboctomap.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/liboctomath.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libtf.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libtf2_ros.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libactionlib.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libtf2.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/devel/lib/libdynamic_predictor.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/devel/lib/libmap_manager.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_apps.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_surface.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_registration.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_people.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_features.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_filters.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_ml.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_search.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_io.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_octree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_common.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkChartsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInfovisCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOLegacy-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOPLY-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersStatistics-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingFourier-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkalglib-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingVolume-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXML-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXMLParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContextOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOImage-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMisc-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMath-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonSystem-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtksys-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkDICOMParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkmetaio-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libflann_cpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_common.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_octree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_search.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_filters.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_io.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_features.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_ml.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_surface.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_registration.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_apps.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_people.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkChartsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtksys-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMath-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMisc-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonSystem-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInfovisCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersStatistics-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingFourier-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkalglib-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOImage-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkDICOMParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkmetaio-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingVolume-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXML-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXMLParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOLegacy-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOPLY-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContextOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libflann_cpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/devel/lib/libonboard_detector.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libcv_bridge.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libimage_transport.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libmessage_filters.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libclass_loader.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libPocoFoundation.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libdl.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libroslib.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librospack.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpython3.8.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libtinyxml2.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_system.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_date_time.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_iostreams.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_regex.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libqhull.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/libOpenNI.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/libOpenNI2.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libjpeg.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpng.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libtiff.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libexpat.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_apps.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_surface.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_registration.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_people.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_features.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_filters.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_ml.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_search.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_io.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_octree.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpcl_common.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkChartsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInfovisCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOLegacy-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOPLY-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkViewsCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersStatistics-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingFourier-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkalglib-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingHybrid-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingVolume-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXML-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOXMLParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContextOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libfreetype.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkImagingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkRenderingCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonColor-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersSources-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkFiltersCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkIOImage-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMisc-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonMath-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonSystem-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkCommonCore-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtksys-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkDICOMParser-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libvtkmetaio-7.1.so.7.1p.1
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libz.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libGLEW.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libSM.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libICE.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libX11.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libXext.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libXt.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libflann_cpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libroscpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libpthread.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librosconsole.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librosconsole_log4cxx.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librosconsole_backend_interface.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/liblog4cxx.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libxmlrpcpp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libroscpp_serialization.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/librostime.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /opt/ros/noetic/lib/libcpp_common.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt/libnlopt.so.1.0.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/lib/x86/libOsqpEigen.so.0.7.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/lib/x86/libosqp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/lib/x86/libOsqpEigen.so.0.7.0
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/lib/x86/libosqp.so
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/ACADO/libacado_toolkit_s.so.1.2.2beta
/home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node: trajectory_planner/CMakeFiles/bspline_node.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/lxy_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable /home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node"
	cd /home/<USER>/lxy_ws/build/trajectory_planner && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bspline_node.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
trajectory_planner/CMakeFiles/bspline_node.dir/build: /home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node

.PHONY : trajectory_planner/CMakeFiles/bspline_node.dir/build

trajectory_planner/CMakeFiles/bspline_node.dir/clean:
	cd /home/<USER>/lxy_ws/build/trajectory_planner && $(CMAKE_COMMAND) -P CMakeFiles/bspline_node.dir/cmake_clean.cmake
.PHONY : trajectory_planner/CMakeFiles/bspline_node.dir/clean

trajectory_planner/CMakeFiles/bspline_node.dir/depend:
	cd /home/<USER>/lxy_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lxy_ws/src /home/<USER>/lxy_ws/src/trajectory_planner /home/<USER>/lxy_ws/build /home/<USER>/lxy_ws/build/trajectory_planner /home/<USER>/lxy_ws/build/trajectory_planner/CMakeFiles/bspline_node.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : trajectory_planner/CMakeFiles/bspline_node.dir/depend

