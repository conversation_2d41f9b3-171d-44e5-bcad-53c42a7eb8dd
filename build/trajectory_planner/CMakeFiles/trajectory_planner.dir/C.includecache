#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_auxiliary_functions.c
acado_auxiliary_functions.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_auxiliary_functions.h
stdio.h
-

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_auxiliary_functions.h
acado_common.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_common.h
Windows.h
-
unistd.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/unistd.h
mach/mach_time.h
-
time.h
-
sys/stat.h
-
sys/time.h
-

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_common.h
math.h
-
string.h
-
acado_qpoases_interface.hpp
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_qpoases_interface.hpp

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_integrator.c
acado_common.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_common.h

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_qpoases_interface.hpp
stdio.h
-
math.h
-

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_solver.c
acado_common.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_common.h

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_solver_sfunction.c
string.h
-
acado_solver_sfunction.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_solver_sfunction.h
acado_auxiliary_functions.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_auxiliary_functions.h

/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_solver_sfunction.h
acado_common.h
/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/acado_common.h

