# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile CX<PERSON> with /usr/bin/c++
CXX_FLAGS = -std=c++17  -O3 -Wall -O3 -DNDEBUG   -w

CXX_DEFINES = -DDISABLE_LIBUSB_1_0 -DDISABLE_PCAP -DDISABLE_PNG -DLINUX -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"trajectory_planner\"

CXX_INCLUDES = -I/home/<USER>/lxy_ws/src/trajectory_planner/./third -I/usr/lib -I/home/<USER>/lxy_ws/devel/include -I/home/<USER>/lxy_ws/src/global_planner/include -I/home/<USER>/lxy_ws/src/map_manager/include -I/home/<USER>/lxy_ws/src/onboard_detector/include -I/home/<USER>/lxy_ws/src/dynamic_predictor/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/home/<USER>/lxy_ws/src/trajectory_planner/src -I/usr/local/lib -I/home/<USER>/lxy_ws/src/trajectory_planner/include -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt/src -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt/src/api -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt/src/util -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt/src/octave -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/ACADO/acado -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/ACADO -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/qpoases -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/qpoases/INCLUDE -I/home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/mpc_solver/qpoases/SRC -isystem /usr/include/eigen3 -isystem /usr/include/pcl-1.10 -isystem /usr/include/vtk-7.1 -isystem /usr/include/freetype2 -isystem /usr/include/ni -isystem /usr/include/openni2 -isystem /usr/include/opencv4 

