set(_CATKIN_CURRENT_PACKAGE "trajectory_planner")
set(trajectory_planner_VERSION "1.0.0")
set(trajectory_planner_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(trajectory_planner_PACKAGE_FORMAT "2")
set(trajectory_planner_BUILD_DEPENDS "roscpp" "rospy" "roslib" "tf" "std_msgs" "global_planner" "map_manager" "dynamic_predictor" "octomap_ros")
set(trajectory_planner_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "roslib" "tf" "std_msgs")
set(trajectory_planner_BUILDTOOL_DEPENDS "catkin")
set(trajectory_planner_BUILDTOOL_EXPORT_DEPENDS )
set(trajectory_planner_EXEC_DEPENDS "roscpp" "rospy" "roslib" "tf" "std_msgs")
set(trajectory_planner_RUN_DEPENDS "roscpp" "rospy" "roslib" "tf" "std_msgs")
set(trajectory_planner_TEST_DEPENDS )
set(trajectory_planner_DOC_DEPENDS )
set(trajectory_planner_URL_WEBSITE "")
set(trajectory_planner_URL_BUGTRACKER "")
set(trajectory_planner_URL_REPOSITORY "")
set(trajectory_planner_DEPRECATED "")