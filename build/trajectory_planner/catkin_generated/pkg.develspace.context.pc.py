# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/lxy_ws/src/trajectory_planner/include".split(';') if "/home/<USER>/lxy_ws/src/trajectory_planner/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;rospy;std_msgs".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-ltrajectory_planner".split(';') if "-ltrajectory_planner" != "" else []
PROJECT_NAME = "trajectory_planner"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/devel"
PROJECT_VERSION = "1.0.0"
