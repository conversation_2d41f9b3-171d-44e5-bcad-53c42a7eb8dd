# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "${prefix}/include".split(';') if "${prefix}/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;rospy;std_msgs".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lglobal_planner".split(';') if "-lglobal_planner" != "" else []
PROJECT_NAME = "global_planner"
PROJECT_SPACE_DIR = "/home/<USER>/lxy_ws/install"
PROJECT_VERSION = "1.0.0"
