set(_CATKIN_CURRENT_PACKAGE "global_planner")
set(global_planner_VERSION "1.0.0")
set(global_planner_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(global_planner_PACKAGE_FORMAT "2")
set(global_planner_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "octomap_ros" "map_manager")
set(global_planner_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs")
set(global_planner_BUILDTOOL_DEPENDS "catkin")
set(global_planner_BUILDTOOL_EXPORT_DEPENDS )
set(global_planner_EXEC_DEPENDS "roscpp" "rospy" "std_msgs")
set(global_planner_RUN_DEPENDS "roscpp" "rospy" "std_msgs")
set(global_planner_TEST_DEPENDS )
set(global_planner_DOC_DEPENDS )
set(global_planner_URL_WEBSITE "")
set(global_planner_URL_BUGTRACKER "")
set(global_planner_URL_REPOSITORY "")
set(global_planner_DEPRECATED "")