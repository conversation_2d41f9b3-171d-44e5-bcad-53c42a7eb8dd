set(_CATKIN_CURRENT_PACKAGE "dynamic_predictor")
set(dynamic_predictor_VERSION "1.0.0")
set(dynamic_predictor_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(dynamic_predictor_PACKAGE_FORMAT "2")
set(dynamic_predictor_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_generation" "onboard_detector" "map_manager")
set(dynamic_predictor_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "onboard_detector" "map_manager")
set(dynamic_predictor_BUILDTOOL_DEPENDS "catkin")
set(dynamic_predictor_BUILDTOOL_EXPORT_DEPENDS )
set(dynamic_predictor_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_runtime" "onboard_detector" "map_manager")
set(dynamic_predictor_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_runtime" "onboard_detector" "map_manager")
set(dynamic_predictor_TEST_DEPENDS )
set(dynamic_predictor_DOC_DEPENDS )
set(dynamic_predictor_URL_WEBSITE "")
set(dynamic_predictor_URL_BUGTRACKER "")
set(dynamic_predictor_URL_REPOSITORY "")
set(dynamic_predictor_DEPRECATED "")