set(_CATKIN_CURRENT_PACKAGE "map_manager")
set(map_manager_VERSION "1.0.0")
set(map_manager_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(map_manager_PACKAGE_FORMAT "2")
set(map_manager_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "message_generation" "cv_bridge" "image_transport" "onboard_detector")
set(map_manager_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs")
set(map_manager_BUILDTOOL_DEPENDS "catkin")
set(map_manager_BUILDTOOL_EXPORT_DEPENDS )
set(map_manager_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "message_runtime" "cv_bridge" "onboard_detector")
set(map_manager_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "message_runtime" "cv_bridge" "onboard_detector")
set(map_manager_TEST_DEPENDS )
set(map_manager_DOC_DEPENDS )
set(map_manager_URL_WEBSITE "")
set(map_manager_URL_BUGTRACKER "")
set(map_manager_URL_REPOSITORY "")
set(map_manager_DEPRECATED "")