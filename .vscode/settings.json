{"python.autoComplete.extraPaths": ["/home/<USER>/catkin_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "python.analysis.extraPaths": ["/home/<USER>/catkin_ws/devel/lib/python3/dist-packages", "/opt/ros/noetic/lib/python3/dist-packages"], "files.associations": {"chrono": "cpp", "random": "cpp", "ostream": "cpp", "vector": "cpp", "tuple": "cpp", "iostream": "cpp", "limits": "cpp", "functional": "cpp", "complex": "cpp", "*.tcc": "cpp", "deque": "cpp", "list": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "hash_map": "cpp", "hash_set": "cpp", "*.ipp": "cpp", "array": "cpp", "string_view": "cpp", "initializer_list": "cpp", "bitset": "cpp", "algorithm": "cpp", "numeric": "cpp", "cmath": "cpp", "memory": "cpp", "future": "cpp", "istream": "cpp", "utility": "cpp", "variant": "cpp", "regex": "cpp", "cctype": "cpp", "clocale": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "atomic": "cpp", "strstream": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "cuchar": "cpp", "forward_list": "cpp", "exception": "cpp", "filesystem": "cpp", "iterator": "cpp", "map": "cpp", "memory_resource": "cpp", "optional": "cpp", "ratio": "cpp", "set": "cpp", "system_error": "cpp", "type_traits": "cpp", "rope": "cpp", "slist": "cpp", "fstream": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "mutex": "cpp", "new": "cpp", "scoped_allocator": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "bit": "cpp", "charconv": "cpp", "netfwd": "cpp", "core": "cpp"}, "cmake.sourceDirectory": "/home/<USER>/lxy_ws/src/planner", "C_Cpp.errorSquiggles": "disabled"}