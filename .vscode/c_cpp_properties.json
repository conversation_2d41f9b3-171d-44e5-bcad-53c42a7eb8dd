{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/opt/ros/noetic/include/**", "/home/<USER>/catkin_ws/devel/include/**", "/home/<USER>/catkin_ws/src/gazebo_ros_pkgs/gazebo_plugins/include/**", "/home/<USER>/catkin_ws/src/gazebo_ros_pkgs/gazebo_ros/include/**", "/home/<USER>/catkin_ws/src/gazebo_ros_pkgs/gazebo_ros_control/include/**", "/usr/include/**", "/usr/include/c++/9/**", "/usr/include/x86_64-linux-gnu/c++/9/**", "/usr/include/c++/9/backward/**", "/usr/lib/gcc/x86_64-linux-gnu/9/include/**", "/usr/local/include/**", "/usr/include/x86_64-linux-gnu/**", "/home/<USER>/lxy_ws/src/planner/modules/**", "/home/<USER>/lxy_ws/src/planner/third/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}