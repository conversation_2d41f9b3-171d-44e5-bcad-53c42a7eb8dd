[35m[1mScanning dependencies of target geometry_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target global_planning[0m
[35m[1mScanning dependencies of target visualization_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target visualization_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target visualization_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target visualization_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target visualization_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_eus[0m
[  0%] Built target visualization_msgs_generate_messages_eus
[  0%] Built target visualization_msgs_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target visualization_msgs_generate_messages_cpp
[  0%] Built target visualization_msgs_generate_messages_nodejs
[  0%] Built target rosgraph_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target sensor_msgs_generate_messages_py
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target sensor_msgs_generate_messages_lisp
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target visualization_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_eus
[ 25%] [32mBuilding CXX object CMakeFiles/global_planning.dir/src/global_planning.cpp.o[0m
[ 25%] Built target roscpp_generate_messages_eus
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_py[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_cpp[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_lisp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_cpp[0m
[ 25%] Built target sensor_msgs_generate_messages_nodejs
[ 25%] Built target roscpp_generate_messages_py
[ 25%] Built target sensor_msgs_generate_messages_eus
[ 25%] Built target std_msgs_generate_messages_lisp
[ 25%] Built target roscpp_generate_messages_cpp
[ 25%] Built target rosgraph_msgs_generate_messages_py
[ 25%] Built target std_msgs_generate_messages_py
[ 25%] Built target rosgraph_msgs_generate_messages_nodejs
[ 25%] Built target rosgraph_msgs_generate_messages_lisp
[ 25%] Built target sensor_msgs_generate_messages_cpp
[ 25%] Built target roscpp_generate_messages_lisp
[ 25%] Built target std_msgs_generate_messages_nodejs
[ 25%] Built target std_msgs_generate_messages_cpp
[35m[1mScanning dependencies of target gcopter[0m
[ 50%] [32mBuilding CXX object CMakeFiles/gcopter.dir/src/global_planning.cpp.o[0m
[ 75%] [32m[1mLinking CXX executable global_planning[0m
[ 75%] Built target global_planning
[100%] [32m[1mLinking CXX shared library /home/<USER>/lxy_ws/devel/.private/gcopter/lib/libgcopter.so[0m
[100%] Built target gcopter
