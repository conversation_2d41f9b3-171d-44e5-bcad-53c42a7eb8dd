[  0%] Built target visualization_msgs_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[ 25%] Built target std_msgs_generate_messages_eus
[ 50%] Built target global_planning
[ 50%] Built target geometry_msgs_generate_messages_lisp
[ 50%] Built target geometry_msgs_generate_messages_nodejs
[ 50%] Built target visualization_msgs_generate_messages_nodejs
[ 50%] Built target roscpp_generate_messages_eus
[ 50%] Built target visualization_msgs_generate_messages_eus
[ 50%] Built target visualization_msgs_generate_messages_cpp
[ 50%] Built target visualization_msgs_generate_messages_lisp
[ 50%] Built target rosgraph_msgs_generate_messages_eus
[ 50%] Built target geometry_msgs_generate_messages_py
[ 50%] Built target sensor_msgs_generate_messages_lisp
[ 50%] Built target sensor_msgs_generate_messages_py
[ 50%] Built target roscpp_generate_messages_nodejs
[ 50%] Built target geometry_msgs_generate_messages_cpp
[ 50%] Built target roscpp_generate_messages_py
[ 50%] Built target sensor_msgs_generate_messages_nodejs
[ 50%] Built target rosgraph_msgs_generate_messages_py
[ 50%] Built target roscpp_generate_messages_cpp
[ 50%] Built target std_msgs_generate_messages_lisp
[ 50%] Built target sensor_msgs_generate_messages_eus
[ 50%] Built target roscpp_generate_messages_lisp
[ 50%] Built target rosgraph_msgs_generate_messages_nodejs
[ 50%] Built target std_msgs_generate_messages_nodejs
[ 50%] Built target rosgraph_msgs_generate_messages_lisp
[ 50%] Built target sensor_msgs_generate_messages_cpp
[ 50%] Built target std_msgs_generate_messages_cpp
[ 50%] Built target std_msgs_generate_messages_py
[100%] Built target gcopter
