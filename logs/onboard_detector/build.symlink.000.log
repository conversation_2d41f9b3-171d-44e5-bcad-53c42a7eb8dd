Symlinking /home/<USER>/lxy_ws/devel/lib/libonboard_detector.so
Symlinking /home/<USER>/lxy_ws/devel/lib/onboard_detector/fake_detector_node
Symlinking /home/<USER>/lxy_ws/devel/lib/onboard_detector/detector_node
Symlinking /home/<USER>/lxy_ws/devel/lib/pkgconfig/onboard_detector.pc
Symlinking /home/<USER>/lxy_ws/devel/lib/python3/dist-packages/onboard_detector/__init__.py
Symlinking /home/<USER>/lxy_ws/devel/lib/python3/dist-packages/onboard_detector/srv/__init__.py
Symlinking /home/<USER>/lxy_ws/devel/lib/python3/dist-packages/onboard_detector/srv/_GetDynamicObstacles.py
Symlinking /home/<USER>/lxy_ws/devel/include/onboard_detector/GetDynamicObstaclesRequest.h
Symlinking /home/<USER>/lxy_ws/devel/include/onboard_detector/GetDynamicObstaclesResponse.h
Symlinking /home/<USER>/lxy_ws/devel/include/onboard_detector/GetDynamicObstacles.h
Symlinking /home/<USER>/lxy_ws/devel/share/gennodejs/ros/onboard_detector/_index.js
Symlinking /home/<USER>/lxy_ws/devel/share/gennodejs/ros/onboard_detector/srv/GetDynamicObstacles.js
Symlinking /home/<USER>/lxy_ws/devel/share/gennodejs/ros/onboard_detector/srv/_index.js
Symlinking /home/<USER>/lxy_ws/devel/share/roseus/ros/onboard_detector/manifest.l
Symlinking /home/<USER>/lxy_ws/devel/share/roseus/ros/onboard_detector/srv/GetDynamicObstacles.l
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/onboard_detector/srv/GetDynamicObstacles.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/onboard_detector/srv/onboard_detector-srv.asd
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/onboard_detector/srv/_package.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/onboard_detector/srv/_package_GetDynamicObstacles.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/onboard_detector/cmake/onboard_detector-msg-paths.cmake
Symlinking /home/<USER>/lxy_ws/devel/share/onboard_detector/cmake/onboard_detectorConfig-version.cmake
Symlinking /home/<USER>/lxy_ws/devel/share/onboard_detector/cmake/onboard_detector-msg-extras.cmake
Symlinking /home/<USER>/lxy_ws/devel/share/onboard_detector/cmake/onboard_detectorConfig.cmake
