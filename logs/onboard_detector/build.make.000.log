[35m[1mScanning dependencies of target std_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target _onboard_detector_generate_messages_check_deps_GetDynamicObstacles[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target vision_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target vision_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target vision_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target vision_msgs_generate_messages_nodejs[0m
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target sensor_msgs_generate_messages_py
[  0%] Built target vision_msgs_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target vision_msgs_generate_messages_cpp
[  0%] Built target vision_msgs_generate_messages_nodejs
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target vision_msgs_generate_messages_lisp
[35m[1mScanning dependencies of target std_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_eus[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_cpp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_py[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_lisp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target vision_msgs_generate_messages_eus[0m
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target _onboard_detector_generate_messages_check_deps_GetDynamicObstacles
[  0%] Built target roscpp_generate_messages_cpp
[  0%] Built target roscpp_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_py
[  0%] Built target sensor_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_py
[  0%] Built target roscpp_generate_messages_lisp
[  0%] Built target sensor_msgs_generate_messages_cpp
[  0%] Built target vision_msgs_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_eus
[35m[1mScanning dependencies of target onboard_detector_generate_messages_lisp[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_cpp[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_eus[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_py[0m
[  5%] [34m[1mGenerating Javascript code from onboard_detector/GetDynamicObstacles.srv[0m
[ 17%] [34m[1mGenerating Lisp code from onboard_detector/GetDynamicObstacles.srv[0m
[ 17%] [34m[1mGenerating C++ code from onboard_detector/GetDynamicObstacles.srv[0m
[ 23%] [34m[1mGenerating EusLisp manifest code for onboard_detector[0m
[ 35%] [34m[1mGenerating Python code from SRV onboard_detector/GetDynamicObstacles[0m
[ 35%] [34m[1mGenerating EusLisp code from onboard_detector/GetDynamicObstacles.srv[0m
[ 35%] Built target onboard_detector_generate_messages_lisp
[ 35%] Built target onboard_detector_generate_messages_nodejs
[ 41%] [34m[1mGenerating Python srv __init__.py for onboard_detector[0m
[ 41%] Built target onboard_detector_generate_messages_cpp
[ 41%] Built target onboard_detector_generate_messages_py
[ 41%] Built target onboard_detector_generate_messages_eus
[35m[1mScanning dependencies of target onboard_detector_generate_messages[0m
[35m[1mScanning dependencies of target onboard_detector[0m
[ 41%] Built target onboard_detector_generate_messages
[ 64%] [32mBuilding CXX object CMakeFiles/onboard_detector.dir/include/onboard_detector/fakeDetector.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/onboard_detector.dir/include/onboard_detector/uvDetector.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/onboard_detector.dir/include/onboard_detector/kalmanFilter.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/onboard_detector.dir/include/onboard_detector/dynamicDetector.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/onboard_detector.dir/include/onboard_detector/dbscan.cpp.o[0m
[ 76%] [32m[1mLinking CXX shared library /home/<USER>/lxy_ws/devel/.private/onboard_detector/lib/libonboard_detector.so[0m
[ 76%] Built target onboard_detector
[35m[1mScanning dependencies of target fake_detector_node[0m
[35m[1mScanning dependencies of target detector_node[0m
[ 82%] [32mBuilding CXX object CMakeFiles/fake_detector_node.dir/src/fake_detector_node.cpp.o[0m
[ 88%] [32mBuilding CXX object CMakeFiles/detector_node.dir/src/detector_node.cpp.o[0m
[ 94%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/onboard_detector/lib/onboard_detector/fake_detector_node[0m
[ 94%] Built target fake_detector_node
[100%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/onboard_detector/lib/onboard_detector/detector_node[0m
[100%] Built target detector_node
