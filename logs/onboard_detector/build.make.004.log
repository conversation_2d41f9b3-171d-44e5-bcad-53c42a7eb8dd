[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target vision_msgs_generate_messages_py
[  0%] Built target vision_msgs_generate_messages_nodejs
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_py
[  0%] Built target vision_msgs_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target vision_msgs_generate_messages_cpp
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target _onboard_detector_generate_messages_check_deps_GetDynamicObstacles
[  0%] Built target sensor_msgs_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_py
[  0%] Built target sensor_msgs_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_nodejs
[  0%] Built target roscpp_generate_messages_eus
[  0%] Built target roscpp_generate_messages_lisp
[  0%] Built target vision_msgs_generate_messages_eus
[  0%] Built target roscpp_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_py
[  0%] Built target sensor_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_lisp
[  5%] Built target onboard_detector_generate_messages_lisp
[ 17%] Built target onboard_detector_generate_messages_eus
[ 29%] Built target onboard_detector_generate_messages_cpp
[ 35%] Built target onboard_detector_generate_messages_py
[ 41%] Built target onboard_detector_generate_messages_nodejs
[ 76%] Built target onboard_detector
[ 76%] Built target onboard_detector_generate_messages
[ 88%] Built target fake_detector_node
[100%] Built target detector_node
