[  0%] Built target sensor_msgs_generate_messages_nodejs
[  0%] Built target roscpp_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target roscpp_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_lisp
[  0%] Built target sensor_msgs_generate_messages_eus
[  0%] Built target roscpp_generate_messages_py
[  0%] Built target sensor_msgs_generate_messages_lisp
[  0%] Built target onboard_detector_generate_messages_py
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target roscpp_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_py
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target _map_manager_generate_messages_check_deps_RayCast
[  0%] Built target _map_manager_generate_messages_check_deps_CheckPosCollision
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target onboard_detector_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_eus
[  0%] Built target onboard_detector_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target onboard_detector_generate_messages_nodejs
[  0%] Built target onboard_detector_generate_messages_cpp
[  8%] Built target map_manager_generate_messages_cpp
[ 20%] Built target map_manager_generate_messages_eus
[ 28%] Built target map_manager_generate_messages_nodejs
[ 36%] Built target map_manager_generate_messages_lisp
[ 48%] Built target map_manager_generate_messages_py
[ 48%] Built target map_manager_generate_messages
[ 68%] Built target map_manager
[ 76%] Built target save_map_node
[ 96%] Built target dynamic_map_node
[100%] Built target esdf_map_node
[100%] Built target occupancy_map_node
