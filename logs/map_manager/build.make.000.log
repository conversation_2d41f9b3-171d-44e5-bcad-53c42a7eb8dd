[35m[1mScanning dependencies of target geometry_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_lisp[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_cpp[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target _map_manager_generate_messages_check_deps_CheckPosCollision[0m
[35m[1mScanning dependencies of target _map_manager_generate_messages_check_deps_RayCast[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_eus[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_py[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_py[0m
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target sensor_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_nodejs
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target roscpp_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_lisp
[  0%] Built target sensor_msgs_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target roscpp_generate_messages_py
[35m[1mScanning dependencies of target onboard_detector_generate_messages_py[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target sensor_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target roscpp_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_cpp[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_lisp[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target rosgraph_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target onboard_detector_generate_messages_eus[0m
[  0%] Built target onboard_detector_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target onboard_detector_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_py
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target _map_manager_generate_messages_check_deps_CheckPosCollision
[  0%] Built target onboard_detector_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target _map_manager_generate_messages_check_deps_RayCast
[  0%] Built target onboard_detector_generate_messages_cpp
[  0%] Built target onboard_detector_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_eus
[35m[1mScanning dependencies of target map_manager_generate_messages_cpp[0m
[35m[1mScanning dependencies of target map_manager_generate_messages_py[0m
[35m[1mScanning dependencies of target map_manager_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target map_manager_generate_messages_lisp[0m
[35m[1mScanning dependencies of target map_manager_generate_messages_eus[0m
[  4%] [34m[1mGenerating C++ code from map_manager/RayCast.srv[0m
[  8%] [34m[1mGenerating C++ code from map_manager/CheckPosCollision.srv[0m
[ 12%] [34m[1mGenerating Python code from SRV map_manager/RayCast[0m
[ 16%] [34m[1mGenerating Javascript code from map_manager/CheckPosCollision.srv[0m
[ 24%] [34m[1mGenerating Javascript code from map_manager/RayCast.srv[0m
[ 24%] [34m[1mGenerating Python code from SRV map_manager/CheckPosCollision[0m
[ 28%] [34m[1mGenerating EusLisp code from map_manager/RayCast.srv[0m
[ 32%] [34m[1mGenerating EusLisp manifest code for map_manager[0m
[ 36%] [34m[1mGenerating EusLisp code from map_manager/CheckPosCollision.srv[0m
[ 40%] [34m[1mGenerating Lisp code from map_manager/RayCast.srv[0m
[ 44%] [34m[1mGenerating Lisp code from map_manager/CheckPosCollision.srv[0m
[ 44%] Built target map_manager_generate_messages_lisp
[ 44%] Built target map_manager_generate_messages_nodejs
[ 48%] [34m[1mGenerating Python srv __init__.py for map_manager[0m
[ 48%] Built target map_manager_generate_messages_cpp
[ 48%] Built target map_manager_generate_messages_py
[ 48%] Built target map_manager_generate_messages_eus
[35m[1mScanning dependencies of target map_manager_generate_messages[0m
[35m[1mScanning dependencies of target map_manager[0m
[ 48%] Built target map_manager_generate_messages
[ 52%] [32mBuilding CXX object CMakeFiles/map_manager.dir/include/map_manager/ESDFMap.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/map_manager.dir/include/map_manager/raycast.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/map_manager.dir/include/map_manager/occupancyMap.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/map_manager.dir/include/map_manager/dynamicMap.cpp.o[0m
[ 68%] [32m[1mLinking CXX shared library /home/<USER>/lxy_ws/devel/.private/map_manager/lib/libmap_manager.so[0m
[ 68%] Built target map_manager
[35m[1mScanning dependencies of target esdf_map_node[0m
[35m[1mScanning dependencies of target occupancy_map_node[0m
[35m[1mScanning dependencies of target dynamic_map_node[0m
[35m[1mScanning dependencies of target save_map_node[0m
[ 72%] [32mBuilding CXX object CMakeFiles/save_map_node.dir/src/save_map_node.cpp.o[0m
[ 80%] [32mBuilding CXX object CMakeFiles/occupancy_map_node.dir/src/occupancy_map_node.cpp.o[0m
[ 80%] [32mBuilding CXX object CMakeFiles/dynamic_map_node.dir/src/dynamic_map_node.cpp.o[0m
[ 84%] [32mBuilding CXX object CMakeFiles/esdf_map_node.dir/src/esdf_map_node.cpp.o[0m
[ 88%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/map_manager/lib/map_manager/esdf_map_node[0m
[ 92%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/map_manager/lib/map_manager/occupancy_map_node[0m
[ 96%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/map_manager/lib/map_manager/save_map_node[0m
[ 96%] Built target save_map_node
[ 96%] Built target esdf_map_node
[ 96%] Built target occupancy_map_node
[100%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/map_manager/lib/map_manager/dynamic_map_node[0m
[100%] Built target dynamic_map_node
