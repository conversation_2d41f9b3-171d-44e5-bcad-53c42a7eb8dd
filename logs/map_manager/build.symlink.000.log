Symlinking /home/<USER>/lxy_ws/devel/lib/libmap_manager.so
Symlinking /home/<USER>/lxy_ws/devel/lib/map_manager/save_map_node
Symlinking /home/<USER>/lxy_ws/devel/lib/map_manager/voxel_counter_node.py
Symlinking /home/<USER>/lxy_ws/devel/lib/map_manager/dynamic_map_node
Symlinking /home/<USER>/lxy_ws/devel/lib/map_manager/esdf_map_node
Symlinking /home/<USER>/lxy_ws/devel/lib/map_manager/occupancy_map_node
Symlinking /home/<USER>/lxy_ws/devel/lib/pkgconfig/map_manager.pc
Symlinking /home/<USER>/lxy_ws/devel/lib/python3/dist-packages/map_manager/__init__.py
Symlinking /home/<USER>/lxy_ws/devel/lib/python3/dist-packages/map_manager/srv/_CheckPosCollision.py
Symlinking /home/<USER>/lxy_ws/devel/lib/python3/dist-packages/map_manager/srv/__init__.py
Symlinking /home/<USER>/lxy_ws/devel/lib/python3/dist-packages/map_manager/srv/_RayCast.py
Symlinking /home/<USER>/lxy_ws/devel/include/map_manager/CheckPosCollision.h
Symlinking /home/<USER>/lxy_ws/devel/include/map_manager/CheckPosCollisionRequest.h
Symlinking /home/<USER>/lxy_ws/devel/include/map_manager/CheckPosCollisionResponse.h
Symlinking /home/<USER>/lxy_ws/devel/include/map_manager/RayCastRequest.h
Symlinking /home/<USER>/lxy_ws/devel/include/map_manager/RayCast.h
Symlinking /home/<USER>/lxy_ws/devel/include/map_manager/RayCastResponse.h
Symlinking /home/<USER>/lxy_ws/devel/share/gennodejs/ros/map_manager/_index.js
Symlinking /home/<USER>/lxy_ws/devel/share/gennodejs/ros/map_manager/srv/CheckPosCollision.js
Symlinking /home/<USER>/lxy_ws/devel/share/gennodejs/ros/map_manager/srv/RayCast.js
Symlinking /home/<USER>/lxy_ws/devel/share/gennodejs/ros/map_manager/srv/_index.js
Symlinking /home/<USER>/lxy_ws/devel/share/roseus/ros/map_manager/manifest.l
Symlinking /home/<USER>/lxy_ws/devel/share/roseus/ros/map_manager/srv/CheckPosCollision.l
Symlinking /home/<USER>/lxy_ws/devel/share/roseus/ros/map_manager/srv/RayCast.l
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/map_manager/srv/RayCast.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/map_manager/srv/_package.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/map_manager/srv/map_manager-srv.asd
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/map_manager/srv/_package_RayCast.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/map_manager/srv/CheckPosCollision.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/common-lisp/ros/map_manager/srv/_package_CheckPosCollision.lisp
Symlinking /home/<USER>/lxy_ws/devel/share/map_manager/cmake/map_managerConfig-version.cmake
Symlinking /home/<USER>/lxy_ws/devel/share/map_manager/cmake/map_manager-msg-paths.cmake
Symlinking /home/<USER>/lxy_ws/devel/share/map_manager/cmake/map_managerConfig.cmake
Symlinking /home/<USER>/lxy_ws/devel/share/map_manager/cmake/map_manager-msg-extras.cmake
