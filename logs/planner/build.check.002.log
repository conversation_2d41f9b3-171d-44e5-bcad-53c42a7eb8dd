[36m--[0m Using CATKIN_DEVEL_PREFIX: /home/<USER>/lxy_ws/devel/.private/planner
[36m--[0m Using CMAKE_PREFIX_PATH: /home/<USER>/lxy_ws/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic
[36m--[0m This workspace overlays: /home/<USER>/lxy_ws/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic
[36m--[0m Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3")
[36m--[0m Using PYTHON_EXECUTABLE: /usr/bin/python3
[36m--[0m Using Debian Python package layout
[36m--[0m Using empy: /usr/lib/python3/dist-packages/em.py
[36m--[0m Using CATKIN_ENABLE_TESTING: ON
[36m--[0m Call enable_testing()
[36m--[0m Using CATKIN_TEST_RESULTS_DIR: /home/<USER>/lxy_ws/build/planner/test_results
[36m--[0m Forcing gtest/gmock from source, though one was otherwise available.
[36m--[0m Found gtest sources under '/usr/src/googletest': gtests will be built
[36m--[0m Found gmock sources under '/usr/src/googletest': gmock will be built
[36m--[0m Found PythonInterp: /usr/bin/python3 (found version "3.8.10")
[36m--[0m Using Python nosetests: /usr/bin/nosetests3
[36m--[0m catkin 0.8.12
[36m--[0m BUILD_SHARED_LIBS is on
[36m--[0m Using these message generators: gencpp;geneus;genlisp;gennodejs;genpy
--------------------------------------------------------------------------------------------------------------------------------------------------------------------
/home/<USER>/lxy_ws/devel/.private/tracking_controller/include/home/<USER>/lxy_ws/devel/.private/dynamic_predictor/include/home/<USER>/lxy_ws/devel/.private/map_manager/include/home/<USER>/lxy_ws/devel/.private/onboard_detector/include/home/<USER>/lxy_ws/src/GCOPTER/gcopter/include/home/<USER>/lxy_ws/src/onboard_detector/include/home/<USER>/lxy_ws/src/map_manager/include/home/<USER>/lxy_ws/src/dynamic_predictor/include/home/<USER>/lxy_ws/src/global_planner/include/home/<USER>/lxy_ws/src/tracking_controller/include/home/<USER>/lxy_ws/src/trajectory_planner/include/opt/ros/noetic/include/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp/usr/include/usr/include/pcl-1.10/usr/include/eigen3/usr/include/vtk-7.1/usr/include/freetype2/usr/include/x86_64-linux-gnu/usr/include/ni/usr/include/openni2/usr/include/opencv4
--------------------------------------------------------------------------------------------------------------------------------------------------------------------
/opt/ros/noetic/lib/libroslib.so/opt/ros/noetic/lib/librospack.so/usr/lib/x86_64-linux-gnu/libpython3.8.so/usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0/usr/lib/x86_64-linux-gnu/libtinyxml2.so/opt/ros/noetic/lib/libtf.so/opt/ros/noetic/lib/libtf2_ros.so/opt/ros/noetic/lib/libactionlib.so/opt/ros/noetic/lib/libmessage_filters.so/opt/ros/noetic/lib/libtf2.so/home/<USER>/lxy_ws/devel/.private/tracking_controller/lib/libtracking_controller.so/home/<USER>/lxy_ws/devel/.private/dynamic_predictor/lib/libdynamic_predictor.so/home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/libtrajectory_planner.so/home/<USER>/lxy_ws/devel/.private/map_manager/lib/libmap_manager.so/usr/lib/x86_64-linux-gnu/libpcl_common.so/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so/usr/lib/x86_64-linux-gnu/libpcl_octree.so/usr/lib/x86_64-linux-gnu/libpcl_search.so/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so/usr/lib/x86_64-linux-gnu/libpcl_filters.so/usr/lib/x86_64-linux-gnu/libpcl_io.so/usr/lib/x86_64-linux-gnu/libpcl_features.so/usr/lib/x86_64-linux-gnu/libpcl_ml.so/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so/usr/lib/x86_64-linux-gnu/libpcl_visualization.so/usr/lib/x86_64-linux-gnu/libpcl_surface.so/usr/lib/x86_64-linux-gnu/libpcl_registration.so/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so/usr/lib/x86_64-linux-gnu/libpcl_tracking.so/usr/lib/x86_64-linux-gnu/libpcl_recognition.so/usr/lib/x86_64-linux-gnu/libpcl_stereo.so/usr/lib/x86_64-linux-gnu/libpcl_apps.so/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so/usr/lib/x86_64-linux-gnu/libpcl_people.so/usr/lib/x86_64-linux-gnu/libboost_system.so/usr/lib/x86_64-linux-gnu/libboost_filesystem.so/usr/lib/x86_64-linux-gnu/libboost_date_time.so/usr/lib/x86_64-linux-gnu/libboost_iostreams.so/usr/lib/x86_64-linux-gnu/libboost_regex.sooptimized/usr/lib/x86_64-linux-gnu/libqhull.sodebug/usr/lib/x86_64-linux-gnu/libqhull.so/usr/lib/libOpenNI.so/usr/lib/libOpenNI2.so/usr/lib/x86_64-linux-gnu/libvtkChartsCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonColor-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtksys-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonMath-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonMisc-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonSystem-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkFiltersCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkInfovisCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkFiltersStatistics-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkImagingFourier-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkImagingCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkalglib-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkRenderingCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkFiltersSources-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libfreetype.so/usr/lib/x86_64-linux-gnu/libz.so/usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkImagingSources-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkFiltersHybrid-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkImagingColor-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkImagingGeneral-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkImagingHybrid-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkIOImage-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkDICOMParser-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkmetaio-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libjpeg.so/usr/lib/x86_64-linux-gnu/libpng.so/usr/lib/x86_64-linux-gnu/libtiff.so/usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkRenderingVolume-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkIOXML-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkIOCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkIOXMLParser-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libexpat.so/usr/lib/x86_64-linux-gnu/libvtkIOGeometry-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkIOLegacy-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkIOPLY-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkViewsCore-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkRenderingContextOpenGL2-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-7.1.so.7.1p.1/usr/lib/x86_64-linux-gnu/libflann_cpp.so/opt/ros/noetic/lib/libcv_bridge.so/usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_video.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_face.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_text.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_core.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.2.0/usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.2.0/home/<USER>/lxy_ws/devel/.private/onboard_detector/lib/libonboard_detector.so/home/<USER>/lxy_ws/devel/.private/global_planner/lib/libglobal_planner.so/opt/ros/noetic/lib/libroscpp.so/usr/lib/x86_64-linux-gnu/libpthread.so/usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0/opt/ros/noetic/lib/librosconsole.so/opt/ros/noetic/lib/librosconsole_log4cxx.so/opt/ros/noetic/lib/librosconsole_backend_interface.so/usr/lib/x86_64-linux-gnu/liblog4cxx.so/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0/opt/ros/noetic/lib/libxmlrpcpp.so/opt/ros/noetic/lib/libroscpp_serialization.so/opt/ros/noetic/lib/librostime.so/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0/opt/ros/noetic/lib/libcpp_common.so/usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4/home/<USER>/lxy_ws/devel/.private/gcopter/lib/libgcopter.so/usr/lib/x86_64-linux-gnu/libompl.so
--------------------------------------------------------------------------------------------------------------------------------------------------------------------
[36m--[0m Configuring done
[36m--[0m Generating done
[36m--[0m Build files have been written to: /home/<USER>/lxy_ws/build/planner
