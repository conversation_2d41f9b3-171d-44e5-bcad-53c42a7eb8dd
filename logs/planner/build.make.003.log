[35m[1mScanning dependencies of target parameter_fitting[0m
[35m[1mScanning dependencies of target map_viewer[0m
[35m[1mScanning dependencies of target Mrpt_test[0m
[35m[1mScanning dependencies of target map_generator[0m
[35m[1mScanning dependencies of target planner_px4_circle[0m
[35m[1mScanning dependencies of target planner_px4[0m
[  6%] [32mBuilding CXX object CMakeFiles/map_viewer.dir/modules/map/map.cpp.o[0m
[  6%] [32mBuilding CXX object CMakeFiles/parameter_fitting.dir/src/parameter_fitting.cpp.o[0m
[  9%] [32mBuilding CXX object CMakeFiles/Mrpt_test.dir/modules/MRPT/mrptmodule.cpp.o[0m
[ 12%] [32mBuilding CXX object CMakeFiles/map_viewer.dir/src/map_viewer.cpp.o[0m
[ 15%] [32mBuilding CXX object CMakeFiles/map_viewer.dir/modules/ros_interface/ros_interface.cpp.o[0m
[ 18%] [32mBuilding CXX object CMakeFiles/Mrpt_test.dir/src/Mrpt_test.cpp.o[0m
[ 21%] [32mBuilding CXX object CMakeFiles/map_generator.dir/src/map_generator.cpp.o[0m
[ 27%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/mpcc/nominal_mpcc.cpp.o[0m
[ 27%] [32mBuilding CXX object CMakeFiles/map_generator.dir/modules/ros_interface/ros_interface.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/bspline_opt/bspline_optimizer.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/src/planner_px4_circle_main.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/px4_interface/px4_interface.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/map_generator.dir/modules/map/map.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/map/map.cpp.o[0m
[ 45%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/MRPT/mrptmodule.cpp.o[0m
[ 48%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/parabolic_airdrop/parabolic_airdrop.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/mpcc/nominal_mpcc.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/kinodynamic_astar/kinodynamic_astar.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/planner_px4_circle.dir/modules/ros_interface/ros_interface.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/src/planner_px4_main.cpp.o[0m
In file included from [01m[K/usr/include/python3.8/numpy/ndarraytypes.h:1830[m[K,
                 from [01m[K/usr/include/python3.8/numpy/ndarrayobject.h:12[m[K,
                 from [01m[K/usr/include/python3.8/numpy/arrayobject.h:4[m[K,
                 from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:20[m[K:
[01m[K/usr/include/python3.8/numpy/npy_1_7_deprecated_api.h:17:2:[m[K [01;35m[Kwarning: [m[K#warning "Using deprecated NumPy API, disable it with " "#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION" [[01;35m[K-Wcpp[m[K]
   17 | #[01;35m[Kwarning[m[K "Using deprecated NumPy API, disable it with " \
      |  [01;35m[K^~~~~~~[m[K
In file included from [01m[K/usr/include/python3.8/numpy/ndarraytypes.h:1830[m[K,
                 from [01m[K/usr/include/python3.8/numpy/ndarrayobject.h:12[m[K,
                 from [01m[K/usr/include/python3.8/numpy/arrayobject.h:4[m[K,
                 from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:20[m[K:
[01m[K/usr/include/python3.8/numpy/npy_1_7_deprecated_api.h:17:2:[m[K [01;35m[Kwarning: [m[K#warning "Using deprecated NumPy API, disable it with " "#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION" [[01;35m[K-Wcpp[m[K]
   17 | #[01;35m[Kwarning[m[K "Using deprecated NumPy API, disable it with " \
      |  [01;35m[K^~~~~~~[m[K
[ 63%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/map/map.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/ros_interface/ros_interface.cpp.o[0m
[ 69%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/kinodynamic_astar/kinodynamic_astar.cpp.o[0m
[ 72%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/bspline_opt/bspline_optimizer.cpp.o[0m
In file included from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:19[m[K:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kbool Mrpt::load(const char*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:968:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  968 |       [01;35m[Kfread(&i, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:975:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  975 |       [01;35m[Kfread(&n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:976:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  976 |       [01;35m[Kfread(&depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:977:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  977 |       [01;35m[Kfread(&density, sizeof(float), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:986:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  986 |       [01;35m[Kfread(split_points.data(), sizeof(float), n_array * n_trees, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:992:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  992 |         [01;35m[Kfread(&sz, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:994:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  994 |         [01;35m[Kfread(&leaves[0], sizeof(int), sz, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1001:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1001 |         [01;35m[Kfread(&non_zeros, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1008:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1008 |           [01;35m[Kfread(&row, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1009:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1009 |           [01;35m[Kfread(&col, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1010:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1010 |           [01;35m[Kfread(&val, sizeof(float), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1018:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1018 |         [01;35m[Kfread(dense_random_matrix.data(), sizeof(float), n_pool * dim, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameter_list(FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1623:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1623 |       [01;35m[Kfread(&par_sz, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameters(Mrpt_Parameters*, FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1596:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1596 |       [01;35m[Kfread(&p->n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1597:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1597 |       [01;35m[Kfread(&p->depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1598:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1598 |       [01;35m[Kfread(&p->votes, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1599:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1599 |       [01;35m[Kfread(&p->k, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1600:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1600 |       [01;35m[Kfread(&p->estimated_qtime, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1601:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1601 |       [01;35m[Kfread(&p->estimated_recall, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[ 75%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/px4_interface/px4_interface.cpp.o[0m
In file included from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:19[m[K:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kbool Mrpt::load(const char*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:968:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  968 |       [01;35m[Kfread(&i, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:975:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  975 |       [01;35m[Kfread(&n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:976:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  976 |       [01;35m[Kfread(&depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:977:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  977 |       [01;35m[Kfread(&density, sizeof(float), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:986:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  986 |       [01;35m[Kfread(split_points.data(), sizeof(float), n_array * n_trees, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:992:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  992 |         [01;35m[Kfread(&sz, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:994:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  994 |         [01;35m[Kfread(&leaves[0], sizeof(int), sz, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1001:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1001 |         [01;35m[Kfread(&non_zeros, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1008:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1008 |           [01;35m[Kfread(&row, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1009:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1009 |           [01;35m[Kfread(&col, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1010:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1010 |           [01;35m[Kfread(&val, sizeof(float), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1018:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1018 |         [01;35m[Kfread(dense_random_matrix.data(), sizeof(float), n_pool * dim, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameter_list(FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1623:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1623 |       [01;35m[Kfread(&par_sz, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameters(Mrpt_Parameters*, FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1596:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1596 |       [01;35m[Kfread(&p->n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1597:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1597 |       [01;35m[Kfread(&p->depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1598:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1598 |       [01;35m[Kfread(&p->votes, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1599:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1599 |       [01;35m[Kfread(&p->k, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1600:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1600 |       [01;35m[Kfread(&p->estimated_qtime, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1601:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1601 |       [01;35m[Kfread(&p->estimated_recall, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[ 78%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/MRPT/mrptmodule.cpp.o[0m
In file included from [01m[K/usr/include/python3.8/numpy/ndarraytypes.h:1830[m[K,
                 from [01m[K/usr/include/python3.8/numpy/ndarrayobject.h:12[m[K,
                 from [01m[K/usr/include/python3.8/numpy/arrayobject.h:4[m[K,
                 from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:20[m[K:
[01m[K/usr/include/python3.8/numpy/npy_1_7_deprecated_api.h:17:2:[m[K [01;35m[Kwarning: [m[K#warning "Using deprecated NumPy API, disable it with " "#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION" [[01;35m[K-Wcpp[m[K]
   17 | #[01;35m[Kwarning[m[K "Using deprecated NumPy API, disable it with " \
      |  [01;35m[K^~~~~~~[m[K
[ 81%] [32mBuilding CXX object CMakeFiles/planner_px4.dir/modules/parabolic_airdrop/parabolic_airdrop.cpp.o[0m
In file included from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:19[m[K:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kbool Mrpt::load(const char*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:968:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  968 |       [01;35m[Kfread(&i, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:975:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  975 |       [01;35m[Kfread(&n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:976:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  976 |       [01;35m[Kfread(&depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:977:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  977 |       [01;35m[Kfread(&density, sizeof(float), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:986:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  986 |       [01;35m[Kfread(split_points.data(), sizeof(float), n_array * n_trees, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:992:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  992 |         [01;35m[Kfread(&sz, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:994:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  994 |         [01;35m[Kfread(&leaves[0], sizeof(int), sz, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1001:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1001 |         [01;35m[Kfread(&non_zeros, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1008:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1008 |           [01;35m[Kfread(&row, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1009:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1009 |           [01;35m[Kfread(&col, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1010:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1010 |           [01;35m[Kfread(&val, sizeof(float), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1018:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1018 |         [01;35m[Kfread(dense_random_matrix.data(), sizeof(float), n_pool * dim, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameter_list(FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1623:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1623 |       [01;35m[Kfread(&par_sz, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameters(Mrpt_Parameters*, FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1596:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1596 |       [01;35m[Kfread(&p->n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1597:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1597 |       [01;35m[Kfread(&p->depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1598:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1598 |       [01;35m[Kfread(&p->votes, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1599:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1599 |       [01;35m[Kfread(&p->k, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1600:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1600 |       [01;35m[Kfread(&p->estimated_qtime, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1601:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1601 |       [01;35m[Kfread(&p->estimated_recall, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[ 84%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/planner/lib/planner/Mrpt_test[0m
[ 87%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/planner/lib/planner/parameter_fitting[0m
[ 87%] Built target parameter_fitting
[ 87%] Built target Mrpt_test
[ 90%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/planner/lib/planner/map_viewer[0m
[ 90%] Built target map_viewer
[ 93%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/planner/lib/planner/map_generator[0m
[ 93%] Built target map_generator
[ 96%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/planner/lib/planner/planner_px4_circle[0m
[ 96%] Built target planner_px4_circle
In file included from [01m[K/usr/include/c++/9/map:60[m[K,
                 from [01m[K/opt/ros/noetic/include/ros/package.h:34[m[K,
                 from [01m[K/home/<USER>/lxy_ws/src/planner/src/planner_px4_main.cpp:2[m[K:
/usr/include/c++/9/bits/stl_tree.h: In instantiation of ‘[01m[Kstatic const _Key& std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_S_key(std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_Const_Link_type) [with _Key = Eigen::Matrix<double, 3, 1>; _Val = Eigen::Matrix<double, 3, 1>; _KeyOfValue = std::_Identity<Eigen::Matrix<double, 3, 1> >; _Compare = geo_utils::filterLess; _Alloc = std::allocator<Eigen::Matrix<double, 3, 1> >; std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_Const_Link_type = const std::_Rb_tree_node<Eigen::Matrix<double, 3, 1> >*][m[K’:
[01m[K/usr/include/c++/9/bits/stl_tree.h:1934:36:[m[K   required from ‘[01m[Kstd::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::iterator std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_M_lower_bound(std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_Link_type, std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_Base_ptr, const _Key&) [with _Key = Eigen::Matrix<double, 3, 1>; _Val = Eigen::Matrix<double, 3, 1>; _KeyOfValue = std::_Identity<Eigen::Matrix<double, 3, 1> >; _Compare = geo_utils::filterLess; _Alloc = std::allocator<Eigen::Matrix<double, 3, 1> >; std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::iterator = std::_Rb_tree_iterator<Eigen::Matrix<double, 3, 1> >; std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_Link_type = std::_Rb_tree_node<Eigen::Matrix<double, 3, 1> >*; std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::_Base_ptr = std::_Rb_tree_node_base*][m[K’
[01m[K/usr/include/c++/9/bits/stl_tree.h:2562:16:[m[K   required from ‘[01m[Kstd::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::iterator std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::find(const _Key&) [with _Key = Eigen::Matrix<double, 3, 1>; _Val = Eigen::Matrix<double, 3, 1>; _KeyOfValue = std::_Identity<Eigen::Matrix<double, 3, 1> >; _Compare = geo_utils::filterLess; _Alloc = std::allocator<Eigen::Matrix<double, 3, 1> >; std::_Rb_tree<_Key, _Val, _KeyOfValue, _Compare, _Alloc>::iterator = std::_Rb_tree_iterator<Eigen::Matrix<double, 3, 1> >][m[K’
[01m[K/usr/include/c++/9/bits/stl_set.h:795:29:[m[K   required from ‘[01m[Kstd::set<_Key, _Compare, _Alloc>::iterator std::set<_Key, _Compare, _Alloc>::find(const key_type&) [with _Key = Eigen::Matrix<double, 3, 1>; _Compare = geo_utils::filterLess; _Alloc = std::allocator<Eigen::Matrix<double, 3, 1> >; std::set<_Key, _Compare, _Alloc>::iterator = std::_Rb_tree_const_iterator<Eigen::Matrix<double, 3, 1> >; std::set<_Key, _Compare, _Alloc>::key_type = Eigen::Matrix<double, 3, 1>][m[K’
[01m[K/home/<USER>/lxy_ws/src/GCOPTER/gcopter/include/gcopter/geo_utils.hpp:113:35:[m[K   required from here
[01m[K/usr/include/c++/9/bits/stl_tree.h:785:8:[m[K [01;31m[Kerror: [m[Kstatic assertion failed: comparison object must be invocable as const
  785 |        [01;31m[Kis_invocable_v<const _Compare&, const _Key&, const _Key&>[m[K,
      |        [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
make[2]: *** [CMakeFiles/planner_px4.dir/build.make:63：CMakeFiles/planner_px4.dir/src/planner_px4_main.cpp.o] 错误 1
make[1]: *** [CMakeFiles/Makefile2:301：CMakeFiles/planner_px4.dir/all] 错误 2
make: *** [Makefile:141：all] 错误 2
