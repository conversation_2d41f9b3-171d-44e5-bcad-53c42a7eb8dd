In file included from [01m[K/usr/include/python3.8/numpy/ndarraytypes.h:1830[m[K,
                 from [01m[K/usr/include/python3.8/numpy/ndarrayobject.h:12[m[K,
                 from [01m[K/usr/include/python3.8/numpy/arrayobject.h:4[m[K,
                 from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:20[m[K:
[01m[K/usr/include/python3.8/numpy/npy_1_7_deprecated_api.h:17:2:[m[K [01;35m[Kwarning: [m[K#warning "Using deprecated NumPy API, disable it with " "#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION" [[01;35m[K-Wcpp[m[K]
   17 | #[01;35m[Kwarning[m[K "Using deprecated NumPy API, disable it with " \
      |  [01;35m[K^~~~~~~[m[K
In file included from [01m[K/usr/include/python3.8/numpy/ndarraytypes.h:1830[m[K,
                 from [01m[K/usr/include/python3.8/numpy/ndarrayobject.h:12[m[K,
                 from [01m[K/usr/include/python3.8/numpy/arrayobject.h:4[m[K,
                 from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:20[m[K:
[01m[K/usr/include/python3.8/numpy/npy_1_7_deprecated_api.h:17:2:[m[K [01;35m[Kwarning: [m[K#warning "Using deprecated NumPy API, disable it with " "#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION" [[01;35m[K-Wcpp[m[K]
   17 | #[01;35m[Kwarning[m[K "Using deprecated NumPy API, disable it with " \
      |  [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/src/planner_px4_main.cpp:31:10:[m[K [01;31m[Kfatal error: [m[Ksfc/sfc_cover_opt.hpp: 没有那个文件或目录
   31 | #include [01;31m[K"sfc/sfc_cover_opt.hpp"[m[K
      |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated.
make[2]: *** [CMakeFiles/planner_px4.dir/build.make:63：CMakeFiles/planner_px4.dir/src/planner_px4_main.cpp.o] 错误 1
make[2]: *** 正在等待未完成的任务....
In file included from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:19[m[K:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kbool Mrpt::load(const char*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:968:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  968 |       [01;35m[Kfread(&i, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:975:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  975 |       [01;35m[Kfread(&n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:976:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  976 |       [01;35m[Kfread(&depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:977:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  977 |       [01;35m[Kfread(&density, sizeof(float), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:986:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  986 |       [01;35m[Kfread(split_points.data(), sizeof(float), n_array * n_trees, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:992:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  992 |         [01;35m[Kfread(&sz, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:994:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  994 |         [01;35m[Kfread(&leaves[0], sizeof(int), sz, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1001:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1001 |         [01;35m[Kfread(&non_zeros, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1008:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1008 |           [01;35m[Kfread(&row, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1009:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1009 |           [01;35m[Kfread(&col, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1010:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1010 |           [01;35m[Kfread(&val, sizeof(float), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1018:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1018 |         [01;35m[Kfread(dense_random_matrix.data(), sizeof(float), n_pool * dim, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameter_list(FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1623:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1623 |       [01;35m[Kfread(&par_sz, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameters(Mrpt_Parameters*, FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1596:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1596 |       [01;35m[Kfread(&p->n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1597:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1597 |       [01;35m[Kfread(&p->depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1598:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1598 |       [01;35m[Kfread(&p->votes, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1599:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1599 |       [01;35m[Kfread(&p->k, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1600:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1600 |       [01;35m[Kfread(&p->estimated_qtime, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1601:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1601 |       [01;35m[Kfread(&p->estimated_recall, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/mrptmodule.cpp:19[m[K:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kbool Mrpt::load(const char*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:968:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  968 |       [01;35m[Kfread(&i, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:975:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  975 |       [01;35m[Kfread(&n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:976:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  976 |       [01;35m[Kfread(&depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:977:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  977 |       [01;35m[Kfread(&density, sizeof(float), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:986:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  986 |       [01;35m[Kfread(split_points.data(), sizeof(float), n_array * n_trees, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:992:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  992 |         [01;35m[Kfread(&sz, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:994:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
  994 |         [01;35m[Kfread(&leaves[0], sizeof(int), sz, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1001:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1001 |         [01;35m[Kfread(&non_zeros, sizeof(int), 1, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1008:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1008 |           [01;35m[Kfread(&row, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1009:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1009 |           [01;35m[Kfread(&col, sizeof(int), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1010:16:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1010 |           [01;35m[Kfread(&val, sizeof(float), 1, fd)[m[K;
      |           [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1018:14:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1018 |         [01;35m[Kfread(dense_random_matrix.data(), sizeof(float), n_pool * dim, fd)[m[K;
      |         [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameter_list(FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1623:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1623 |       [01;35m[Kfread(&par_sz, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:[m[K In member function ‘[01m[Kvoid Mrpt::read_parameters(Mrpt_Parameters*, FILE*)[m[K’:
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1596:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1596 |       [01;35m[Kfread(&p->n_trees, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1597:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1597 |       [01;35m[Kfread(&p->depth, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1598:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1598 |       [01;35m[Kfread(&p->votes, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1599:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1599 |       [01;35m[Kfread(&p->k, sizeof(int), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1600:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1600 |       [01;35m[Kfread(&p->estimated_qtime, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/lxy_ws/src/planner/modules/MRPT/Mrpt.h:1601:12:[m[K [01;35m[Kwarning: [m[Kignoring return value of ‘[01m[Ksize_t fread(void*, size_t, size_t, FILE*)[m[K’, declared with attribute warn_unused_result [[01;35m[K-Wunused-result[m[K]
 1601 |       [01;35m[Kfread(&p->estimated_recall, sizeof(double), 1, fd)[m[K;
      |       [01;35m[K~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
make[1]: *** [CMakeFiles/Makefile2:301：CMakeFiles/planner_px4.dir/all] 错误 2
make[1]: *** 正在等待未完成的任务....
make: *** [Makefile:141：all] 错误 2
