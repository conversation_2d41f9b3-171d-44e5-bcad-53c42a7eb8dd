[35m[1mScanning dependencies of target std_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target _tracking_controller_generate_messages_check_deps_Target[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_lisp[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_cpp[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_eus[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target geometry_msgs_generate_messages_py[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target std_msgs_generate_messages_lisp[0m
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target _tracking_controller_generate_messages_check_deps_Target
[35m[1mScanning dependencies of target tracking_controller_generate_messages_py[0m
[35m[1mScanning dependencies of target tracking_controller_generate_messages_nodejs[0m
[35m[1mScanning dependencies of target tracking_controller_generate_messages_eus[0m
[35m[1mScanning dependencies of target tracking_controller_generate_messages_lisp[0m
[35m[1mScanning dependencies of target tracking_controller_generate_messages_cpp[0m
[ 27%] [34m[1mGenerating Javascript code from tracking_controller/Target.msg[0m
[ 27%] [34m[1mGenerating EusLisp code from tracking_controller/Target.msg[0m
[ 27%] [34m[1mGenerating Lisp code from tracking_controller/Target.msg[0m
[ 36%] [34m[1mGenerating EusLisp manifest code for tracking_controller[0m
[ 54%] [34m[1mGenerating C++ code from tracking_controller/Target.msg[0m
[ 54%] [34m[1mGenerating Python from MSG tracking_controller/Target[0m
[ 54%] Built target tracking_controller_generate_messages_nodejs
[ 54%] Built target tracking_controller_generate_messages_lisp
[ 54%] Built target tracking_controller_generate_messages_cpp
[35m[1mScanning dependencies of target tracking_controller[0m
[ 63%] [34m[1mGenerating Python msg __init__.py for tracking_controller[0m
[ 72%] [32mBuilding CXX object CMakeFiles/tracking_controller.dir/include/tracking_controller/trackingController.cpp.o[0m
[ 72%] Built target tracking_controller_generate_messages_py
[ 72%] Built target tracking_controller_generate_messages_eus
[35m[1mScanning dependencies of target tracking_controller_generate_messages[0m
[ 72%] Built target tracking_controller_generate_messages
[ 81%] [32m[1mLinking CXX shared library /home/<USER>/lxy_ws/devel/.private/tracking_controller/lib/libtracking_controller.so[0m
[ 81%] Built target tracking_controller
[35m[1mScanning dependencies of target tracking_controller_node[0m
[ 90%] [32mBuilding CXX object CMakeFiles/tracking_controller_node.dir/src/tracking_controller_node.cpp.o[0m
[100%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/tracking_controller/lib/tracking_controller/tracking_controller_node[0m
[100%] Built target tracking_controller_node
