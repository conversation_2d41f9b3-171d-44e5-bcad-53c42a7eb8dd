[35m[1mScanning dependencies of target global_planner[0m
[35m[1mScanning dependencies of target rrt_star_interactive_node[0m
[35m[1mScanning dependencies of target rrt_interactive_node[0m
[ 10%] [32mBuilding CXX object CMakeFiles/rrt_star_interactive_node.dir/src/rrt_star_interactive_node.cpp.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/rrt_interactive_node.dir/src/rrt_interactive_node.cpp.o[0m
[ 30%] [32mBuilding CXX object CMakeFiles/global_planner.dir/src/globalPlannerLib.cpp.o[0m
[ 40%] [32mBuilding CXX object CMakeFiles/global_planner.dir/include/global_planner/PRMKDTree.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/global_planner.dir/include/global_planner/dep.cpp.o[0m
[ 60%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/global_planner/lib/global_planner/rrt_interactive_node[0m
[ 70%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/global_planner/lib/global_planner/rrt_star_interactive_node[0m
[ 70%] Built target rrt_interactive_node
[ 70%] Built target rrt_star_interactive_node
[ 80%] [32m[1mLinking CXX shared library /home/<USER>/lxy_ws/devel/.private/global_planner/lib/libglobal_planner.so[0m
[ 80%] Built target global_planner
[35m[1mScanning dependencies of target test_dep_node[0m
[ 90%] [32mBuilding CXX object CMakeFiles/test_dep_node.dir/src/test_dep_node.cpp.o[0m
[100%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/global_planner/lib/global_planner/test_dep_node[0m
[100%] Built target test_dep_node
