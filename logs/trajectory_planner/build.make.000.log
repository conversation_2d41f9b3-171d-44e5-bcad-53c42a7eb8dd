[35m[1mScanning dependencies of target mpc_solver_setup[0m
[35m[1mScanning dependencies of target trajectory_planner[0m
[  2%] [32mBuilding CXX object CMakeFiles/mpc_solver_setup.dir/src/mpc_solver_setup.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/path_search/astarOcc.cpp.o[0m
[  6%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/polyTrajSolver.cpp.o[0m
[  9%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/bspline.cpp.o[0m
[ 11%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/piecewiseLinearTraj.cpp.o[0m
[ 13%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/clustering/Kmeans.cpp.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/acado_qpoases_interface.cpp.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/bsplineTraj.cpp.o[0m
[ 22%] [32mBuilding C object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/acado_solver_sfunction.c.o[0m
[ 20%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpcPlanner.cpp.o[0m
[ 25%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/polyTrajOctomap.cpp.o[0m
[ 27%] [32mBuilding C object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/acado_integrator.c.o[0m
[ 29%] [32mBuilding C object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/acado_auxiliary_functions.c.o[0m
[ 31%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/polyTrajOccMap.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/nominal_mpcc.cpp.o[0m
[ 38%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/clustering/obstacleClustering.cpp.o[0m
[ 38%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/obstacle_test/dynamicObstacleGenerator.cpp.o[0m
[ 43%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/Bounds.cpp.o[0m
[ 43%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/Constraints.cpp.o[0m
[ 45%] [32mBuilding C object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/acado_solver.c.o[0m
[ 47%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/CyclingManager.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/Indexlist.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/MessageHandling.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/QProblem.cpp.o[0m
[ 56%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/QProblemB.cpp.o[0m
[ 59%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/SubjectTo.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/Utils.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/trajectory_planner.dir/include/trajectory_planner/mpc_solver/qpoases/SRC/EXTRAS/SolutionAnalysis.cpp.o[0m
[ 65%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/mpc_solver_setup[0m
[ 65%] Built target mpc_solver_setup
[ 68%] [32m[1mLinking CXX shared library /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/libtrajectory_planner.so[0m
[ 68%] Built target trajectory_planner
[35m[1mScanning dependencies of target bspline_node[0m
[35m[1mScanning dependencies of target poly_RRTStar_node[0m
[35m[1mScanning dependencies of target poly_RRT_node[0m
[35m[1mScanning dependencies of target mpc_node[0m
[35m[1mScanning dependencies of target poly_RRT_goal_node[0m
[35m[1mScanning dependencies of target poly_RRTStar_goal_node[0m
[35m[1mScanning dependencies of target testObstacleClustering[0m
[ 70%] [32mBuilding CXX object CMakeFiles/poly_RRTStar_node.dir/src/poly_RRTStar_node.cpp.o[0m
[ 72%] [32mBuilding CXX object CMakeFiles/poly_RRT_goal_node.dir/src/poly_RRT_goal_node.cpp.o[0m
[ 75%] [32mBuilding CXX object CMakeFiles/poly_RRTStar_goal_node.dir/src/poly_RRTStar_goal_node.cpp.o[0m
[ 77%] [32mBuilding CXX object CMakeFiles/poly_RRT_node.dir/src/poly_RRT_node.cpp.o[0m
[ 79%] [32mBuilding CXX object CMakeFiles/bspline_node.dir/src/bspline_node.cpp.o[0m
[ 84%] [32mBuilding CXX object CMakeFiles/testObstacleClustering.dir/src/test/testObstacleClustering.cpp.o[0m
[ 84%] [32mBuilding CXX object CMakeFiles/mpc_node.dir/src/mpc_node.cpp.o[0m
[ 86%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/poly_RRT_node[0m
[ 88%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/poly_RRT_goal_node[0m
[ 90%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/poly_RRTStar_node[0m
[ 93%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/poly_RRTStar_goal_node[0m
[ 93%] Built target poly_RRT_node
[ 93%] Built target poly_RRT_goal_node
[ 93%] Built target poly_RRTStar_node
[ 93%] Built target poly_RRTStar_goal_node
[ 95%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/mpc_node[0m
[ 97%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/testObstacleClustering[0m
[100%] [32m[1mLinking CXX executable /home/<USER>/lxy_ws/devel/.private/trajectory_planner/lib/trajectory_planner/bspline_node[0m
[100%] Built target mpc_node
[100%] Built target testObstacleClustering
[100%] Built target bspline_node
