Symlinking /home/<USER>/lxy_ws/devel/lib/libtrajectory_planner.so
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/poly_RRTStar_node
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/bspline_node
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/poly_RRT_node
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/poly_RRT_goal_node
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/poly_RRTStar_goal_node
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_node
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/mpc_solver_setup
Symlinking /home/<USER>/lxy_ws/devel/lib/trajectory_planner/testObstacleClustering
Symlinking /home/<USER>/lxy_ws/devel/lib/pkgconfig/trajectory_planner.pc
Symlinking /home/<USER>/lxy_ws/devel/share/trajectory_planner/cmake/trajectory_plannerConfig.cmake
Symlinking /home/<USER>/lxy_ws/devel/share/trajectory_planner/cmake/trajectory_plannerConfig-version.cmake
