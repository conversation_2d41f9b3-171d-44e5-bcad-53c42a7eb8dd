Not searching for unused variables given on the command line.
[36m--[0m The C compiler identification is GNU 9.4.0
[36m--[0m The CXX compiler identification is GNU 9.4.0
[36m--[0m Check for working C compiler: /usr/bin/cc
[36m--[0m Check for working C compiler: /usr/bin/cc -- works
[36m--[0m Detecting C compiler ABI info
[36m--[0m Detecting C compiler ABI info - done
[36m--[0m Detecting C compile features
[36m--[0m Detecting C compile features - done
[36m--[0m Check for working CXX compiler: /usr/bin/c++
[36m--[0m Check for working CXX compiler: /usr/bin/c++ -- works
[36m--[0m Detecting CXX compiler ABI info
[36m--[0m Detecting CXX compiler ABI info - done
[36m--[0m Detecting CXX compile features
[36m--[0m Detecting CXX compile features - done
[36m--[0m Using CATKIN_DEVEL_PREFIX: /home/<USER>/lxy_ws/devel/.private/trajectory_planner
[36m--[0m Using CMAKE_PREFIX_PATH: /home/<USER>/lxy_ws/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic
[36m--[0m This workspace overlays: /home/<USER>/lxy_ws/devel;/home/<USER>/catkin_ws/devel;/opt/ros/noetic
[36m--[0m Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3")
[36m--[0m Using PYTHON_EXECUTABLE: /usr/bin/python3
[36m--[0m Using Debian Python package layout
[36m--[0m Found PY_em: /usr/lib/python3/dist-packages/em.py
[36m--[0m Using empy: /usr/lib/python3/dist-packages/em.py
[36m--[0m Using CATKIN_ENABLE_TESTING: ON
[36m--[0m Call enable_testing()
[36m--[0m Using CATKIN_TEST_RESULTS_DIR: /home/<USER>/lxy_ws/build/trajectory_planner/test_results
[36m--[0m Forcing gtest/gmock from source, though one was otherwise available.
[36m--[0m Found gtest sources under '/usr/src/googletest': gtests will be built
[36m--[0m Found gmock sources under '/usr/src/googletest': gmock will be built
[36m--[0m Found PythonInterp: /usr/bin/python3 (found version "3.8.10")
[36m--[0m Found Threads: TRUE
[36m--[0m Using Python nosetests: /usr/bin/nosetests3
[36m--[0m catkin 0.8.12
[36m--[0m BUILD_SHARED_LIBS is on
[36m--[0m Using these message generators: gencpp;geneus;genlisp;gennodejs;genpy
[36m--[0m Found Python3: /usr/lib/x86_64-linux-gnu/libpython3.8.so (found version "3.8.10") found components: Development NumPy Interpreter
[36m--[0m Found nlopt library: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/nlopt/libnlopt.so.1.0.0
[36m--[0m Found ACADO library: /home/<USER>/lxy_ws/src/trajectory_planner/include/trajectory_planner/third_party/ACADO/libacado_toolkit_s.so.1.2.2beta
Detected x86_64 architecture.
[36m--[0m Configuring done
[36m--[0m Generating done
[36m--[0m Build files have been written to: /home/<USER>/lxy_ws/build/trajectory_planner
