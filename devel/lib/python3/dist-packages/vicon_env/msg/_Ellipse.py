# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from vicon_env/Ellipse.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import vicon_env.msg

class Ellipse(genpy.Message):
  _md5sum = "0d9f78b0e58e82ba751d86429f04bc3c"
  _type = "vicon_env/Ellipse"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """int32 id
Point2d pos
Point2d noise
float64 r #clearance range
float64 a
float64 b
float64 alpha # rotation

================================================================================
MSG: vicon_env/Point2d
float64 x
float64 y
"""
  __slots__ = ['id','pos','noise','r','a','b','alpha']
  _slot_types = ['int32','vicon_env/Point2d','vicon_env/Point2d','float64','float64','float64','float64']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       id,pos,noise,r,a,b,alpha

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Ellipse, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.id is None:
        self.id = 0
      if self.pos is None:
        self.pos = vicon_env.msg.Point2d()
      if self.noise is None:
        self.noise = vicon_env.msg.Point2d()
      if self.r is None:
        self.r = 0.
      if self.a is None:
        self.a = 0.
      if self.b is None:
        self.b = 0.
      if self.alpha is None:
        self.alpha = 0.
    else:
      self.id = 0
      self.pos = vicon_env.msg.Point2d()
      self.noise = vicon_env.msg.Point2d()
      self.r = 0.
      self.a = 0.
      self.b = 0.
      self.alpha = 0.

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_i8d().pack(_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r, _x.a, _x.b, _x.alpha))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.pos is None:
        self.pos = vicon_env.msg.Point2d()
      if self.noise is None:
        self.noise = vicon_env.msg.Point2d()
      end = 0
      _x = self
      start = end
      end += 68
      (_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r, _x.a, _x.b, _x.alpha,) = _get_struct_i8d().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_i8d().pack(_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r, _x.a, _x.b, _x.alpha))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.pos is None:
        self.pos = vicon_env.msg.Point2d()
      if self.noise is None:
        self.noise = vicon_env.msg.Point2d()
      end = 0
      _x = self
      start = end
      end += 68
      (_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r, _x.a, _x.b, _x.alpha,) = _get_struct_i8d().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_i8d = None
def _get_struct_i8d():
    global _struct_i8d
    if _struct_i8d is None:
        _struct_i8d = struct.Struct("<i8d")
    return _struct_i8d
