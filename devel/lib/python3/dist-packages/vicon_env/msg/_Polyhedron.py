# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from vicon_env/Polyhedron.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import vicon_env.msg

class Polyhedron(genpy.Message):
  _md5sum = "4e4035e4e6d87b6937718889da241e8b"
  _type = "vicon_env/Polyhedron"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """int32 id
Point2d[] rps
Point3d noise
float64 r #clearance range
Point3d[] points
Point3d[] normals #norm is an outer vector

================================================================================
MSG: vicon_env/Point2d
float64 x
float64 y

================================================================================
MSG: vicon_env/Point3d
float64 x
float64 y
float64 z
"""
  __slots__ = ['id','rps','noise','r','points','normals']
  _slot_types = ['int32','vicon_env/Point2d[]','vicon_env/Point3d','float64','vicon_env/Point3d[]','vicon_env/Point3d[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       id,rps,noise,r,points,normals

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Polyhedron, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.id is None:
        self.id = 0
      if self.rps is None:
        self.rps = []
      if self.noise is None:
        self.noise = vicon_env.msg.Point3d()
      if self.r is None:
        self.r = 0.
      if self.points is None:
        self.points = []
      if self.normals is None:
        self.normals = []
    else:
      self.id = 0
      self.rps = []
      self.noise = vicon_env.msg.Point3d()
      self.r = 0.
      self.points = []
      self.normals = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.id
      buff.write(_get_struct_i().pack(_x))
      length = len(self.rps)
      buff.write(_struct_I.pack(length))
      for val1 in self.rps:
        _x = val1
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
      _x = self
      buff.write(_get_struct_4d().pack(_x.noise.x, _x.noise.y, _x.noise.z, _x.r))
      length = len(self.points)
      buff.write(_struct_I.pack(length))
      for val1 in self.points:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      length = len(self.normals)
      buff.write(_struct_I.pack(length))
      for val1 in self.normals:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.rps is None:
        self.rps = None
      if self.noise is None:
        self.noise = vicon_env.msg.Point3d()
      if self.points is None:
        self.points = None
      if self.normals is None:
        self.normals = None
      end = 0
      start = end
      end += 4
      (self.id,) = _get_struct_i().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.rps = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point2d()
        _x = val1
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        self.rps.append(val1)
      _x = self
      start = end
      end += 32
      (_x.noise.x, _x.noise.y, _x.noise.z, _x.r,) = _get_struct_4d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.points = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point3d()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.points.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.normals = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point3d()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.normals.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.id
      buff.write(_get_struct_i().pack(_x))
      length = len(self.rps)
      buff.write(_struct_I.pack(length))
      for val1 in self.rps:
        _x = val1
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
      _x = self
      buff.write(_get_struct_4d().pack(_x.noise.x, _x.noise.y, _x.noise.z, _x.r))
      length = len(self.points)
      buff.write(_struct_I.pack(length))
      for val1 in self.points:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      length = len(self.normals)
      buff.write(_struct_I.pack(length))
      for val1 in self.normals:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.rps is None:
        self.rps = None
      if self.noise is None:
        self.noise = vicon_env.msg.Point3d()
      if self.points is None:
        self.points = None
      if self.normals is None:
        self.normals = None
      end = 0
      start = end
      end += 4
      (self.id,) = _get_struct_i().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.rps = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point2d()
        _x = val1
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        self.rps.append(val1)
      _x = self
      start = end
      end += 32
      (_x.noise.x, _x.noise.y, _x.noise.z, _x.r,) = _get_struct_4d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.points = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point3d()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.points.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.normals = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point3d()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.normals.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2d = None
def _get_struct_2d():
    global _struct_2d
    if _struct_2d is None:
        _struct_2d = struct.Struct("<2d")
    return _struct_2d
_struct_3d = None
def _get_struct_3d():
    global _struct_3d
    if _struct_3d is None:
        _struct_3d = struct.Struct("<3d")
    return _struct_3d
_struct_4d = None
def _get_struct_4d():
    global _struct_4d
    if _struct_4d is None:
        _struct_4d = struct.Struct("<4d")
    return _struct_4d
_struct_i = None
def _get_struct_i():
    global _struct_i
    if _struct_i is None:
        _struct_i = struct.Struct("<i")
    return _struct_i
