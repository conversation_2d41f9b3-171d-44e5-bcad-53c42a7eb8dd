# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from vicon_env/Polygon.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import vicon_env.msg

class Polygon(genpy.Message):
  _md5sum = "********************************"
  _type = "vicon_env/Polygon"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """int32 id
Point2d pos
Point2d noise
float64 r #clearance range
Point2d[] points
Point2d[] normals #norm is an outer vector

================================================================================
MSG: vicon_env/Point2d
float64 x
float64 y
"""
  __slots__ = ['id','pos','noise','r','points','normals']
  _slot_types = ['int32','vicon_env/Point2d','vicon_env/Point2d','float64','vicon_env/Point2d[]','vicon_env/Point2d[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       id,pos,noise,r,points,normals

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(Polygon, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.id is None:
        self.id = 0
      if self.pos is None:
        self.pos = vicon_env.msg.Point2d()
      if self.noise is None:
        self.noise = vicon_env.msg.Point2d()
      if self.r is None:
        self.r = 0.
      if self.points is None:
        self.points = []
      if self.normals is None:
        self.normals = []
    else:
      self.id = 0
      self.pos = vicon_env.msg.Point2d()
      self.noise = vicon_env.msg.Point2d()
      self.r = 0.
      self.points = []
      self.normals = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_i5d().pack(_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r))
      length = len(self.points)
      buff.write(_struct_I.pack(length))
      for val1 in self.points:
        _x = val1
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
      length = len(self.normals)
      buff.write(_struct_I.pack(length))
      for val1 in self.normals:
        _x = val1
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.pos is None:
        self.pos = vicon_env.msg.Point2d()
      if self.noise is None:
        self.noise = vicon_env.msg.Point2d()
      if self.points is None:
        self.points = None
      if self.normals is None:
        self.normals = None
      end = 0
      _x = self
      start = end
      end += 44
      (_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r,) = _get_struct_i5d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.points = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point2d()
        _x = val1
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        self.points.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.normals = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point2d()
        _x = val1
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        self.normals.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_i5d().pack(_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r))
      length = len(self.points)
      buff.write(_struct_I.pack(length))
      for val1 in self.points:
        _x = val1
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
      length = len(self.normals)
      buff.write(_struct_I.pack(length))
      for val1 in self.normals:
        _x = val1
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.pos is None:
        self.pos = vicon_env.msg.Point2d()
      if self.noise is None:
        self.noise = vicon_env.msg.Point2d()
      if self.points is None:
        self.points = None
      if self.normals is None:
        self.normals = None
      end = 0
      _x = self
      start = end
      end += 44
      (_x.id, _x.pos.x, _x.pos.y, _x.noise.x, _x.noise.y, _x.r,) = _get_struct_i5d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.points = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point2d()
        _x = val1
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        self.points.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.normals = []
      for i in range(0, length):
        val1 = vicon_env.msg.Point2d()
        _x = val1
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        self.normals.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2d = None
def _get_struct_2d():
    global _struct_2d
    if _struct_2d is None:
        _struct_2d = struct.Struct("<2d")
    return _struct_2d
_struct_i5d = None
def _get_struct_i5d():
    global _struct_i5d
    if _struct_i5d is None:
        _struct_i5d = struct.Struct("<i5d")
    return _struct_i5d
