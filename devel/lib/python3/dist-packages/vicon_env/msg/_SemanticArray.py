# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from vicon_env/SemanticArray.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import std_msgs.msg
import vicon_env.msg

class SemanticArray(genpy.Message):
  _md5sum = "f11c93d4fb77d44a8c5e24f11f9114d5"
  _type = "vicon_env/SemanticArray"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """std_msgs/Header header
int32 mav_id #-1 for global map, 0 + for the mav_id
Point3d mav_pos

#2d semantics
Ellipse[] ellipses
Circle[]  circles
Polygon[] polygons

#3d semantics
Cylinder[] cylinders
Polyhedron[] polyhedrons

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: vicon_env/Point3d
float64 x
float64 y
float64 z

================================================================================
MSG: vicon_env/Ellipse
int32 id
Point2d pos
Point2d noise
float64 r #clearance range
float64 a
float64 b
float64 alpha # rotation

================================================================================
MSG: vicon_env/Point2d
float64 x
float64 y

================================================================================
MSG: vicon_env/Circle
int32 id
Point2d pos
Point2d noise
float64 r


================================================================================
MSG: vicon_env/Polygon
int32 id
Point2d pos
Point2d noise
float64 r #clearance range
Point2d[] points
Point2d[] normals #norm is an outer vector

================================================================================
MSG: vicon_env/Cylinder
int32 id
Point2d pos
Point3d noise
float64 r
float64 h

================================================================================
MSG: vicon_env/Polyhedron
int32 id
Point2d[] rps
Point3d noise
float64 r #clearance range
Point3d[] points
Point3d[] normals #norm is an outer vector
"""
  __slots__ = ['header','mav_id','mav_pos','ellipses','circles','polygons','cylinders','polyhedrons']
  _slot_types = ['std_msgs/Header','int32','vicon_env/Point3d','vicon_env/Ellipse[]','vicon_env/Circle[]','vicon_env/Polygon[]','vicon_env/Cylinder[]','vicon_env/Polyhedron[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,mav_id,mav_pos,ellipses,circles,polygons,cylinders,polyhedrons

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(SemanticArray, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.mav_id is None:
        self.mav_id = 0
      if self.mav_pos is None:
        self.mav_pos = vicon_env.msg.Point3d()
      if self.ellipses is None:
        self.ellipses = []
      if self.circles is None:
        self.circles = []
      if self.polygons is None:
        self.polygons = []
      if self.cylinders is None:
        self.cylinders = []
      if self.polyhedrons is None:
        self.polyhedrons = []
    else:
      self.header = std_msgs.msg.Header()
      self.mav_id = 0
      self.mav_pos = vicon_env.msg.Point3d()
      self.ellipses = []
      self.circles = []
      self.polygons = []
      self.cylinders = []
      self.polyhedrons = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_i3d().pack(_x.mav_id, _x.mav_pos.x, _x.mav_pos.y, _x.mav_pos.z))
      length = len(self.ellipses)
      buff.write(_struct_I.pack(length))
      for val1 in self.ellipses:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v1 = val1.pos
        _x = _v1
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v2 = val1.noise
        _x = _v2
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _x = val1
        buff.write(_get_struct_4d().pack(_x.r, _x.a, _x.b, _x.alpha))
      length = len(self.circles)
      buff.write(_struct_I.pack(length))
      for val1 in self.circles:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v3 = val1.pos
        _x = _v3
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v4 = val1.noise
        _x = _v4
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _x = val1.r
        buff.write(_get_struct_d().pack(_x))
      length = len(self.polygons)
      buff.write(_struct_I.pack(length))
      for val1 in self.polygons:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v5 = val1.pos
        _x = _v5
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v6 = val1.noise
        _x = _v6
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _x = val1.r
        buff.write(_get_struct_d().pack(_x))
        length = len(val1.points)
        buff.write(_struct_I.pack(length))
        for val2 in val1.points:
          _x = val2
          buff.write(_get_struct_2d().pack(_x.x, _x.y))
        length = len(val1.normals)
        buff.write(_struct_I.pack(length))
        for val2 in val1.normals:
          _x = val2
          buff.write(_get_struct_2d().pack(_x.x, _x.y))
      length = len(self.cylinders)
      buff.write(_struct_I.pack(length))
      for val1 in self.cylinders:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v7 = val1.pos
        _x = _v7
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v8 = val1.noise
        _x = _v8
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _x = val1
        buff.write(_get_struct_2d().pack(_x.r, _x.h))
      length = len(self.polyhedrons)
      buff.write(_struct_I.pack(length))
      for val1 in self.polyhedrons:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        length = len(val1.rps)
        buff.write(_struct_I.pack(length))
        for val2 in val1.rps:
          _x = val2
          buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v9 = val1.noise
        _x = _v9
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _x = val1.r
        buff.write(_get_struct_d().pack(_x))
        length = len(val1.points)
        buff.write(_struct_I.pack(length))
        for val2 in val1.points:
          _x = val2
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        length = len(val1.normals)
        buff.write(_struct_I.pack(length))
        for val2 in val1.normals:
          _x = val2
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.mav_pos is None:
        self.mav_pos = vicon_env.msg.Point3d()
      if self.ellipses is None:
        self.ellipses = None
      if self.circles is None:
        self.circles = None
      if self.polygons is None:
        self.polygons = None
      if self.cylinders is None:
        self.cylinders = None
      if self.polyhedrons is None:
        self.polyhedrons = None
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 28
      (_x.mav_id, _x.mav_pos.x, _x.mav_pos.y, _x.mav_pos.z,) = _get_struct_i3d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.ellipses = []
      for i in range(0, length):
        val1 = vicon_env.msg.Ellipse()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v10 = val1.pos
        _x = _v10
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v11 = val1.noise
        _x = _v11
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _x = val1
        start = end
        end += 32
        (_x.r, _x.a, _x.b, _x.alpha,) = _get_struct_4d().unpack(str[start:end])
        self.ellipses.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.circles = []
      for i in range(0, length):
        val1 = vicon_env.msg.Circle()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v12 = val1.pos
        _x = _v12
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v13 = val1.noise
        _x = _v13
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        start = end
        end += 8
        (val1.r,) = _get_struct_d().unpack(str[start:end])
        self.circles.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.polygons = []
      for i in range(0, length):
        val1 = vicon_env.msg.Polygon()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v14 = val1.pos
        _x = _v14
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v15 = val1.noise
        _x = _v15
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        start = end
        end += 8
        (val1.r,) = _get_struct_d().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.points = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point2d()
          _x = val2
          start = end
          end += 16
          (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
          val1.points.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.normals = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point2d()
          _x = val2
          start = end
          end += 16
          (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
          val1.normals.append(val2)
        self.polygons.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.cylinders = []
      for i in range(0, length):
        val1 = vicon_env.msg.Cylinder()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v16 = val1.pos
        _x = _v16
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v17 = val1.noise
        _x = _v17
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        _x = val1
        start = end
        end += 16
        (_x.r, _x.h,) = _get_struct_2d().unpack(str[start:end])
        self.cylinders.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.polyhedrons = []
      for i in range(0, length):
        val1 = vicon_env.msg.Polyhedron()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.rps = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point2d()
          _x = val2
          start = end
          end += 16
          (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
          val1.rps.append(val2)
        _v18 = val1.noise
        _x = _v18
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        start = end
        end += 8
        (val1.r,) = _get_struct_d().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.points = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point3d()
          _x = val2
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          val1.points.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.normals = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point3d()
          _x = val2
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          val1.normals.append(val2)
        self.polyhedrons.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_i3d().pack(_x.mav_id, _x.mav_pos.x, _x.mav_pos.y, _x.mav_pos.z))
      length = len(self.ellipses)
      buff.write(_struct_I.pack(length))
      for val1 in self.ellipses:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v19 = val1.pos
        _x = _v19
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v20 = val1.noise
        _x = _v20
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _x = val1
        buff.write(_get_struct_4d().pack(_x.r, _x.a, _x.b, _x.alpha))
      length = len(self.circles)
      buff.write(_struct_I.pack(length))
      for val1 in self.circles:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v21 = val1.pos
        _x = _v21
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v22 = val1.noise
        _x = _v22
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _x = val1.r
        buff.write(_get_struct_d().pack(_x))
      length = len(self.polygons)
      buff.write(_struct_I.pack(length))
      for val1 in self.polygons:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v23 = val1.pos
        _x = _v23
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v24 = val1.noise
        _x = _v24
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _x = val1.r
        buff.write(_get_struct_d().pack(_x))
        length = len(val1.points)
        buff.write(_struct_I.pack(length))
        for val2 in val1.points:
          _x = val2
          buff.write(_get_struct_2d().pack(_x.x, _x.y))
        length = len(val1.normals)
        buff.write(_struct_I.pack(length))
        for val2 in val1.normals:
          _x = val2
          buff.write(_get_struct_2d().pack(_x.x, _x.y))
      length = len(self.cylinders)
      buff.write(_struct_I.pack(length))
      for val1 in self.cylinders:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        _v25 = val1.pos
        _x = _v25
        buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v26 = val1.noise
        _x = _v26
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _x = val1
        buff.write(_get_struct_2d().pack(_x.r, _x.h))
      length = len(self.polyhedrons)
      buff.write(_struct_I.pack(length))
      for val1 in self.polyhedrons:
        _x = val1.id
        buff.write(_get_struct_i().pack(_x))
        length = len(val1.rps)
        buff.write(_struct_I.pack(length))
        for val2 in val1.rps:
          _x = val2
          buff.write(_get_struct_2d().pack(_x.x, _x.y))
        _v27 = val1.noise
        _x = _v27
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _x = val1.r
        buff.write(_get_struct_d().pack(_x))
        length = len(val1.points)
        buff.write(_struct_I.pack(length))
        for val2 in val1.points:
          _x = val2
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        length = len(val1.normals)
        buff.write(_struct_I.pack(length))
        for val2 in val1.normals:
          _x = val2
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.mav_pos is None:
        self.mav_pos = vicon_env.msg.Point3d()
      if self.ellipses is None:
        self.ellipses = None
      if self.circles is None:
        self.circles = None
      if self.polygons is None:
        self.polygons = None
      if self.cylinders is None:
        self.cylinders = None
      if self.polyhedrons is None:
        self.polyhedrons = None
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 28
      (_x.mav_id, _x.mav_pos.x, _x.mav_pos.y, _x.mav_pos.z,) = _get_struct_i3d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.ellipses = []
      for i in range(0, length):
        val1 = vicon_env.msg.Ellipse()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v28 = val1.pos
        _x = _v28
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v29 = val1.noise
        _x = _v29
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _x = val1
        start = end
        end += 32
        (_x.r, _x.a, _x.b, _x.alpha,) = _get_struct_4d().unpack(str[start:end])
        self.ellipses.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.circles = []
      for i in range(0, length):
        val1 = vicon_env.msg.Circle()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v30 = val1.pos
        _x = _v30
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v31 = val1.noise
        _x = _v31
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        start = end
        end += 8
        (val1.r,) = _get_struct_d().unpack(str[start:end])
        self.circles.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.polygons = []
      for i in range(0, length):
        val1 = vicon_env.msg.Polygon()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v32 = val1.pos
        _x = _v32
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v33 = val1.noise
        _x = _v33
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        start = end
        end += 8
        (val1.r,) = _get_struct_d().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.points = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point2d()
          _x = val2
          start = end
          end += 16
          (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
          val1.points.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.normals = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point2d()
          _x = val2
          start = end
          end += 16
          (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
          val1.normals.append(val2)
        self.polygons.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.cylinders = []
      for i in range(0, length):
        val1 = vicon_env.msg.Cylinder()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        _v34 = val1.pos
        _x = _v34
        start = end
        end += 16
        (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
        _v35 = val1.noise
        _x = _v35
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        _x = val1
        start = end
        end += 16
        (_x.r, _x.h,) = _get_struct_2d().unpack(str[start:end])
        self.cylinders.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.polyhedrons = []
      for i in range(0, length):
        val1 = vicon_env.msg.Polyhedron()
        start = end
        end += 4
        (val1.id,) = _get_struct_i().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.rps = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point2d()
          _x = val2
          start = end
          end += 16
          (_x.x, _x.y,) = _get_struct_2d().unpack(str[start:end])
          val1.rps.append(val2)
        _v36 = val1.noise
        _x = _v36
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        start = end
        end += 8
        (val1.r,) = _get_struct_d().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.points = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point3d()
          _x = val2
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          val1.points.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.normals = []
        for i in range(0, length):
          val2 = vicon_env.msg.Point3d()
          _x = val2
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          val1.normals.append(val2)
        self.polyhedrons.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2d = None
def _get_struct_2d():
    global _struct_2d
    if _struct_2d is None:
        _struct_2d = struct.Struct("<2d")
    return _struct_2d
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_3d = None
def _get_struct_3d():
    global _struct_3d
    if _struct_3d is None:
        _struct_3d = struct.Struct("<3d")
    return _struct_3d
_struct_4d = None
def _get_struct_4d():
    global _struct_4d
    if _struct_4d is None:
        _struct_4d = struct.Struct("<4d")
    return _struct_4d
_struct_d = None
def _get_struct_d():
    global _struct_d
    if _struct_d is None:
        _struct_d = struct.Struct("<d")
    return _struct_d
_struct_i = None
def _get_struct_i():
    global _struct_i
    if _struct_i is None:
        _struct_i = struct.Struct("<i")
    return _struct_i
_struct_i3d = None
def _get_struct_i3d():
    global _struct_i3d
    if _struct_i3d is None:
        _struct_i3d = struct.Struct("<i3d")
    return _struct_i3d
