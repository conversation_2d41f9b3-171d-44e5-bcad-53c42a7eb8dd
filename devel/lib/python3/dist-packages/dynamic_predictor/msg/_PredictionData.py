# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from dynamic_predictor/PredictionData.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import std_msgs.msg

class PredictionData(genpy.Message):
  _md5sum = "51a1997e68e7a493e21155880d375c24"
  _type = "dynamic_predictor/PredictionData"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """# 动态障碍物预测数据消息
# 对应C++数据结构：
# - std::vector<Eigen::VectorXd> intentProb_
# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_

std_msgs/Header header

# ========== 数据维度信息 ==========
int32 num_obstacles          # 障碍物数量
int32 num_intents           # 意图数量 (固定为4: FORWARD=0, LEFT=1, RIGHT=2, STOP=3)
int32 num_time_steps        # 预测时间步数

# ========== 意图概率数据 ==========
# 对应: std::vector<Eigen::VectorXd> intentProb_
# 数据排列: [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, obs1_intent1, ...]
# 访问方式: prob = intent_probs_data[obstacle_idx * num_intents + intent_idx]
float64[] intent_probs_data

# ========== 位置预测数据 ==========
# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
# 访问方式: pos = pos_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
geometry_msgs/Point[] pos_pred_data

# ========== 尺寸预测数据 ==========
# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_
# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
# 访问方式: size = size_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
geometry_msgs/Point[] size_pred_data 
================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
"""
  __slots__ = ['header','num_obstacles','num_intents','num_time_steps','intent_probs_data','pos_pred_data','size_pred_data']
  _slot_types = ['std_msgs/Header','int32','int32','int32','float64[]','geometry_msgs/Point[]','geometry_msgs/Point[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,num_obstacles,num_intents,num_time_steps,intent_probs_data,pos_pred_data,size_pred_data

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(PredictionData, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.num_obstacles is None:
        self.num_obstacles = 0
      if self.num_intents is None:
        self.num_intents = 0
      if self.num_time_steps is None:
        self.num_time_steps = 0
      if self.intent_probs_data is None:
        self.intent_probs_data = []
      if self.pos_pred_data is None:
        self.pos_pred_data = []
      if self.size_pred_data is None:
        self.size_pred_data = []
    else:
      self.header = std_msgs.msg.Header()
      self.num_obstacles = 0
      self.num_intents = 0
      self.num_time_steps = 0
      self.intent_probs_data = []
      self.pos_pred_data = []
      self.size_pred_data = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3i().pack(_x.num_obstacles, _x.num_intents, _x.num_time_steps))
      length = len(self.intent_probs_data)
      buff.write(_struct_I.pack(length))
      pattern = '<%sd'%length
      buff.write(struct.Struct(pattern).pack(*self.intent_probs_data))
      length = len(self.pos_pred_data)
      buff.write(_struct_I.pack(length))
      for val1 in self.pos_pred_data:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      length = len(self.size_pred_data)
      buff.write(_struct_I.pack(length))
      for val1 in self.size_pred_data:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.pos_pred_data is None:
        self.pos_pred_data = None
      if self.size_pred_data is None:
        self.size_pred_data = None
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.num_obstacles, _x.num_intents, _x.num_time_steps,) = _get_struct_3i().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sd'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.intent_probs_data = s.unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.pos_pred_data = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.pos_pred_data.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.size_pred_data = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.size_pred_data.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3i().pack(_x.num_obstacles, _x.num_intents, _x.num_time_steps))
      length = len(self.intent_probs_data)
      buff.write(_struct_I.pack(length))
      pattern = '<%sd'%length
      buff.write(self.intent_probs_data.tostring())
      length = len(self.pos_pred_data)
      buff.write(_struct_I.pack(length))
      for val1 in self.pos_pred_data:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      length = len(self.size_pred_data)
      buff.write(_struct_I.pack(length))
      for val1 in self.size_pred_data:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.pos_pred_data is None:
        self.pos_pred_data = None
      if self.size_pred_data is None:
        self.size_pred_data = None
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.num_obstacles, _x.num_intents, _x.num_time_steps,) = _get_struct_3i().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sd'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.intent_probs_data = numpy.frombuffer(str[start:end], dtype=numpy.float64, count=length)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.pos_pred_data = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.pos_pred_data.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.size_pred_data = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.size_pred_data.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_3d = None
def _get_struct_3d():
    global _struct_3d
    if _struct_3d is None:
        _struct_3d = struct.Struct("<3d")
    return _struct_3d
_struct_3i = None
def _get_struct_3i():
    global _struct_3i
    if _struct_3i is None:
        _struct_3i = struct.Struct("<3i")
    return _struct_3i
