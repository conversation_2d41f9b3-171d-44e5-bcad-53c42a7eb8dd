# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from opt_sfc/TrajectoryTarget.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import std_msgs.msg

class TrajectoryTarget(genpy.Message):
  _md5sum = "af6564869756c5cd87560435d26882f1"
  _type = "opt_sfc/TrajectoryTarget"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """# TrajectoryTarget.msg
# 包含起点和终点的位置、速度、加速度信息

# 标准消息头
std_msgs/Header header

# 起点信息
geometry_msgs/Point start_position     # 起点位置
geometry_msgs/Vector3 start_velocity   # 起点速度
geometry_msgs/Vector3 start_acceleration  # 起点加速度

# 终点信息
geometry_msgs/Point goal_position      # 终点位置
geometry_msgs/Vector3 goal_velocity    # 终点速度
geometry_msgs/Vector3 goal_acceleration   # 终点加速度

# 可视化控制标志
bool enable_visualization              # 是否启用可视化
================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z"""
  __slots__ = ['header','start_position','start_velocity','start_acceleration','goal_position','goal_velocity','goal_acceleration','enable_visualization']
  _slot_types = ['std_msgs/Header','geometry_msgs/Point','geometry_msgs/Vector3','geometry_msgs/Vector3','geometry_msgs/Point','geometry_msgs/Vector3','geometry_msgs/Vector3','bool']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,start_position,start_velocity,start_acceleration,goal_position,goal_velocity,goal_acceleration,enable_visualization

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(TrajectoryTarget, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.start_position is None:
        self.start_position = geometry_msgs.msg.Point()
      if self.start_velocity is None:
        self.start_velocity = geometry_msgs.msg.Vector3()
      if self.start_acceleration is None:
        self.start_acceleration = geometry_msgs.msg.Vector3()
      if self.goal_position is None:
        self.goal_position = geometry_msgs.msg.Point()
      if self.goal_velocity is None:
        self.goal_velocity = geometry_msgs.msg.Vector3()
      if self.goal_acceleration is None:
        self.goal_acceleration = geometry_msgs.msg.Vector3()
      if self.enable_visualization is None:
        self.enable_visualization = False
    else:
      self.header = std_msgs.msg.Header()
      self.start_position = geometry_msgs.msg.Point()
      self.start_velocity = geometry_msgs.msg.Vector3()
      self.start_acceleration = geometry_msgs.msg.Vector3()
      self.goal_position = geometry_msgs.msg.Point()
      self.goal_velocity = geometry_msgs.msg.Vector3()
      self.goal_acceleration = geometry_msgs.msg.Vector3()
      self.enable_visualization = False

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_18dB().pack(_x.start_position.x, _x.start_position.y, _x.start_position.z, _x.start_velocity.x, _x.start_velocity.y, _x.start_velocity.z, _x.start_acceleration.x, _x.start_acceleration.y, _x.start_acceleration.z, _x.goal_position.x, _x.goal_position.y, _x.goal_position.z, _x.goal_velocity.x, _x.goal_velocity.y, _x.goal_velocity.z, _x.goal_acceleration.x, _x.goal_acceleration.y, _x.goal_acceleration.z, _x.enable_visualization))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.start_position is None:
        self.start_position = geometry_msgs.msg.Point()
      if self.start_velocity is None:
        self.start_velocity = geometry_msgs.msg.Vector3()
      if self.start_acceleration is None:
        self.start_acceleration = geometry_msgs.msg.Vector3()
      if self.goal_position is None:
        self.goal_position = geometry_msgs.msg.Point()
      if self.goal_velocity is None:
        self.goal_velocity = geometry_msgs.msg.Vector3()
      if self.goal_acceleration is None:
        self.goal_acceleration = geometry_msgs.msg.Vector3()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 145
      (_x.start_position.x, _x.start_position.y, _x.start_position.z, _x.start_velocity.x, _x.start_velocity.y, _x.start_velocity.z, _x.start_acceleration.x, _x.start_acceleration.y, _x.start_acceleration.z, _x.goal_position.x, _x.goal_position.y, _x.goal_position.z, _x.goal_velocity.x, _x.goal_velocity.y, _x.goal_velocity.z, _x.goal_acceleration.x, _x.goal_acceleration.y, _x.goal_acceleration.z, _x.enable_visualization,) = _get_struct_18dB().unpack(str[start:end])
      self.enable_visualization = bool(self.enable_visualization)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_18dB().pack(_x.start_position.x, _x.start_position.y, _x.start_position.z, _x.start_velocity.x, _x.start_velocity.y, _x.start_velocity.z, _x.start_acceleration.x, _x.start_acceleration.y, _x.start_acceleration.z, _x.goal_position.x, _x.goal_position.y, _x.goal_position.z, _x.goal_velocity.x, _x.goal_velocity.y, _x.goal_velocity.z, _x.goal_acceleration.x, _x.goal_acceleration.y, _x.goal_acceleration.z, _x.enable_visualization))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.start_position is None:
        self.start_position = geometry_msgs.msg.Point()
      if self.start_velocity is None:
        self.start_velocity = geometry_msgs.msg.Vector3()
      if self.start_acceleration is None:
        self.start_acceleration = geometry_msgs.msg.Vector3()
      if self.goal_position is None:
        self.goal_position = geometry_msgs.msg.Point()
      if self.goal_velocity is None:
        self.goal_velocity = geometry_msgs.msg.Vector3()
      if self.goal_acceleration is None:
        self.goal_acceleration = geometry_msgs.msg.Vector3()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 145
      (_x.start_position.x, _x.start_position.y, _x.start_position.z, _x.start_velocity.x, _x.start_velocity.y, _x.start_velocity.z, _x.start_acceleration.x, _x.start_acceleration.y, _x.start_acceleration.z, _x.goal_position.x, _x.goal_position.y, _x.goal_position.z, _x.goal_velocity.x, _x.goal_velocity.y, _x.goal_velocity.z, _x.goal_acceleration.x, _x.goal_acceleration.y, _x.goal_acceleration.z, _x.enable_visualization,) = _get_struct_18dB().unpack(str[start:end])
      self.enable_visualization = bool(self.enable_visualization)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_18dB = None
def _get_struct_18dB():
    global _struct_18dB
    if _struct_18dB is None:
        _struct_18dB = struct.Struct("<18dB")
    return _struct_18dB
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
