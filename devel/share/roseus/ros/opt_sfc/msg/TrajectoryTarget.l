;; Auto-generated. Do not edit!


(when (boundp 'opt_sfc::TrajectoryTarget)
  (if (not (find-package "OPT_SFC"))
    (make-package "OPT_SFC"))
  (shadow 'TrajectoryTarget (find-package "OPT_SFC")))
(unless (find-package "OPT_SFC::TRAJECTORYTARGET")
  (make-package "OPT_SFC::TRAJECTORYTARGET"))

(in-package "ROS")
;;//! \htmlinclude TrajectoryTarget.msg.html
(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))
(if (not (find-package "STD_MSGS"))
  (ros::roseus-add-msgs "std_msgs"))


(defclass opt_sfc::TrajectoryTarget
  :super ros::object
  :slots (_header _start_position _start_velocity _start_acceleration _goal_position _goal_velocity _goal_acceleration _enable_visualization ))

(defmethod opt_sfc::TrajectoryTarget
  (:init
   (&key
    ((:header __header) (instance std_msgs::Header :init))
    ((:start_position __start_position) (instance geometry_msgs::Point :init))
    ((:start_velocity __start_velocity) (instance geometry_msgs::Vector3 :init))
    ((:start_acceleration __start_acceleration) (instance geometry_msgs::Vector3 :init))
    ((:goal_position __goal_position) (instance geometry_msgs::Point :init))
    ((:goal_velocity __goal_velocity) (instance geometry_msgs::Vector3 :init))
    ((:goal_acceleration __goal_acceleration) (instance geometry_msgs::Vector3 :init))
    ((:enable_visualization __enable_visualization) nil)
    )
   (send-super :init)
   (setq _header __header)
   (setq _start_position __start_position)
   (setq _start_velocity __start_velocity)
   (setq _start_acceleration __start_acceleration)
   (setq _goal_position __goal_position)
   (setq _goal_velocity __goal_velocity)
   (setq _goal_acceleration __goal_acceleration)
   (setq _enable_visualization __enable_visualization)
   self)
  (:header
   (&rest __header)
   (if (keywordp (car __header))
       (send* _header __header)
     (progn
       (if __header (setq _header (car __header)))
       _header)))
  (:start_position
   (&rest __start_position)
   (if (keywordp (car __start_position))
       (send* _start_position __start_position)
     (progn
       (if __start_position (setq _start_position (car __start_position)))
       _start_position)))
  (:start_velocity
   (&rest __start_velocity)
   (if (keywordp (car __start_velocity))
       (send* _start_velocity __start_velocity)
     (progn
       (if __start_velocity (setq _start_velocity (car __start_velocity)))
       _start_velocity)))
  (:start_acceleration
   (&rest __start_acceleration)
   (if (keywordp (car __start_acceleration))
       (send* _start_acceleration __start_acceleration)
     (progn
       (if __start_acceleration (setq _start_acceleration (car __start_acceleration)))
       _start_acceleration)))
  (:goal_position
   (&rest __goal_position)
   (if (keywordp (car __goal_position))
       (send* _goal_position __goal_position)
     (progn
       (if __goal_position (setq _goal_position (car __goal_position)))
       _goal_position)))
  (:goal_velocity
   (&rest __goal_velocity)
   (if (keywordp (car __goal_velocity))
       (send* _goal_velocity __goal_velocity)
     (progn
       (if __goal_velocity (setq _goal_velocity (car __goal_velocity)))
       _goal_velocity)))
  (:goal_acceleration
   (&rest __goal_acceleration)
   (if (keywordp (car __goal_acceleration))
       (send* _goal_acceleration __goal_acceleration)
     (progn
       (if __goal_acceleration (setq _goal_acceleration (car __goal_acceleration)))
       _goal_acceleration)))
  (:enable_visualization
   (&optional (__enable_visualization :null))
   (if (not (eq __enable_visualization :null)) (setq _enable_visualization __enable_visualization)) _enable_visualization)
  (:serialization-length
   ()
   (+
    ;; std_msgs/Header _header
    (send _header :serialization-length)
    ;; geometry_msgs/Point _start_position
    (send _start_position :serialization-length)
    ;; geometry_msgs/Vector3 _start_velocity
    (send _start_velocity :serialization-length)
    ;; geometry_msgs/Vector3 _start_acceleration
    (send _start_acceleration :serialization-length)
    ;; geometry_msgs/Point _goal_position
    (send _goal_position :serialization-length)
    ;; geometry_msgs/Vector3 _goal_velocity
    (send _goal_velocity :serialization-length)
    ;; geometry_msgs/Vector3 _goal_acceleration
    (send _goal_acceleration :serialization-length)
    ;; bool _enable_visualization
    1
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; std_msgs/Header _header
       (send _header :serialize s)
     ;; geometry_msgs/Point _start_position
       (send _start_position :serialize s)
     ;; geometry_msgs/Vector3 _start_velocity
       (send _start_velocity :serialize s)
     ;; geometry_msgs/Vector3 _start_acceleration
       (send _start_acceleration :serialize s)
     ;; geometry_msgs/Point _goal_position
       (send _goal_position :serialize s)
     ;; geometry_msgs/Vector3 _goal_velocity
       (send _goal_velocity :serialize s)
     ;; geometry_msgs/Vector3 _goal_acceleration
       (send _goal_acceleration :serialize s)
     ;; bool _enable_visualization
       (if _enable_visualization (write-byte -1 s) (write-byte 0 s))
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; std_msgs/Header _header
     (send _header :deserialize buf ptr-) (incf ptr- (send _header :serialization-length))
   ;; geometry_msgs/Point _start_position
     (send _start_position :deserialize buf ptr-) (incf ptr- (send _start_position :serialization-length))
   ;; geometry_msgs/Vector3 _start_velocity
     (send _start_velocity :deserialize buf ptr-) (incf ptr- (send _start_velocity :serialization-length))
   ;; geometry_msgs/Vector3 _start_acceleration
     (send _start_acceleration :deserialize buf ptr-) (incf ptr- (send _start_acceleration :serialization-length))
   ;; geometry_msgs/Point _goal_position
     (send _goal_position :deserialize buf ptr-) (incf ptr- (send _goal_position :serialization-length))
   ;; geometry_msgs/Vector3 _goal_velocity
     (send _goal_velocity :deserialize buf ptr-) (incf ptr- (send _goal_velocity :serialization-length))
   ;; geometry_msgs/Vector3 _goal_acceleration
     (send _goal_acceleration :deserialize buf ptr-) (incf ptr- (send _goal_acceleration :serialization-length))
   ;; bool _enable_visualization
     (setq _enable_visualization (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;;
   self)
  )

(setf (get opt_sfc::TrajectoryTarget :md5sum-) "af6564869756c5cd87560435d26882f1")
(setf (get opt_sfc::TrajectoryTarget :datatype-) "opt_sfc/TrajectoryTarget")
(setf (get opt_sfc::TrajectoryTarget :definition-)
      "# TrajectoryTarget.msg
# 包含起点和终点的位置、速度、加速度信息

# 标准消息头
std_msgs/Header header

# 起点信息
geometry_msgs/Point start_position     # 起点位置
geometry_msgs/Vector3 start_velocity   # 起点速度
geometry_msgs/Vector3 start_acceleration  # 起点加速度

# 终点信息
geometry_msgs/Point goal_position      # 终点位置
geometry_msgs/Vector3 goal_velocity    # 终点速度
geometry_msgs/Vector3 goal_acceleration   # 终点加速度

# 可视化控制标志
bool enable_visualization              # 是否启用可视化
================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z
")



(provide :opt_sfc/TrajectoryTarget "af6564869756c5cd87560435d26882f1")


