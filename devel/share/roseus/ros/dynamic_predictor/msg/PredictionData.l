;; Auto-generated. Do not edit!


(when (boundp 'dynamic_predictor::PredictionData)
  (if (not (find-package "DYNAMIC_PREDICTOR"))
    (make-package "DYNAMIC_PREDICTOR"))
  (shadow 'PredictionData (find-package "DYNAMIC_PREDICTOR")))
(unless (find-package "DYNAMIC_PREDICTOR::PREDIC<PERSON>ONDATA")
  (make-package "DYNAMIC_PREDICTOR::PREDICTIONDATA"))

(in-package "ROS")
;;//! \htmlinclude PredictionData.msg.html
(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))
(if (not (find-package "STD_MSGS"))
  (ros::roseus-add-msgs "std_msgs"))


(defclass dynamic_predictor::PredictionData
  :super ros::object
  :slots (_header _num_obstacles _num_intents _num_time_steps _intent_probs_data _pos_pred_data _size_pred_data ))

(defmethod dynamic_predictor::PredictionData
  (:init
   (&key
    ((:header __header) (instance std_msgs::Header :init))
    ((:num_obstacles __num_obstacles) 0)
    ((:num_intents __num_intents) 0)
    ((:num_time_steps __num_time_steps) 0)
    ((:intent_probs_data __intent_probs_data) (make-array 0 :initial-element 0.0 :element-type :float))
    ((:pos_pred_data __pos_pred_data) ())
    ((:size_pred_data __size_pred_data) ())
    )
   (send-super :init)
   (setq _header __header)
   (setq _num_obstacles (round __num_obstacles))
   (setq _num_intents (round __num_intents))
   (setq _num_time_steps (round __num_time_steps))
   (setq _intent_probs_data __intent_probs_data)
   (setq _pos_pred_data __pos_pred_data)
   (setq _size_pred_data __size_pred_data)
   self)
  (:header
   (&rest __header)
   (if (keywordp (car __header))
       (send* _header __header)
     (progn
       (if __header (setq _header (car __header)))
       _header)))
  (:num_obstacles
   (&optional __num_obstacles)
   (if __num_obstacles (setq _num_obstacles __num_obstacles)) _num_obstacles)
  (:num_intents
   (&optional __num_intents)
   (if __num_intents (setq _num_intents __num_intents)) _num_intents)
  (:num_time_steps
   (&optional __num_time_steps)
   (if __num_time_steps (setq _num_time_steps __num_time_steps)) _num_time_steps)
  (:intent_probs_data
   (&optional __intent_probs_data)
   (if __intent_probs_data (setq _intent_probs_data __intent_probs_data)) _intent_probs_data)
  (:pos_pred_data
   (&rest __pos_pred_data)
   (if (keywordp (car __pos_pred_data))
       (send* _pos_pred_data __pos_pred_data)
     (progn
       (if __pos_pred_data (setq _pos_pred_data (car __pos_pred_data)))
       _pos_pred_data)))
  (:size_pred_data
   (&rest __size_pred_data)
   (if (keywordp (car __size_pred_data))
       (send* _size_pred_data __size_pred_data)
     (progn
       (if __size_pred_data (setq _size_pred_data (car __size_pred_data)))
       _size_pred_data)))
  (:serialization-length
   ()
   (+
    ;; std_msgs/Header _header
    (send _header :serialization-length)
    ;; int32 _num_obstacles
    4
    ;; int32 _num_intents
    4
    ;; int32 _num_time_steps
    4
    ;; float64[] _intent_probs_data
    (* 8    (length _intent_probs_data)) 4
    ;; geometry_msgs/Point[] _pos_pred_data
    (apply #'+ (send-all _pos_pred_data :serialization-length)) 4
    ;; geometry_msgs/Point[] _size_pred_data
    (apply #'+ (send-all _size_pred_data :serialization-length)) 4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; std_msgs/Header _header
       (send _header :serialize s)
     ;; int32 _num_obstacles
       (write-long _num_obstacles s)
     ;; int32 _num_intents
       (write-long _num_intents s)
     ;; int32 _num_time_steps
       (write-long _num_time_steps s)
     ;; float64[] _intent_probs_data
     (write-long (length _intent_probs_data) s)
     (dotimes (i (length _intent_probs_data))
       (sys::poke (elt _intent_probs_data i) (send s :buffer) (send s :count) :double) (incf (stream-count s) 8)
       )
     ;; geometry_msgs/Point[] _pos_pred_data
     (write-long (length _pos_pred_data) s)
     (dolist (elem _pos_pred_data)
       (send elem :serialize s)
       )
     ;; geometry_msgs/Point[] _size_pred_data
     (write-long (length _size_pred_data) s)
     (dolist (elem _size_pred_data)
       (send elem :serialize s)
       )
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; std_msgs/Header _header
     (send _header :deserialize buf ptr-) (incf ptr- (send _header :serialization-length))
   ;; int32 _num_obstacles
     (setq _num_obstacles (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; int32 _num_intents
     (setq _num_intents (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; int32 _num_time_steps
     (setq _num_time_steps (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; float64[] _intent_probs_data
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _intent_probs_data (instantiate float-vector n))
     (dotimes (i n)
     (setf (elt _intent_probs_data i) (sys::peek buf ptr- :double)) (incf ptr- 8)
     ))
   ;; geometry_msgs/Point[] _pos_pred_data
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _pos_pred_data (let (r) (dotimes (i n) (push (instance geometry_msgs::Point :init) r)) r))
     (dolist (elem- _pos_pred_data)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;; geometry_msgs/Point[] _size_pred_data
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _size_pred_data (let (r) (dotimes (i n) (push (instance geometry_msgs::Point :init) r)) r))
     (dolist (elem- _size_pred_data)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;;
   self)
  )

(setf (get dynamic_predictor::PredictionData :md5sum-) "51a1997e68e7a493e21155880d375c24")
(setf (get dynamic_predictor::PredictionData :datatype-) "dynamic_predictor/PredictionData")
(setf (get dynamic_predictor::PredictionData :definition-)
      "# 动态障碍物预测数据消息
# 对应C++数据结构：
# - std::vector<Eigen::VectorXd> intentProb_
# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_

std_msgs/Header header

# ========== 数据维度信息 ==========
int32 num_obstacles          # 障碍物数量
int32 num_intents           # 意图数量 (固定为4: FORWARD=0, LEFT=1, RIGHT=2, STOP=3)
int32 num_time_steps        # 预测时间步数

# ========== 意图概率数据 ==========
# 对应: std::vector<Eigen::VectorXd> intentProb_
# 数据排列: [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, obs1_intent1, ...]
# 访问方式: prob = intent_probs_data[obstacle_idx * num_intents + intent_idx]
float64[] intent_probs_data

# ========== 位置预测数据 ==========
# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
# 访问方式: pos = pos_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
geometry_msgs/Point[] pos_pred_data

# ========== 尺寸预测数据 ==========
# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_
# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
# 访问方式: size = size_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
geometry_msgs/Point[] size_pred_data 
================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

")



(provide :dynamic_predictor/PredictionData "51a1997e68e7a493e21155880d375c24")


