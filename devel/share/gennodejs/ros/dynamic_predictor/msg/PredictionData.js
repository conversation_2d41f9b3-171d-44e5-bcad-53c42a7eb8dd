// Auto-generated. Do not edit!

// (in-package dynamic_predictor.msg)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let geometry_msgs = _finder('geometry_msgs');
let std_msgs = _finder('std_msgs');

//-----------------------------------------------------------

class PredictionData {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.header = null;
      this.num_obstacles = null;
      this.num_intents = null;
      this.num_time_steps = null;
      this.intent_probs_data = null;
      this.pos_pred_data = null;
      this.size_pred_data = null;
    }
    else {
      if (initObj.hasOwnProperty('header')) {
        this.header = initObj.header
      }
      else {
        this.header = new std_msgs.msg.Header();
      }
      if (initObj.hasOwnProperty('num_obstacles')) {
        this.num_obstacles = initObj.num_obstacles
      }
      else {
        this.num_obstacles = 0;
      }
      if (initObj.hasOwnProperty('num_intents')) {
        this.num_intents = initObj.num_intents
      }
      else {
        this.num_intents = 0;
      }
      if (initObj.hasOwnProperty('num_time_steps')) {
        this.num_time_steps = initObj.num_time_steps
      }
      else {
        this.num_time_steps = 0;
      }
      if (initObj.hasOwnProperty('intent_probs_data')) {
        this.intent_probs_data = initObj.intent_probs_data
      }
      else {
        this.intent_probs_data = [];
      }
      if (initObj.hasOwnProperty('pos_pred_data')) {
        this.pos_pred_data = initObj.pos_pred_data
      }
      else {
        this.pos_pred_data = [];
      }
      if (initObj.hasOwnProperty('size_pred_data')) {
        this.size_pred_data = initObj.size_pred_data
      }
      else {
        this.size_pred_data = [];
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type PredictionData
    // Serialize message field [header]
    bufferOffset = std_msgs.msg.Header.serialize(obj.header, buffer, bufferOffset);
    // Serialize message field [num_obstacles]
    bufferOffset = _serializer.int32(obj.num_obstacles, buffer, bufferOffset);
    // Serialize message field [num_intents]
    bufferOffset = _serializer.int32(obj.num_intents, buffer, bufferOffset);
    // Serialize message field [num_time_steps]
    bufferOffset = _serializer.int32(obj.num_time_steps, buffer, bufferOffset);
    // Serialize message field [intent_probs_data]
    bufferOffset = _arraySerializer.float64(obj.intent_probs_data, buffer, bufferOffset, null);
    // Serialize message field [pos_pred_data]
    // Serialize the length for message field [pos_pred_data]
    bufferOffset = _serializer.uint32(obj.pos_pred_data.length, buffer, bufferOffset);
    obj.pos_pred_data.forEach((val) => {
      bufferOffset = geometry_msgs.msg.Point.serialize(val, buffer, bufferOffset);
    });
    // Serialize message field [size_pred_data]
    // Serialize the length for message field [size_pred_data]
    bufferOffset = _serializer.uint32(obj.size_pred_data.length, buffer, bufferOffset);
    obj.size_pred_data.forEach((val) => {
      bufferOffset = geometry_msgs.msg.Point.serialize(val, buffer, bufferOffset);
    });
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type PredictionData
    let len;
    let data = new PredictionData(null);
    // Deserialize message field [header]
    data.header = std_msgs.msg.Header.deserialize(buffer, bufferOffset);
    // Deserialize message field [num_obstacles]
    data.num_obstacles = _deserializer.int32(buffer, bufferOffset);
    // Deserialize message field [num_intents]
    data.num_intents = _deserializer.int32(buffer, bufferOffset);
    // Deserialize message field [num_time_steps]
    data.num_time_steps = _deserializer.int32(buffer, bufferOffset);
    // Deserialize message field [intent_probs_data]
    data.intent_probs_data = _arrayDeserializer.float64(buffer, bufferOffset, null)
    // Deserialize message field [pos_pred_data]
    // Deserialize array length for message field [pos_pred_data]
    len = _deserializer.uint32(buffer, bufferOffset);
    data.pos_pred_data = new Array(len);
    for (let i = 0; i < len; ++i) {
      data.pos_pred_data[i] = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset)
    }
    // Deserialize message field [size_pred_data]
    // Deserialize array length for message field [size_pred_data]
    len = _deserializer.uint32(buffer, bufferOffset);
    data.size_pred_data = new Array(len);
    for (let i = 0; i < len; ++i) {
      data.size_pred_data[i] = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset)
    }
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += std_msgs.msg.Header.getMessageSize(object.header);
    length += 8 * object.intent_probs_data.length;
    length += 24 * object.pos_pred_data.length;
    length += 24 * object.size_pred_data.length;
    return length + 24;
  }

  static datatype() {
    // Returns string type for a message object
    return 'dynamic_predictor/PredictionData';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '51a1997e68e7a493e21155880d375c24';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 动态障碍物预测数据消息
    # 对应C++数据结构：
    # - std::vector<Eigen::VectorXd> intentProb_
    # - std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
    # - std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_
    
    std_msgs/Header header
    
    # ========== 数据维度信息 ==========
    int32 num_obstacles          # 障碍物数量
    int32 num_intents           # 意图数量 (固定为4: FORWARD=0, LEFT=1, RIGHT=2, STOP=3)
    int32 num_time_steps        # 预测时间步数
    
    # ========== 意图概率数据 ==========
    # 对应: std::vector<Eigen::VectorXd> intentProb_
    # 数据排列: [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, obs1_intent1, ...]
    # 访问方式: prob = intent_probs_data[obstacle_idx * num_intents + intent_idx]
    float64[] intent_probs_data
    
    # ========== 位置预测数据 ==========
    # 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_
    # 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
    # 访问方式: pos = pos_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
    geometry_msgs/Point[] pos_pred_data
    
    # ========== 尺寸预测数据 ==========
    # 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_
    # 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]
    # 访问方式: size = size_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]
    geometry_msgs/Point[] size_pred_data 
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new PredictionData(null);
    if (msg.header !== undefined) {
      resolved.header = std_msgs.msg.Header.Resolve(msg.header)
    }
    else {
      resolved.header = new std_msgs.msg.Header()
    }

    if (msg.num_obstacles !== undefined) {
      resolved.num_obstacles = msg.num_obstacles;
    }
    else {
      resolved.num_obstacles = 0
    }

    if (msg.num_intents !== undefined) {
      resolved.num_intents = msg.num_intents;
    }
    else {
      resolved.num_intents = 0
    }

    if (msg.num_time_steps !== undefined) {
      resolved.num_time_steps = msg.num_time_steps;
    }
    else {
      resolved.num_time_steps = 0
    }

    if (msg.intent_probs_data !== undefined) {
      resolved.intent_probs_data = msg.intent_probs_data;
    }
    else {
      resolved.intent_probs_data = []
    }

    if (msg.pos_pred_data !== undefined) {
      resolved.pos_pred_data = new Array(msg.pos_pred_data.length);
      for (let i = 0; i < resolved.pos_pred_data.length; ++i) {
        resolved.pos_pred_data[i] = geometry_msgs.msg.Point.Resolve(msg.pos_pred_data[i]);
      }
    }
    else {
      resolved.pos_pred_data = []
    }

    if (msg.size_pred_data !== undefined) {
      resolved.size_pred_data = new Array(msg.size_pred_data.length);
      for (let i = 0; i < resolved.size_pred_data.length; ++i) {
        resolved.size_pred_data[i] = geometry_msgs.msg.Point.Resolve(msg.size_pred_data[i]);
      }
    }
    else {
      resolved.size_pred_data = []
    }

    return resolved;
    }
};

module.exports = PredictionData;
