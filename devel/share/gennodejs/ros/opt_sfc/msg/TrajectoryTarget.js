// Auto-generated. Do not edit!

// (in-package opt_sfc.msg)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let std_msgs = _finder('std_msgs');
let geometry_msgs = _finder('geometry_msgs');

//-----------------------------------------------------------

class TrajectoryTarget {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.header = null;
      this.start_position = null;
      this.start_velocity = null;
      this.start_acceleration = null;
      this.goal_position = null;
      this.goal_velocity = null;
      this.goal_acceleration = null;
      this.enable_visualization = null;
    }
    else {
      if (initObj.hasOwnProperty('header')) {
        this.header = initObj.header
      }
      else {
        this.header = new std_msgs.msg.Header();
      }
      if (initObj.hasOwnProperty('start_position')) {
        this.start_position = initObj.start_position
      }
      else {
        this.start_position = new geometry_msgs.msg.Point();
      }
      if (initObj.hasOwnProperty('start_velocity')) {
        this.start_velocity = initObj.start_velocity
      }
      else {
        this.start_velocity = new geometry_msgs.msg.Vector3();
      }
      if (initObj.hasOwnProperty('start_acceleration')) {
        this.start_acceleration = initObj.start_acceleration
      }
      else {
        this.start_acceleration = new geometry_msgs.msg.Vector3();
      }
      if (initObj.hasOwnProperty('goal_position')) {
        this.goal_position = initObj.goal_position
      }
      else {
        this.goal_position = new geometry_msgs.msg.Point();
      }
      if (initObj.hasOwnProperty('goal_velocity')) {
        this.goal_velocity = initObj.goal_velocity
      }
      else {
        this.goal_velocity = new geometry_msgs.msg.Vector3();
      }
      if (initObj.hasOwnProperty('goal_acceleration')) {
        this.goal_acceleration = initObj.goal_acceleration
      }
      else {
        this.goal_acceleration = new geometry_msgs.msg.Vector3();
      }
      if (initObj.hasOwnProperty('enable_visualization')) {
        this.enable_visualization = initObj.enable_visualization
      }
      else {
        this.enable_visualization = false;
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type TrajectoryTarget
    // Serialize message field [header]
    bufferOffset = std_msgs.msg.Header.serialize(obj.header, buffer, bufferOffset);
    // Serialize message field [start_position]
    bufferOffset = geometry_msgs.msg.Point.serialize(obj.start_position, buffer, bufferOffset);
    // Serialize message field [start_velocity]
    bufferOffset = geometry_msgs.msg.Vector3.serialize(obj.start_velocity, buffer, bufferOffset);
    // Serialize message field [start_acceleration]
    bufferOffset = geometry_msgs.msg.Vector3.serialize(obj.start_acceleration, buffer, bufferOffset);
    // Serialize message field [goal_position]
    bufferOffset = geometry_msgs.msg.Point.serialize(obj.goal_position, buffer, bufferOffset);
    // Serialize message field [goal_velocity]
    bufferOffset = geometry_msgs.msg.Vector3.serialize(obj.goal_velocity, buffer, bufferOffset);
    // Serialize message field [goal_acceleration]
    bufferOffset = geometry_msgs.msg.Vector3.serialize(obj.goal_acceleration, buffer, bufferOffset);
    // Serialize message field [enable_visualization]
    bufferOffset = _serializer.bool(obj.enable_visualization, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type TrajectoryTarget
    let len;
    let data = new TrajectoryTarget(null);
    // Deserialize message field [header]
    data.header = std_msgs.msg.Header.deserialize(buffer, bufferOffset);
    // Deserialize message field [start_position]
    data.start_position = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset);
    // Deserialize message field [start_velocity]
    data.start_velocity = geometry_msgs.msg.Vector3.deserialize(buffer, bufferOffset);
    // Deserialize message field [start_acceleration]
    data.start_acceleration = geometry_msgs.msg.Vector3.deserialize(buffer, bufferOffset);
    // Deserialize message field [goal_position]
    data.goal_position = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset);
    // Deserialize message field [goal_velocity]
    data.goal_velocity = geometry_msgs.msg.Vector3.deserialize(buffer, bufferOffset);
    // Deserialize message field [goal_acceleration]
    data.goal_acceleration = geometry_msgs.msg.Vector3.deserialize(buffer, bufferOffset);
    // Deserialize message field [enable_visualization]
    data.enable_visualization = _deserializer.bool(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += std_msgs.msg.Header.getMessageSize(object.header);
    return length + 145;
  }

  static datatype() {
    // Returns string type for a message object
    return 'opt_sfc/TrajectoryTarget';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'af6564869756c5cd87560435d26882f1';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # TrajectoryTarget.msg
    # 包含起点和终点的位置、速度、加速度信息
    
    # 标准消息头
    std_msgs/Header header
    
    # 起点信息
    geometry_msgs/Point start_position     # 起点位置
    geometry_msgs/Vector3 start_velocity   # 起点速度
    geometry_msgs/Vector3 start_acceleration  # 起点加速度
    
    # 终点信息
    geometry_msgs/Point goal_position      # 终点位置
    geometry_msgs/Vector3 goal_velocity    # 终点速度
    geometry_msgs/Vector3 goal_acceleration   # 终点加速度
    
    # 可视化控制标志
    bool enable_visualization              # 是否启用可视化
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Vector3
    # This represents a vector in free space. 
    # It is only meant to represent a direction. Therefore, it does not
    # make sense to apply a translation to it (e.g., when applying a 
    # generic rigid transformation to a Vector3, tf2 will only apply the
    # rotation). If you want your data to be translatable too, use the
    # geometry_msgs/Point message instead.
    
    float64 x
    float64 y
    float64 z
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new TrajectoryTarget(null);
    if (msg.header !== undefined) {
      resolved.header = std_msgs.msg.Header.Resolve(msg.header)
    }
    else {
      resolved.header = new std_msgs.msg.Header()
    }

    if (msg.start_position !== undefined) {
      resolved.start_position = geometry_msgs.msg.Point.Resolve(msg.start_position)
    }
    else {
      resolved.start_position = new geometry_msgs.msg.Point()
    }

    if (msg.start_velocity !== undefined) {
      resolved.start_velocity = geometry_msgs.msg.Vector3.Resolve(msg.start_velocity)
    }
    else {
      resolved.start_velocity = new geometry_msgs.msg.Vector3()
    }

    if (msg.start_acceleration !== undefined) {
      resolved.start_acceleration = geometry_msgs.msg.Vector3.Resolve(msg.start_acceleration)
    }
    else {
      resolved.start_acceleration = new geometry_msgs.msg.Vector3()
    }

    if (msg.goal_position !== undefined) {
      resolved.goal_position = geometry_msgs.msg.Point.Resolve(msg.goal_position)
    }
    else {
      resolved.goal_position = new geometry_msgs.msg.Point()
    }

    if (msg.goal_velocity !== undefined) {
      resolved.goal_velocity = geometry_msgs.msg.Vector3.Resolve(msg.goal_velocity)
    }
    else {
      resolved.goal_velocity = new geometry_msgs.msg.Vector3()
    }

    if (msg.goal_acceleration !== undefined) {
      resolved.goal_acceleration = geometry_msgs.msg.Vector3.Resolve(msg.goal_acceleration)
    }
    else {
      resolved.goal_acceleration = new geometry_msgs.msg.Vector3()
    }

    if (msg.enable_visualization !== undefined) {
      resolved.enable_visualization = msg.enable_visualization;
    }
    else {
      resolved.enable_visualization = false
    }

    return resolved;
    }
};

module.exports = TrajectoryTarget;
