; Auto-generated. Do not edit!


(cl:in-package dynamic_predictor-msg)


;//! \htmlinclude PredictionData.msg.html

(cl:defclass <PredictionData> (roslisp-msg-protocol:ros-message)
  ((header
    :reader header
    :initarg :header
    :type std_msgs-msg:Header
    :initform (cl:make-instance 'std_msgs-msg:Header))
   (num_obstacles
    :reader num_obstacles
    :initarg :num_obstacles
    :type cl:integer
    :initform 0)
   (num_intents
    :reader num_intents
    :initarg :num_intents
    :type cl:integer
    :initform 0)
   (num_time_steps
    :reader num_time_steps
    :initarg :num_time_steps
    :type cl:integer
    :initform 0)
   (intent_probs_data
    :reader intent_probs_data
    :initarg :intent_probs_data
    :type (cl:vector cl:float)
   :initform (cl:make-array 0 :element-type 'cl:float :initial-element 0.0))
   (pos_pred_data
    :reader pos_pred_data
    :initarg :pos_pred_data
    :type (cl:vector geometry_msgs-msg:Point)
   :initform (cl:make-array 0 :element-type 'geometry_msgs-msg:Point :initial-element (cl:make-instance 'geometry_msgs-msg:Point)))
   (size_pred_data
    :reader size_pred_data
    :initarg :size_pred_data
    :type (cl:vector geometry_msgs-msg:Point)
   :initform (cl:make-array 0 :element-type 'geometry_msgs-msg:Point :initial-element (cl:make-instance 'geometry_msgs-msg:Point))))
)

(cl:defclass PredictionData (<PredictionData>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <PredictionData>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'PredictionData)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name dynamic_predictor-msg:<PredictionData> is deprecated: use dynamic_predictor-msg:PredictionData instead.")))

(cl:ensure-generic-function 'header-val :lambda-list '(m))
(cl:defmethod header-val ((m <PredictionData>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader dynamic_predictor-msg:header-val is deprecated.  Use dynamic_predictor-msg:header instead.")
  (header m))

(cl:ensure-generic-function 'num_obstacles-val :lambda-list '(m))
(cl:defmethod num_obstacles-val ((m <PredictionData>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader dynamic_predictor-msg:num_obstacles-val is deprecated.  Use dynamic_predictor-msg:num_obstacles instead.")
  (num_obstacles m))

(cl:ensure-generic-function 'num_intents-val :lambda-list '(m))
(cl:defmethod num_intents-val ((m <PredictionData>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader dynamic_predictor-msg:num_intents-val is deprecated.  Use dynamic_predictor-msg:num_intents instead.")
  (num_intents m))

(cl:ensure-generic-function 'num_time_steps-val :lambda-list '(m))
(cl:defmethod num_time_steps-val ((m <PredictionData>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader dynamic_predictor-msg:num_time_steps-val is deprecated.  Use dynamic_predictor-msg:num_time_steps instead.")
  (num_time_steps m))

(cl:ensure-generic-function 'intent_probs_data-val :lambda-list '(m))
(cl:defmethod intent_probs_data-val ((m <PredictionData>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader dynamic_predictor-msg:intent_probs_data-val is deprecated.  Use dynamic_predictor-msg:intent_probs_data instead.")
  (intent_probs_data m))

(cl:ensure-generic-function 'pos_pred_data-val :lambda-list '(m))
(cl:defmethod pos_pred_data-val ((m <PredictionData>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader dynamic_predictor-msg:pos_pred_data-val is deprecated.  Use dynamic_predictor-msg:pos_pred_data instead.")
  (pos_pred_data m))

(cl:ensure-generic-function 'size_pred_data-val :lambda-list '(m))
(cl:defmethod size_pred_data-val ((m <PredictionData>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader dynamic_predictor-msg:size_pred_data-val is deprecated.  Use dynamic_predictor-msg:size_pred_data instead.")
  (size_pred_data m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <PredictionData>) ostream)
  "Serializes a message object of type '<PredictionData>"
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'header) ostream)
  (cl:let* ((signed (cl:slot-value msg 'num_obstacles)) (unsigned (cl:if (cl:< signed 0) (cl:+ signed 4294967296) signed)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) unsigned) ostream)
    )
  (cl:let* ((signed (cl:slot-value msg 'num_intents)) (unsigned (cl:if (cl:< signed 0) (cl:+ signed 4294967296) signed)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) unsigned) ostream)
    )
  (cl:let* ((signed (cl:slot-value msg 'num_time_steps)) (unsigned (cl:if (cl:< signed 0) (cl:+ signed 4294967296) signed)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) unsigned) ostream)
    )
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'intent_probs_data))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((bits (roslisp-utils:encode-double-float-bits ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 32) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 40) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 48) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 56) bits) ostream)))
   (cl:slot-value msg 'intent_probs_data))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'pos_pred_data))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (roslisp-msg-protocol:serialize ele ostream))
   (cl:slot-value msg 'pos_pred_data))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'size_pred_data))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (roslisp-msg-protocol:serialize ele ostream))
   (cl:slot-value msg 'size_pred_data))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <PredictionData>) istream)
  "Deserializes a message object of type '<PredictionData>"
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'header) istream)
    (cl:let ((unsigned 0))
      (cl:setf (cl:ldb (cl:byte 8 0) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) unsigned) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'num_obstacles) (cl:if (cl:< unsigned 2147483648) unsigned (cl:- unsigned 4294967296))))
    (cl:let ((unsigned 0))
      (cl:setf (cl:ldb (cl:byte 8 0) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) unsigned) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'num_intents) (cl:if (cl:< unsigned 2147483648) unsigned (cl:- unsigned 4294967296))))
    (cl:let ((unsigned 0))
      (cl:setf (cl:ldb (cl:byte 8 0) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) unsigned) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'num_time_steps) (cl:if (cl:< unsigned 2147483648) unsigned (cl:- unsigned 4294967296))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'intent_probs_data) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'intent_probs_data)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 32) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 40) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 48) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 56) bits) (cl:read-byte istream))
    (cl:setf (cl:aref vals i) (roslisp-utils:decode-double-float-bits bits))))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'pos_pred_data) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'pos_pred_data)))
    (cl:dotimes (i __ros_arr_len)
    (cl:setf (cl:aref vals i) (cl:make-instance 'geometry_msgs-msg:Point))
  (roslisp-msg-protocol:deserialize (cl:aref vals i) istream))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'size_pred_data) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'size_pred_data)))
    (cl:dotimes (i __ros_arr_len)
    (cl:setf (cl:aref vals i) (cl:make-instance 'geometry_msgs-msg:Point))
  (roslisp-msg-protocol:deserialize (cl:aref vals i) istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<PredictionData>)))
  "Returns string type for a message object of type '<PredictionData>"
  "dynamic_predictor/PredictionData")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'PredictionData)))
  "Returns string type for a message object of type 'PredictionData"
  "dynamic_predictor/PredictionData")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<PredictionData>)))
  "Returns md5sum for a message object of type '<PredictionData>"
  "51a1997e68e7a493e21155880d375c24")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'PredictionData)))
  "Returns md5sum for a message object of type 'PredictionData"
  "51a1997e68e7a493e21155880d375c24")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<PredictionData>)))
  "Returns full string definition for message of type '<PredictionData>"
  (cl:format cl:nil "# 动态障碍物预测数据消息~%# 对应C++数据结构：~%# - std::vector<Eigen::VectorXd> intentProb_~%# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_~%# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_~%~%std_msgs/Header header~%~%# ========== 数据维度信息 ==========~%int32 num_obstacles          # 障碍物数量~%int32 num_intents           # 意图数量 (固定为4: FORWARD=0, LEFT=1, RIGHT=2, STOP=3)~%int32 num_time_steps        # 预测时间步数~%~%# ========== 意图概率数据 ==========~%# 对应: std::vector<Eigen::VectorXd> intentProb_~%# 数据排列: [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, obs1_intent1, ...]~%# 访问方式: prob = intent_probs_data[obstacle_idx * num_intents + intent_idx]~%float64[] intent_probs_data~%~%# ========== 位置预测数据 ==========~%# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_~%# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]~%# 访问方式: pos = pos_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]~%geometry_msgs/Point[] pos_pred_data~%~%# ========== 尺寸预测数据 ==========~%# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_~%# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]~%# 访问方式: size = size_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]~%geometry_msgs/Point[] size_pred_data ~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'PredictionData)))
  "Returns full string definition for message of type 'PredictionData"
  (cl:format cl:nil "# 动态障碍物预测数据消息~%# 对应C++数据结构：~%# - std::vector<Eigen::VectorXd> intentProb_~%# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_~%# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_~%~%std_msgs/Header header~%~%# ========== 数据维度信息 ==========~%int32 num_obstacles          # 障碍物数量~%int32 num_intents           # 意图数量 (固定为4: FORWARD=0, LEFT=1, RIGHT=2, STOP=3)~%int32 num_time_steps        # 预测时间步数~%~%# ========== 意图概率数据 ==========~%# 对应: std::vector<Eigen::VectorXd> intentProb_~%# 数据排列: [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, obs1_intent1, ...]~%# 访问方式: prob = intent_probs_data[obstacle_idx * num_intents + intent_idx]~%float64[] intent_probs_data~%~%# ========== 位置预测数据 ==========~%# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_~%# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]~%# 访问方式: pos = pos_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]~%geometry_msgs/Point[] pos_pred_data~%~%# ========== 尺寸预测数据 ==========~%# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_~%# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]~%# 访问方式: size = size_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]~%geometry_msgs/Point[] size_pred_data ~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <PredictionData>))
  (cl:+ 0
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'header))
     4
     4
     4
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'intent_probs_data) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 8)))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'pos_pred_data) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ (roslisp-msg-protocol:serialization-length ele))))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'size_pred_data) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ (roslisp-msg-protocol:serialization-length ele))))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <PredictionData>))
  "Converts a ROS message object to a list"
  (cl:list 'PredictionData
    (cl:cons ':header (header msg))
    (cl:cons ':num_obstacles (num_obstacles msg))
    (cl:cons ':num_intents (num_intents msg))
    (cl:cons ':num_time_steps (num_time_steps msg))
    (cl:cons ':intent_probs_data (intent_probs_data msg))
    (cl:cons ':pos_pred_data (pos_pred_data msg))
    (cl:cons ':size_pred_data (size_pred_data msg))
))
