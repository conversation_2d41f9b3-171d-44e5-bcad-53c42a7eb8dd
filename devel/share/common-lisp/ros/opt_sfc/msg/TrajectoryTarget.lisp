; Auto-generated. Do not edit!


(cl:in-package opt_sfc-msg)


;//! \htmlinclude TrajectoryTarget.msg.html

(cl:defclass <TrajectoryTarget> (roslisp-msg-protocol:ros-message)
  ((header
    :reader header
    :initarg :header
    :type std_msgs-msg:Header
    :initform (cl:make-instance 'std_msgs-msg:Header))
   (start_position
    :reader start_position
    :initarg :start_position
    :type geometry_msgs-msg:Point
    :initform (cl:make-instance 'geometry_msgs-msg:Point))
   (start_velocity
    :reader start_velocity
    :initarg :start_velocity
    :type geometry_msgs-msg:Vector3
    :initform (cl:make-instance 'geometry_msgs-msg:Vector3))
   (start_acceleration
    :reader start_acceleration
    :initarg :start_acceleration
    :type geometry_msgs-msg:Vector3
    :initform (cl:make-instance 'geometry_msgs-msg:Vector3))
   (goal_position
    :reader goal_position
    :initarg :goal_position
    :type geometry_msgs-msg:Point
    :initform (cl:make-instance 'geometry_msgs-msg:Point))
   (goal_velocity
    :reader goal_velocity
    :initarg :goal_velocity
    :type geometry_msgs-msg:Vector3
    :initform (cl:make-instance 'geometry_msgs-msg:Vector3))
   (goal_acceleration
    :reader goal_acceleration
    :initarg :goal_acceleration
    :type geometry_msgs-msg:Vector3
    :initform (cl:make-instance 'geometry_msgs-msg:Vector3))
   (enable_visualization
    :reader enable_visualization
    :initarg :enable_visualization
    :type cl:boolean
    :initform cl:nil))
)

(cl:defclass TrajectoryTarget (<TrajectoryTarget>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <TrajectoryTarget>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'TrajectoryTarget)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name opt_sfc-msg:<TrajectoryTarget> is deprecated: use opt_sfc-msg:TrajectoryTarget instead.")))

(cl:ensure-generic-function 'header-val :lambda-list '(m))
(cl:defmethod header-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:header-val is deprecated.  Use opt_sfc-msg:header instead.")
  (header m))

(cl:ensure-generic-function 'start_position-val :lambda-list '(m))
(cl:defmethod start_position-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:start_position-val is deprecated.  Use opt_sfc-msg:start_position instead.")
  (start_position m))

(cl:ensure-generic-function 'start_velocity-val :lambda-list '(m))
(cl:defmethod start_velocity-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:start_velocity-val is deprecated.  Use opt_sfc-msg:start_velocity instead.")
  (start_velocity m))

(cl:ensure-generic-function 'start_acceleration-val :lambda-list '(m))
(cl:defmethod start_acceleration-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:start_acceleration-val is deprecated.  Use opt_sfc-msg:start_acceleration instead.")
  (start_acceleration m))

(cl:ensure-generic-function 'goal_position-val :lambda-list '(m))
(cl:defmethod goal_position-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:goal_position-val is deprecated.  Use opt_sfc-msg:goal_position instead.")
  (goal_position m))

(cl:ensure-generic-function 'goal_velocity-val :lambda-list '(m))
(cl:defmethod goal_velocity-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:goal_velocity-val is deprecated.  Use opt_sfc-msg:goal_velocity instead.")
  (goal_velocity m))

(cl:ensure-generic-function 'goal_acceleration-val :lambda-list '(m))
(cl:defmethod goal_acceleration-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:goal_acceleration-val is deprecated.  Use opt_sfc-msg:goal_acceleration instead.")
  (goal_acceleration m))

(cl:ensure-generic-function 'enable_visualization-val :lambda-list '(m))
(cl:defmethod enable_visualization-val ((m <TrajectoryTarget>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader opt_sfc-msg:enable_visualization-val is deprecated.  Use opt_sfc-msg:enable_visualization instead.")
  (enable_visualization m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <TrajectoryTarget>) ostream)
  "Serializes a message object of type '<TrajectoryTarget>"
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'header) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'start_position) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'start_velocity) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'start_acceleration) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'goal_position) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'goal_velocity) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'goal_acceleration) ostream)
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'enable_visualization) 1 0)) ostream)
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <TrajectoryTarget>) istream)
  "Deserializes a message object of type '<TrajectoryTarget>"
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'header) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'start_position) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'start_velocity) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'start_acceleration) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'goal_position) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'goal_velocity) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'goal_acceleration) istream)
    (cl:setf (cl:slot-value msg 'enable_visualization) (cl:not (cl:zerop (cl:read-byte istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<TrajectoryTarget>)))
  "Returns string type for a message object of type '<TrajectoryTarget>"
  "opt_sfc/TrajectoryTarget")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'TrajectoryTarget)))
  "Returns string type for a message object of type 'TrajectoryTarget"
  "opt_sfc/TrajectoryTarget")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<TrajectoryTarget>)))
  "Returns md5sum for a message object of type '<TrajectoryTarget>"
  "af6564869756c5cd87560435d26882f1")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'TrajectoryTarget)))
  "Returns md5sum for a message object of type 'TrajectoryTarget"
  "af6564869756c5cd87560435d26882f1")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<TrajectoryTarget>)))
  "Returns full string definition for message of type '<TrajectoryTarget>"
  (cl:format cl:nil "# TrajectoryTarget.msg~%# 包含起点和终点的位置、速度、加速度信息~%~%# 标准消息头~%std_msgs/Header header~%~%# 起点信息~%geometry_msgs/Point start_position     # 起点位置~%geometry_msgs/Vector3 start_velocity   # 起点速度~%geometry_msgs/Vector3 start_acceleration  # 起点加速度~%~%# 终点信息~%geometry_msgs/Point goal_position      # 终点位置~%geometry_msgs/Vector3 goal_velocity    # 终点速度~%geometry_msgs/Vector3 goal_acceleration   # 终点加速度~%~%# 可视化控制标志~%bool enable_visualization              # 是否启用可视化~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Vector3~%# This represents a vector in free space. ~%# It is only meant to represent a direction. Therefore, it does not~%# make sense to apply a translation to it (e.g., when applying a ~%# generic rigid transformation to a Vector3, tf2 will only apply the~%# rotation). If you want your data to be translatable too, use the~%# geometry_msgs/Point message instead.~%~%float64 x~%float64 y~%float64 z~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'TrajectoryTarget)))
  "Returns full string definition for message of type 'TrajectoryTarget"
  (cl:format cl:nil "# TrajectoryTarget.msg~%# 包含起点和终点的位置、速度、加速度信息~%~%# 标准消息头~%std_msgs/Header header~%~%# 起点信息~%geometry_msgs/Point start_position     # 起点位置~%geometry_msgs/Vector3 start_velocity   # 起点速度~%geometry_msgs/Vector3 start_acceleration  # 起点加速度~%~%# 终点信息~%geometry_msgs/Point goal_position      # 终点位置~%geometry_msgs/Vector3 goal_velocity    # 终点速度~%geometry_msgs/Vector3 goal_acceleration   # 终点加速度~%~%# 可视化控制标志~%bool enable_visualization              # 是否启用可视化~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Vector3~%# This represents a vector in free space. ~%# It is only meant to represent a direction. Therefore, it does not~%# make sense to apply a translation to it (e.g., when applying a ~%# generic rigid transformation to a Vector3, tf2 will only apply the~%# rotation). If you want your data to be translatable too, use the~%# geometry_msgs/Point message instead.~%~%float64 x~%float64 y~%float64 z~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <TrajectoryTarget>))
  (cl:+ 0
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'header))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'start_position))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'start_velocity))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'start_acceleration))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'goal_position))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'goal_velocity))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'goal_acceleration))
     1
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <TrajectoryTarget>))
  "Converts a ROS message object to a list"
  (cl:list 'TrajectoryTarget
    (cl:cons ':header (header msg))
    (cl:cons ':start_position (start_position msg))
    (cl:cons ':start_velocity (start_velocity msg))
    (cl:cons ':start_acceleration (start_acceleration msg))
    (cl:cons ':goal_position (goal_position msg))
    (cl:cons ':goal_velocity (goal_velocity msg))
    (cl:cons ':goal_acceleration (goal_acceleration msg))
    (cl:cons ':enable_visualization (enable_visualization msg))
))
