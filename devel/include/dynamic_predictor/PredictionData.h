// Generated by gencpp from file dynamic_predictor/PredictionData.msg
// DO NOT EDIT!


#ifndef DYNAMIC_PREDICTOR_MESSAGE_PREDICTIONDATA_H
#define DYNAMIC_PREDICTOR_MESSAGE_PREDICTIONDATA_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Point.h>

namespace dynamic_predictor
{
template <class ContainerAllocator>
struct PredictionData_
{
  typedef PredictionData_<ContainerAllocator> Type;

  PredictionData_()
    : header()
    , num_obstacles(0)
    , num_intents(0)
    , num_time_steps(0)
    , intent_probs_data()
    , pos_pred_data()
    , size_pred_data()  {
    }
  PredictionData_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , num_obstacles(0)
    , num_intents(0)
    , num_time_steps(0)
    , intent_probs_data(_alloc)
    , pos_pred_data(_alloc)
    , size_pred_data(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef int32_t _num_obstacles_type;
  _num_obstacles_type num_obstacles;

   typedef int32_t _num_intents_type;
  _num_intents_type num_intents;

   typedef int32_t _num_time_steps_type;
  _num_time_steps_type num_time_steps;

   typedef std::vector<double, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<double>> _intent_probs_data_type;
  _intent_probs_data_type intent_probs_data;

   typedef std::vector< ::geometry_msgs::Point_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Point_<ContainerAllocator> >> _pos_pred_data_type;
  _pos_pred_data_type pos_pred_data;

   typedef std::vector< ::geometry_msgs::Point_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Point_<ContainerAllocator> >> _size_pred_data_type;
  _size_pred_data_type size_pred_data;





  typedef boost::shared_ptr< ::dynamic_predictor::PredictionData_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::dynamic_predictor::PredictionData_<ContainerAllocator> const> ConstPtr;

}; // struct PredictionData_

typedef ::dynamic_predictor::PredictionData_<std::allocator<void> > PredictionData;

typedef boost::shared_ptr< ::dynamic_predictor::PredictionData > PredictionDataPtr;
typedef boost::shared_ptr< ::dynamic_predictor::PredictionData const> PredictionDataConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::dynamic_predictor::PredictionData_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::dynamic_predictor::PredictionData_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::dynamic_predictor::PredictionData_<ContainerAllocator1> & lhs, const ::dynamic_predictor::PredictionData_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.num_obstacles == rhs.num_obstacles &&
    lhs.num_intents == rhs.num_intents &&
    lhs.num_time_steps == rhs.num_time_steps &&
    lhs.intent_probs_data == rhs.intent_probs_data &&
    lhs.pos_pred_data == rhs.pos_pred_data &&
    lhs.size_pred_data == rhs.size_pred_data;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::dynamic_predictor::PredictionData_<ContainerAllocator1> & lhs, const ::dynamic_predictor::PredictionData_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace dynamic_predictor

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::dynamic_predictor::PredictionData_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::dynamic_predictor::PredictionData_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::dynamic_predictor::PredictionData_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
{
  static const char* value()
  {
    return "51a1997e68e7a493e21155880d375c24";
  }

  static const char* value(const ::dynamic_predictor::PredictionData_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x51a1997e68e7a493ULL;
  static const uint64_t static_value2 = 0xe21155880d375c24ULL;
};

template<class ContainerAllocator>
struct DataType< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
{
  static const char* value()
  {
    return "dynamic_predictor/PredictionData";
  }

  static const char* value(const ::dynamic_predictor::PredictionData_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 动态障碍物预测数据消息\n"
"# 对应C++数据结构：\n"
"# - std::vector<Eigen::VectorXd> intentProb_\n"
"# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_\n"
"# - std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_\n"
"\n"
"std_msgs/Header header\n"
"\n"
"# ========== 数据维度信息 ==========\n"
"int32 num_obstacles          # 障碍物数量\n"
"int32 num_intents           # 意图数量 (固定为4: FORWARD=0, LEFT=1, RIGHT=2, STOP=3)\n"
"int32 num_time_steps        # 预测时间步数\n"
"\n"
"# ========== 意图概率数据 ==========\n"
"# 对应: std::vector<Eigen::VectorXd> intentProb_\n"
"# 数据排列: [obs0_intent0, obs0_intent1, obs0_intent2, obs0_intent3, obs1_intent0, obs1_intent1, ...]\n"
"# 访问方式: prob = intent_probs_data[obstacle_idx * num_intents + intent_idx]\n"
"float64[] intent_probs_data\n"
"\n"
"# ========== 位置预测数据 ==========\n"
"# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> posPred_\n"
"# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]\n"
"# 访问方式: pos = pos_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]\n"
"geometry_msgs/Point[] pos_pred_data\n"
"\n"
"# ========== 尺寸预测数据 ==========\n"
"# 对应: std::vector<std::vector<std::vector<Eigen::Vector3d>>> sizePred_\n"
"# 数据排列: [obs0_intent0_t0, obs0_intent0_t1, ..., obs0_intent0_tN, obs0_intent1_t0, ..., obs1_intent0_t0, ...]\n"
"# 访问方式: size = size_pred_data[obstacle_idx * num_intents * num_time_steps + intent_idx * num_time_steps + time_idx]\n"
"geometry_msgs/Point[] size_pred_data \n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::dynamic_predictor::PredictionData_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.num_obstacles);
      stream.next(m.num_intents);
      stream.next(m.num_time_steps);
      stream.next(m.intent_probs_data);
      stream.next(m.pos_pred_data);
      stream.next(m.size_pred_data);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct PredictionData_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::dynamic_predictor::PredictionData_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::dynamic_predictor::PredictionData_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "num_obstacles: ";
    Printer<int32_t>::stream(s, indent + "  ", v.num_obstacles);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "num_intents: ";
    Printer<int32_t>::stream(s, indent + "  ", v.num_intents);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "num_time_steps: ";
    Printer<int32_t>::stream(s, indent + "  ", v.num_time_steps);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "intent_probs_data: ";
    if (v.intent_probs_data.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.intent_probs_data.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<double>::stream(s, true ? std::string() : indent + "    ", v.intent_probs_data[i]);
    }
    if (v.intent_probs_data.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos_pred_data: ";
    if (v.pos_pred_data.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.pos_pred_data.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.pos_pred_data[i]);
    }
    if (v.pos_pred_data.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "size_pred_data: ";
    if (v.size_pred_data.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.size_pred_data.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.size_pred_data[i]);
    }
    if (v.size_pred_data.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // DYNAMIC_PREDICTOR_MESSAGE_PREDICTIONDATA_H
