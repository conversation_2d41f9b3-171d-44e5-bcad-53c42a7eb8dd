// Generated by gencpp from file tracking_controller/Target.msg
// DO NOT EDIT!


#ifndef TRACKING_CONTROLLER_MESSAGE_TARGET_H
#define TRACKING_CONTROLLER_MESSAGE_TARGET_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Vector3.h>

namespace tracking_controller
{
template <class ContainerAllocator>
struct Target_
{
  typedef Target_<ContainerAllocator> Type;

  Target_()
    : header()
    , type_mask(0)
    , position()
    , velocity()
    , acceleration()
    , yaw(0.0)  {
    }
  Target_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , type_mask(0)
    , position(_alloc)
    , velocity(_alloc)
    , acceleration(_alloc)
    , yaw(0.0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef uint8_t _type_mask_type;
  _type_mask_type type_mask;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _position_type;
  _position_type position;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _velocity_type;
  _velocity_type velocity;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _acceleration_type;
  _acceleration_type acceleration;

   typedef float _yaw_type;
  _yaw_type yaw;



// reducing the odds to have name collisions with Windows.h 
#if defined(_WIN32) && defined(IGNORE_ACC)
  #undef IGNORE_ACC
#endif
#if defined(_WIN32) && defined(IGNORE_ACC_VEL)
  #undef IGNORE_ACC_VEL
#endif

  enum {
    IGNORE_ACC = 1u,
    IGNORE_ACC_VEL = 2u,
  };


  typedef boost::shared_ptr< ::tracking_controller::Target_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::tracking_controller::Target_<ContainerAllocator> const> ConstPtr;

}; // struct Target_

typedef ::tracking_controller::Target_<std::allocator<void> > Target;

typedef boost::shared_ptr< ::tracking_controller::Target > TargetPtr;
typedef boost::shared_ptr< ::tracking_controller::Target const> TargetConstPtr;

// constants requiring out of line definition

   

   



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::tracking_controller::Target_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::tracking_controller::Target_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::tracking_controller::Target_<ContainerAllocator1> & lhs, const ::tracking_controller::Target_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.type_mask == rhs.type_mask &&
    lhs.position == rhs.position &&
    lhs.velocity == rhs.velocity &&
    lhs.acceleration == rhs.acceleration &&
    lhs.yaw == rhs.yaw;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::tracking_controller::Target_<ContainerAllocator1> & lhs, const ::tracking_controller::Target_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace tracking_controller

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::tracking_controller::Target_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::tracking_controller::Target_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::tracking_controller::Target_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::tracking_controller::Target_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::tracking_controller::Target_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::tracking_controller::Target_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::tracking_controller::Target_<ContainerAllocator> >
{
  static const char* value()
  {
    return "260f087a436e669355e95cd12c00e98a";
  }

  static const char* value(const ::tracking_controller::Target_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x260f087a436e6693ULL;
  static const uint64_t static_value2 = 0x55e95cd12c00e98aULL;
};

template<class ContainerAllocator>
struct DataType< ::tracking_controller::Target_<ContainerAllocator> >
{
  static const char* value()
  {
    return "tracking_controller/Target";
  }

  static const char* value(const ::tracking_controller::Target_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::tracking_controller::Target_<ContainerAllocator> >
{
  static const char* value()
  {
    return "std_msgs/Header header\n"
"\n"
"uint8 type_mask\n"
"uint8 IGNORE_ACC = 1	# Position Reference\n"
"uint8 IGNORE_ACC_VEL = 2	# Position Reference\n"
"\n"
"geometry_msgs/Vector3 position\n"
"geometry_msgs/Vector3 velocity\n"
"geometry_msgs/Vector3 acceleration\n"
"float32 yaw\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::tracking_controller::Target_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::tracking_controller::Target_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.type_mask);
      stream.next(m.position);
      stream.next(m.velocity);
      stream.next(m.acceleration);
      stream.next(m.yaw);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Target_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::tracking_controller::Target_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::tracking_controller::Target_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "type_mask: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.type_mask);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.position);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "velocity: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.velocity);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "acceleration: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.acceleration);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "yaw: ";
    Printer<float>::stream(s, indent + "  ", v.yaw);
  }
};

} // namespace message_operations
} // namespace ros

#endif // TRACKING_CONTROLLER_MESSAGE_TARGET_H
