// Generated by gencpp from file opt_sfc/TrajectoryTarget.msg
// DO NOT EDIT!


#ifndef OPT_SFC_MESSAGE_TRAJECTORYTARGET_H
#define OPT_SFC_MESSAGE_TRAJECTORYTARGET_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Vector3.h>

namespace opt_sfc
{
template <class ContainerAllocator>
struct TrajectoryTarget_
{
  typedef TrajectoryTarget_<ContainerAllocator> Type;

  TrajectoryTarget_()
    : header()
    , start_position()
    , start_velocity()
    , start_acceleration()
    , goal_position()
    , goal_velocity()
    , goal_acceleration()
    , enable_visualization(false)  {
    }
  TrajectoryTarget_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , start_position(_alloc)
    , start_velocity(_alloc)
    , start_acceleration(_alloc)
    , goal_position(_alloc)
    , goal_velocity(_alloc)
    , goal_acceleration(_alloc)
    , enable_visualization(false)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _start_position_type;
  _start_position_type start_position;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _start_velocity_type;
  _start_velocity_type start_velocity;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _start_acceleration_type;
  _start_acceleration_type start_acceleration;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _goal_position_type;
  _goal_position_type goal_position;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _goal_velocity_type;
  _goal_velocity_type goal_velocity;

   typedef  ::geometry_msgs::Vector3_<ContainerAllocator>  _goal_acceleration_type;
  _goal_acceleration_type goal_acceleration;

   typedef uint8_t _enable_visualization_type;
  _enable_visualization_type enable_visualization;





  typedef boost::shared_ptr< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> const> ConstPtr;

}; // struct TrajectoryTarget_

typedef ::opt_sfc::TrajectoryTarget_<std::allocator<void> > TrajectoryTarget;

typedef boost::shared_ptr< ::opt_sfc::TrajectoryTarget > TrajectoryTargetPtr;
typedef boost::shared_ptr< ::opt_sfc::TrajectoryTarget const> TrajectoryTargetConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::opt_sfc::TrajectoryTarget_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::opt_sfc::TrajectoryTarget_<ContainerAllocator1> & lhs, const ::opt_sfc::TrajectoryTarget_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.start_position == rhs.start_position &&
    lhs.start_velocity == rhs.start_velocity &&
    lhs.start_acceleration == rhs.start_acceleration &&
    lhs.goal_position == rhs.goal_position &&
    lhs.goal_velocity == rhs.goal_velocity &&
    lhs.goal_acceleration == rhs.goal_acceleration &&
    lhs.enable_visualization == rhs.enable_visualization;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::opt_sfc::TrajectoryTarget_<ContainerAllocator1> & lhs, const ::opt_sfc::TrajectoryTarget_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace opt_sfc

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
{
  static const char* value()
  {
    return "af6564869756c5cd87560435d26882f1";
  }

  static const char* value(const ::opt_sfc::TrajectoryTarget_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xaf6564869756c5cdULL;
  static const uint64_t static_value2 = 0x87560435d26882f1ULL;
};

template<class ContainerAllocator>
struct DataType< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
{
  static const char* value()
  {
    return "opt_sfc/TrajectoryTarget";
  }

  static const char* value(const ::opt_sfc::TrajectoryTarget_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# TrajectoryTarget.msg\n"
"# 包含起点和终点的位置、速度、加速度信息\n"
"\n"
"# 标准消息头\n"
"std_msgs/Header header\n"
"\n"
"# 起点信息\n"
"geometry_msgs/Point start_position     # 起点位置\n"
"geometry_msgs/Vector3 start_velocity   # 起点速度\n"
"geometry_msgs/Vector3 start_acceleration  # 起点加速度\n"
"\n"
"# 终点信息\n"
"geometry_msgs/Point goal_position      # 终点位置\n"
"geometry_msgs/Vector3 goal_velocity    # 终点速度\n"
"geometry_msgs/Vector3 goal_acceleration   # 终点加速度\n"
"\n"
"# 可视化控制标志\n"
"bool enable_visualization              # 是否启用可视化\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::opt_sfc::TrajectoryTarget_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.start_position);
      stream.next(m.start_velocity);
      stream.next(m.start_acceleration);
      stream.next(m.goal_position);
      stream.next(m.goal_velocity);
      stream.next(m.goal_acceleration);
      stream.next(m.enable_visualization);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct TrajectoryTarget_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::opt_sfc::TrajectoryTarget_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::opt_sfc::TrajectoryTarget_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "start_position: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.start_position);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "start_velocity: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.start_velocity);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "start_acceleration: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.start_acceleration);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "goal_position: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.goal_position);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "goal_velocity: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.goal_velocity);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "goal_acceleration: ";
    Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, indent + "  ", v.goal_acceleration);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "enable_visualization: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.enable_visualization);
  }
};

} // namespace message_operations
} // namespace ros

#endif // OPT_SFC_MESSAGE_TRAJECTORYTARGET_H
