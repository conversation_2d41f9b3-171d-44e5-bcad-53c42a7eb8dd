// Generated by gencpp from file uav_simulator/LivoxCustomMsg.msg
// DO NOT EDIT!


#ifndef UAV_SIMULATOR_MESSAGE_LIVOXCUSTOMMSG_H
#define UAV_SIMULATOR_MESSAGE_LIVOXCUSTOMMSG_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <uav_simulator/CustomPoint.h>

namespace uav_simulator
{
template <class ContainerAllocator>
struct LivoxCustomMsg_
{
  typedef LivoxCustomMsg_<ContainerAllocator> Type;

  LivoxCustomMsg_()
    : header()
    , timebase(0)
    , point_num(0)
    , lidar_id(0)
    , rsvd()
    , points()  {
      rsvd.assign(0);
  }
  LivoxCustomMsg_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , timebase(0)
    , point_num(0)
    , lidar_id(0)
    , rsvd()
    , points(_alloc)  {
  (void)_alloc;
      rsvd.assign(0);
  }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef uint64_t _timebase_type;
  _timebase_type timebase;

   typedef uint32_t _point_num_type;
  _point_num_type point_num;

   typedef uint8_t _lidar_id_type;
  _lidar_id_type lidar_id;

   typedef boost::array<uint8_t, 3>  _rsvd_type;
  _rsvd_type rsvd;

   typedef std::vector< ::uav_simulator::CustomPoint_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::uav_simulator::CustomPoint_<ContainerAllocator> >> _points_type;
  _points_type points;





  typedef boost::shared_ptr< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> const> ConstPtr;

}; // struct LivoxCustomMsg_

typedef ::uav_simulator::LivoxCustomMsg_<std::allocator<void> > LivoxCustomMsg;

typedef boost::shared_ptr< ::uav_simulator::LivoxCustomMsg > LivoxCustomMsgPtr;
typedef boost::shared_ptr< ::uav_simulator::LivoxCustomMsg const> LivoxCustomMsgConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator1> & lhs, const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.timebase == rhs.timebase &&
    lhs.point_num == rhs.point_num &&
    lhs.lidar_id == rhs.lidar_id &&
    lhs.rsvd == rhs.rsvd &&
    lhs.points == rhs.points;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator1> & lhs, const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace uav_simulator

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
{
  static const char* value()
  {
    return "e4d6829bdfe657cb6c21a746c86b21a6";
  }

  static const char* value(const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xe4d6829bdfe657cbULL;
  static const uint64_t static_value2 = 0x6c21a746c86b21a6ULL;
};

template<class ContainerAllocator>
struct DataType< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
{
  static const char* value()
  {
    return "uav_simulator/LivoxCustomMsg";
  }

  static const char* value(const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# Livox publish pointcloud msg format.\n"
"\n"
"std_msgs/Header header    # ROS standard message header\n"
"uint64 timebase           # The time of first point\n"
"uint32 point_num          # Total number of pointclouds\n"
"uint8  lidar_id           # Lidar device id number\n"
"uint8[3]  rsvd            # Reserved use\n"
"CustomPoint[] points      # Pointcloud data\n"
"\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: uav_simulator/CustomPoint\n"
"# Livox costom pointcloud format.\n"
"\n"
"uint32 offset_time      # offset time relative to the base time\n"
"float32 x               # X axis, unit:m\n"
"float32 y               # Y axis, unit:m\n"
"float32 z               # Z axis, unit:m\n"
"uint8 reflectivity      # reflectivity, 0~255\n"
"uint8 tag               # livox tag\n"
"uint8 line              # laser number in lidar\n"
"\n"
;
  }

  static const char* value(const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.timebase);
      stream.next(m.point_num);
      stream.next(m.lidar_id);
      stream.next(m.rsvd);
      stream.next(m.points);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct LivoxCustomMsg_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::uav_simulator::LivoxCustomMsg_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::uav_simulator::LivoxCustomMsg_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "timebase: ";
    Printer<uint64_t>::stream(s, indent + "  ", v.timebase);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "point_num: ";
    Printer<uint32_t>::stream(s, indent + "  ", v.point_num);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "lidar_id: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.lidar_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "rsvd: ";
    if (v.rsvd.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.rsvd.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<uint8_t>::stream(s, true ? std::string() : indent + "    ", v.rsvd[i]);
    }
    if (v.rsvd.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "points: ";
    if (v.points.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.points.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::uav_simulator::CustomPoint_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.points[i]);
    }
    if (v.points.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // UAV_SIMULATOR_MESSAGE_LIVOXCUSTOMMSG_H
