// Generated by gencpp from file vicon_env/Point2d.msg
// DO NOT EDIT!


#ifndef VICON_ENV_MESSAGE_POINT2D_H
#define VICON_ENV_MESSAGE_POINT2D_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace vicon_env
{
template <class ContainerAllocator>
struct Point2d_
{
  typedef Point2d_<ContainerAllocator> Type;

  Point2d_()
    : x(0.0)
    , y(0.0)  {
    }
  Point2d_(const ContainerAllocator& _alloc)
    : x(0.0)
    , y(0.0)  {
  (void)_alloc;
    }



   typedef double _x_type;
  _x_type x;

   typedef double _y_type;
  _y_type y;





  typedef boost::shared_ptr< ::vicon_env::Point2d_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::vicon_env::Point2d_<ContainerAllocator> const> ConstPtr;

}; // struct Point2d_

typedef ::vicon_env::Point2d_<std::allocator<void> > Point2d;

typedef boost::shared_ptr< ::vicon_env::Point2d > Point2dPtr;
typedef boost::shared_ptr< ::vicon_env::Point2d const> Point2dConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::vicon_env::Point2d_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::vicon_env::Point2d_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::vicon_env::Point2d_<ContainerAllocator1> & lhs, const ::vicon_env::Point2d_<ContainerAllocator2> & rhs)
{
  return lhs.x == rhs.x &&
    lhs.y == rhs.y;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::vicon_env::Point2d_<ContainerAllocator1> & lhs, const ::vicon_env::Point2d_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace vicon_env

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Point2d_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Point2d_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Point2d_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Point2d_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Point2d_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Point2d_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::vicon_env::Point2d_<ContainerAllocator> >
{
  static const char* value()
  {
    return "209f516d3eb691f0663e25cb750d67c1";
  }

  static const char* value(const ::vicon_env::Point2d_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x209f516d3eb691f0ULL;
  static const uint64_t static_value2 = 0x663e25cb750d67c1ULL;
};

template<class ContainerAllocator>
struct DataType< ::vicon_env::Point2d_<ContainerAllocator> >
{
  static const char* value()
  {
    return "vicon_env/Point2d";
  }

  static const char* value(const ::vicon_env::Point2d_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::vicon_env::Point2d_<ContainerAllocator> >
{
  static const char* value()
  {
    return "float64 x\n"
"float64 y\n"
;
  }

  static const char* value(const ::vicon_env::Point2d_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::vicon_env::Point2d_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.x);
      stream.next(m.y);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Point2d_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::vicon_env::Point2d_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::vicon_env::Point2d_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "x: ";
    Printer<double>::stream(s, indent + "  ", v.x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "y: ";
    Printer<double>::stream(s, indent + "  ", v.y);
  }
};

} // namespace message_operations
} // namespace ros

#endif // VICON_ENV_MESSAGE_POINT2D_H
