// Generated by gencpp from file vicon_env/Polyhedron.msg
// DO NOT EDIT!


#ifndef VICON_ENV_MESSAGE_POLYHEDRON_H
#define VICON_ENV_MESSAGE_POLYHEDRON_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <vicon_env/Point2d.h>
#include <vicon_env/Point3d.h>
#include <vicon_env/Point3d.h>
#include <vicon_env/Point3d.h>

namespace vicon_env
{
template <class ContainerAllocator>
struct Polyhedron_
{
  typedef Polyhedron_<ContainerAllocator> Type;

  Polyhedron_()
    : id(0)
    , rps()
    , noise()
    , r(0.0)
    , points()
    , normals()  {
    }
  Polyhedron_(const ContainerAllocator& _alloc)
    : id(0)
    , rps(_alloc)
    , noise(_alloc)
    , r(0.0)
    , points(_alloc)
    , normals(_alloc)  {
  (void)_alloc;
    }



   typedef int32_t _id_type;
  _id_type id;

   typedef std::vector< ::vicon_env::Point2d_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Point2d_<ContainerAllocator> >> _rps_type;
  _rps_type rps;

   typedef  ::vicon_env::Point3d_<ContainerAllocator>  _noise_type;
  _noise_type noise;

   typedef double _r_type;
  _r_type r;

   typedef std::vector< ::vicon_env::Point3d_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Point3d_<ContainerAllocator> >> _points_type;
  _points_type points;

   typedef std::vector< ::vicon_env::Point3d_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Point3d_<ContainerAllocator> >> _normals_type;
  _normals_type normals;





  typedef boost::shared_ptr< ::vicon_env::Polyhedron_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::vicon_env::Polyhedron_<ContainerAllocator> const> ConstPtr;

}; // struct Polyhedron_

typedef ::vicon_env::Polyhedron_<std::allocator<void> > Polyhedron;

typedef boost::shared_ptr< ::vicon_env::Polyhedron > PolyhedronPtr;
typedef boost::shared_ptr< ::vicon_env::Polyhedron const> PolyhedronConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::vicon_env::Polyhedron_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::vicon_env::Polyhedron_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::vicon_env::Polyhedron_<ContainerAllocator1> & lhs, const ::vicon_env::Polyhedron_<ContainerAllocator2> & rhs)
{
  return lhs.id == rhs.id &&
    lhs.rps == rhs.rps &&
    lhs.noise == rhs.noise &&
    lhs.r == rhs.r &&
    lhs.points == rhs.points &&
    lhs.normals == rhs.normals;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::vicon_env::Polyhedron_<ContainerAllocator1> & lhs, const ::vicon_env::Polyhedron_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace vicon_env

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Polyhedron_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Polyhedron_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Polyhedron_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Polyhedron_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Polyhedron_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Polyhedron_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::vicon_env::Polyhedron_<ContainerAllocator> >
{
  static const char* value()
  {
    return "4e4035e4e6d87b6937718889da241e8b";
  }

  static const char* value(const ::vicon_env::Polyhedron_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x4e4035e4e6d87b69ULL;
  static const uint64_t static_value2 = 0x37718889da241e8bULL;
};

template<class ContainerAllocator>
struct DataType< ::vicon_env::Polyhedron_<ContainerAllocator> >
{
  static const char* value()
  {
    return "vicon_env/Polyhedron";
  }

  static const char* value(const ::vicon_env::Polyhedron_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::vicon_env::Polyhedron_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 id\n"
"Point2d[] rps\n"
"Point3d noise\n"
"float64 r #clearance range\n"
"Point3d[] points\n"
"Point3d[] normals #norm is an outer vector\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point2d\n"
"float64 x\n"
"float64 y\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point3d\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::vicon_env::Polyhedron_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::vicon_env::Polyhedron_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.id);
      stream.next(m.rps);
      stream.next(m.noise);
      stream.next(m.r);
      stream.next(m.points);
      stream.next(m.normals);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Polyhedron_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::vicon_env::Polyhedron_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::vicon_env::Polyhedron_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "rps: ";
    if (v.rps.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.rps.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Point2d_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.rps[i]);
    }
    if (v.rps.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "noise: ";
    Printer< ::vicon_env::Point3d_<ContainerAllocator> >::stream(s, indent + "  ", v.noise);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "r: ";
    Printer<double>::stream(s, indent + "  ", v.r);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "points: ";
    if (v.points.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.points.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Point3d_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.points[i]);
    }
    if (v.points.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "normals: ";
    if (v.normals.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.normals.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Point3d_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.normals[i]);
    }
    if (v.normals.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // VICON_ENV_MESSAGE_POLYHEDRON_H
