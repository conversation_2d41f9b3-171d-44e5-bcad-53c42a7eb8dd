// Generated by gencpp from file vicon_env/Ellipse.msg
// DO NOT EDIT!


#ifndef VICON_ENV_MESSAGE_ELLIPSE_H
#define VICON_ENV_MESSAGE_ELLIPSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <vicon_env/Point2d.h>
#include <vicon_env/Point2d.h>

namespace vicon_env
{
template <class ContainerAllocator>
struct Ellipse_
{
  typedef Ellipse_<ContainerAllocator> Type;

  Ellipse_()
    : id(0)
    , pos()
    , noise()
    , r(0.0)
    , a(0.0)
    , b(0.0)
    , alpha(0.0)  {
    }
  Ellipse_(const ContainerAllocator& _alloc)
    : id(0)
    , pos(_alloc)
    , noise(_alloc)
    , r(0.0)
    , a(0.0)
    , b(0.0)
    , alpha(0.0)  {
  (void)_alloc;
    }



   typedef int32_t _id_type;
  _id_type id;

   typedef  ::vicon_env::Point2d_<ContainerAllocator>  _pos_type;
  _pos_type pos;

   typedef  ::vicon_env::Point2d_<ContainerAllocator>  _noise_type;
  _noise_type noise;

   typedef double _r_type;
  _r_type r;

   typedef double _a_type;
  _a_type a;

   typedef double _b_type;
  _b_type b;

   typedef double _alpha_type;
  _alpha_type alpha;





  typedef boost::shared_ptr< ::vicon_env::Ellipse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::vicon_env::Ellipse_<ContainerAllocator> const> ConstPtr;

}; // struct Ellipse_

typedef ::vicon_env::Ellipse_<std::allocator<void> > Ellipse;

typedef boost::shared_ptr< ::vicon_env::Ellipse > EllipsePtr;
typedef boost::shared_ptr< ::vicon_env::Ellipse const> EllipseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::vicon_env::Ellipse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::vicon_env::Ellipse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::vicon_env::Ellipse_<ContainerAllocator1> & lhs, const ::vicon_env::Ellipse_<ContainerAllocator2> & rhs)
{
  return lhs.id == rhs.id &&
    lhs.pos == rhs.pos &&
    lhs.noise == rhs.noise &&
    lhs.r == rhs.r &&
    lhs.a == rhs.a &&
    lhs.b == rhs.b &&
    lhs.alpha == rhs.alpha;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::vicon_env::Ellipse_<ContainerAllocator1> & lhs, const ::vicon_env::Ellipse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace vicon_env

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Ellipse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Ellipse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Ellipse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Ellipse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Ellipse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Ellipse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::vicon_env::Ellipse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "0d9f78b0e58e82ba751d86429f04bc3c";
  }

  static const char* value(const ::vicon_env::Ellipse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x0d9f78b0e58e82baULL;
  static const uint64_t static_value2 = 0x751d86429f04bc3cULL;
};

template<class ContainerAllocator>
struct DataType< ::vicon_env::Ellipse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "vicon_env/Ellipse";
  }

  static const char* value(const ::vicon_env::Ellipse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::vicon_env::Ellipse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 id\n"
"Point2d pos\n"
"Point2d noise\n"
"float64 r #clearance range\n"
"float64 a\n"
"float64 b\n"
"float64 alpha # rotation\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point2d\n"
"float64 x\n"
"float64 y\n"
;
  }

  static const char* value(const ::vicon_env::Ellipse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::vicon_env::Ellipse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.id);
      stream.next(m.pos);
      stream.next(m.noise);
      stream.next(m.r);
      stream.next(m.a);
      stream.next(m.b);
      stream.next(m.alpha);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Ellipse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::vicon_env::Ellipse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::vicon_env::Ellipse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos: ";
    Printer< ::vicon_env::Point2d_<ContainerAllocator> >::stream(s, indent + "  ", v.pos);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "noise: ";
    Printer< ::vicon_env::Point2d_<ContainerAllocator> >::stream(s, indent + "  ", v.noise);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "r: ";
    Printer<double>::stream(s, indent + "  ", v.r);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "a: ";
    Printer<double>::stream(s, indent + "  ", v.a);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "b: ";
    Printer<double>::stream(s, indent + "  ", v.b);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "alpha: ";
    Printer<double>::stream(s, indent + "  ", v.alpha);
  }
};

} // namespace message_operations
} // namespace ros

#endif // VICON_ENV_MESSAGE_ELLIPSE_H
