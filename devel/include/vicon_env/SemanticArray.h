// Generated by gencpp from file vicon_env/SemanticArray.msg
// DO NOT EDIT!


#ifndef VICON_ENV_MESSAGE_SEMANTICARRAY_H
#define VICON_ENV_MESSAGE_SEMANTICARRAY_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <vicon_env/Point3d.h>
#include <vicon_env/Ellipse.h>
#include <vicon_env/Circle.h>
#include <vicon_env/Polygon.h>
#include <vicon_env/Cylinder.h>
#include <vicon_env/Polyhedron.h>

namespace vicon_env
{
template <class ContainerAllocator>
struct SemanticArray_
{
  typedef SemanticArray_<ContainerAllocator> Type;

  SemanticArray_()
    : header()
    , mav_id(0)
    , mav_pos()
    , ellipses()
    , circles()
    , polygons()
    , cylinders()
    , polyhedrons()  {
    }
  SemanticArray_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , mav_id(0)
    , mav_pos(_alloc)
    , ellipses(_alloc)
    , circles(_alloc)
    , polygons(_alloc)
    , cylinders(_alloc)
    , polyhedrons(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef int32_t _mav_id_type;
  _mav_id_type mav_id;

   typedef  ::vicon_env::Point3d_<ContainerAllocator>  _mav_pos_type;
  _mav_pos_type mav_pos;

   typedef std::vector< ::vicon_env::Ellipse_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Ellipse_<ContainerAllocator> >> _ellipses_type;
  _ellipses_type ellipses;

   typedef std::vector< ::vicon_env::Circle_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Circle_<ContainerAllocator> >> _circles_type;
  _circles_type circles;

   typedef std::vector< ::vicon_env::Polygon_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Polygon_<ContainerAllocator> >> _polygons_type;
  _polygons_type polygons;

   typedef std::vector< ::vicon_env::Cylinder_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Cylinder_<ContainerAllocator> >> _cylinders_type;
  _cylinders_type cylinders;

   typedef std::vector< ::vicon_env::Polyhedron_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::vicon_env::Polyhedron_<ContainerAllocator> >> _polyhedrons_type;
  _polyhedrons_type polyhedrons;





  typedef boost::shared_ptr< ::vicon_env::SemanticArray_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::vicon_env::SemanticArray_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticArray_

typedef ::vicon_env::SemanticArray_<std::allocator<void> > SemanticArray;

typedef boost::shared_ptr< ::vicon_env::SemanticArray > SemanticArrayPtr;
typedef boost::shared_ptr< ::vicon_env::SemanticArray const> SemanticArrayConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::vicon_env::SemanticArray_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::vicon_env::SemanticArray_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::vicon_env::SemanticArray_<ContainerAllocator1> & lhs, const ::vicon_env::SemanticArray_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.mav_id == rhs.mav_id &&
    lhs.mav_pos == rhs.mav_pos &&
    lhs.ellipses == rhs.ellipses &&
    lhs.circles == rhs.circles &&
    lhs.polygons == rhs.polygons &&
    lhs.cylinders == rhs.cylinders &&
    lhs.polyhedrons == rhs.polyhedrons;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::vicon_env::SemanticArray_<ContainerAllocator1> & lhs, const ::vicon_env::SemanticArray_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace vicon_env

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::vicon_env::SemanticArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::vicon_env::SemanticArray_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::SemanticArray_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::SemanticArray_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::SemanticArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::SemanticArray_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::vicon_env::SemanticArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "f11c93d4fb77d44a8c5e24f11f9114d5";
  }

  static const char* value(const ::vicon_env::SemanticArray_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xf11c93d4fb77d44aULL;
  static const uint64_t static_value2 = 0x8c5e24f11f9114d5ULL;
};

template<class ContainerAllocator>
struct DataType< ::vicon_env::SemanticArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "vicon_env/SemanticArray";
  }

  static const char* value(const ::vicon_env::SemanticArray_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::vicon_env::SemanticArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "std_msgs/Header header\n"
"int32 mav_id #-1 for global map, 0 + for the mav_id\n"
"Point3d mav_pos\n"
"\n"
"#2d semantics\n"
"Ellipse[] ellipses\n"
"Circle[]  circles\n"
"Polygon[] polygons\n"
"\n"
"#3d semantics\n"
"Cylinder[] cylinders\n"
"Polyhedron[] polyhedrons\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point3d\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Ellipse\n"
"int32 id\n"
"Point2d pos\n"
"Point2d noise\n"
"float64 r #clearance range\n"
"float64 a\n"
"float64 b\n"
"float64 alpha # rotation\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point2d\n"
"float64 x\n"
"float64 y\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Circle\n"
"int32 id\n"
"Point2d pos\n"
"Point2d noise\n"
"float64 r\n"
"\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Polygon\n"
"int32 id\n"
"Point2d pos\n"
"Point2d noise\n"
"float64 r #clearance range\n"
"Point2d[] points\n"
"Point2d[] normals #norm is an outer vector\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Cylinder\n"
"int32 id\n"
"Point2d pos\n"
"Point3d noise\n"
"float64 r\n"
"float64 h\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Polyhedron\n"
"int32 id\n"
"Point2d[] rps\n"
"Point3d noise\n"
"float64 r #clearance range\n"
"Point3d[] points\n"
"Point3d[] normals #norm is an outer vector\n"
;
  }

  static const char* value(const ::vicon_env::SemanticArray_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::vicon_env::SemanticArray_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.mav_id);
      stream.next(m.mav_pos);
      stream.next(m.ellipses);
      stream.next(m.circles);
      stream.next(m.polygons);
      stream.next(m.cylinders);
      stream.next(m.polyhedrons);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticArray_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::vicon_env::SemanticArray_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::vicon_env::SemanticArray_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "mav_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.mav_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "mav_pos: ";
    Printer< ::vicon_env::Point3d_<ContainerAllocator> >::stream(s, indent + "  ", v.mav_pos);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "ellipses: ";
    if (v.ellipses.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.ellipses.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Ellipse_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.ellipses[i]);
    }
    if (v.ellipses.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "circles: ";
    if (v.circles.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.circles.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Circle_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.circles[i]);
    }
    if (v.circles.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "polygons: ";
    if (v.polygons.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.polygons.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Polygon_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.polygons[i]);
    }
    if (v.polygons.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "cylinders: ";
    if (v.cylinders.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.cylinders.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Cylinder_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.cylinders[i]);
    }
    if (v.cylinders.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "polyhedrons: ";
    if (v.polyhedrons.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.polyhedrons.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::vicon_env::Polyhedron_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.polyhedrons[i]);
    }
    if (v.polyhedrons.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // VICON_ENV_MESSAGE_SEMANTICARRAY_H
