// Generated by gencpp from file vicon_env/Circle.msg
// DO NOT EDIT!


#ifndef VICON_ENV_MESSAGE_CIRCLE_H
#define VICON_ENV_MESSAGE_CIRCLE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <vicon_env/Point2d.h>
#include <vicon_env/Point2d.h>

namespace vicon_env
{
template <class ContainerAllocator>
struct Circle_
{
  typedef Circle_<ContainerAllocator> Type;

  Circle_()
    : id(0)
    , pos()
    , noise()
    , r(0.0)  {
    }
  Circle_(const ContainerAllocator& _alloc)
    : id(0)
    , pos(_alloc)
    , noise(_alloc)
    , r(0.0)  {
  (void)_alloc;
    }



   typedef int32_t _id_type;
  _id_type id;

   typedef  ::vicon_env::Point2d_<ContainerAllocator>  _pos_type;
  _pos_type pos;

   typedef  ::vicon_env::Point2d_<ContainerAllocator>  _noise_type;
  _noise_type noise;

   typedef double _r_type;
  _r_type r;





  typedef boost::shared_ptr< ::vicon_env::Circle_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::vicon_env::Circle_<ContainerAllocator> const> ConstPtr;

}; // struct Circle_

typedef ::vicon_env::Circle_<std::allocator<void> > Circle;

typedef boost::shared_ptr< ::vicon_env::Circle > CirclePtr;
typedef boost::shared_ptr< ::vicon_env::Circle const> CircleConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::vicon_env::Circle_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::vicon_env::Circle_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::vicon_env::Circle_<ContainerAllocator1> & lhs, const ::vicon_env::Circle_<ContainerAllocator2> & rhs)
{
  return lhs.id == rhs.id &&
    lhs.pos == rhs.pos &&
    lhs.noise == rhs.noise &&
    lhs.r == rhs.r;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::vicon_env::Circle_<ContainerAllocator1> & lhs, const ::vicon_env::Circle_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace vicon_env

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Circle_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Circle_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Circle_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Circle_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Circle_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Circle_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::vicon_env::Circle_<ContainerAllocator> >
{
  static const char* value()
  {
    return "f30dbb5c40b813ebac6bbcc3f2845923";
  }

  static const char* value(const ::vicon_env::Circle_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xf30dbb5c40b813ebULL;
  static const uint64_t static_value2 = 0xac6bbcc3f2845923ULL;
};

template<class ContainerAllocator>
struct DataType< ::vicon_env::Circle_<ContainerAllocator> >
{
  static const char* value()
  {
    return "vicon_env/Circle";
  }

  static const char* value(const ::vicon_env::Circle_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::vicon_env::Circle_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 id\n"
"Point2d pos\n"
"Point2d noise\n"
"float64 r\n"
"\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point2d\n"
"float64 x\n"
"float64 y\n"
;
  }

  static const char* value(const ::vicon_env::Circle_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::vicon_env::Circle_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.id);
      stream.next(m.pos);
      stream.next(m.noise);
      stream.next(m.r);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Circle_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::vicon_env::Circle_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::vicon_env::Circle_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos: ";
    Printer< ::vicon_env::Point2d_<ContainerAllocator> >::stream(s, indent + "  ", v.pos);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "noise: ";
    Printer< ::vicon_env::Point2d_<ContainerAllocator> >::stream(s, indent + "  ", v.noise);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "r: ";
    Printer<double>::stream(s, indent + "  ", v.r);
  }
};

} // namespace message_operations
} // namespace ros

#endif // VICON_ENV_MESSAGE_CIRCLE_H
