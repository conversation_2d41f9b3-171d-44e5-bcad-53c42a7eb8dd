// Generated by gencpp from file vicon_env/Cylinder.msg
// DO NOT EDIT!


#ifndef VICON_ENV_MESSAGE_CYLINDER_H
#define VICON_ENV_MESSAGE_CYLINDER_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <vicon_env/Point2d.h>
#include <vicon_env/Point3d.h>

namespace vicon_env
{
template <class ContainerAllocator>
struct Cylinder_
{
  typedef Cylinder_<ContainerAllocator> Type;

  Cylinder_()
    : id(0)
    , pos()
    , noise()
    , r(0.0)
    , h(0.0)  {
    }
  Cylinder_(const ContainerAllocator& _alloc)
    : id(0)
    , pos(_alloc)
    , noise(_alloc)
    , r(0.0)
    , h(0.0)  {
  (void)_alloc;
    }



   typedef int32_t _id_type;
  _id_type id;

   typedef  ::vicon_env::Point2d_<ContainerAllocator>  _pos_type;
  _pos_type pos;

   typedef  ::vicon_env::Point3d_<ContainerAllocator>  _noise_type;
  _noise_type noise;

   typedef double _r_type;
  _r_type r;

   typedef double _h_type;
  _h_type h;





  typedef boost::shared_ptr< ::vicon_env::Cylinder_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::vicon_env::Cylinder_<ContainerAllocator> const> ConstPtr;

}; // struct Cylinder_

typedef ::vicon_env::Cylinder_<std::allocator<void> > Cylinder;

typedef boost::shared_ptr< ::vicon_env::Cylinder > CylinderPtr;
typedef boost::shared_ptr< ::vicon_env::Cylinder const> CylinderConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::vicon_env::Cylinder_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::vicon_env::Cylinder_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::vicon_env::Cylinder_<ContainerAllocator1> & lhs, const ::vicon_env::Cylinder_<ContainerAllocator2> & rhs)
{
  return lhs.id == rhs.id &&
    lhs.pos == rhs.pos &&
    lhs.noise == rhs.noise &&
    lhs.r == rhs.r &&
    lhs.h == rhs.h;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::vicon_env::Cylinder_<ContainerAllocator1> & lhs, const ::vicon_env::Cylinder_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace vicon_env

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Cylinder_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::vicon_env::Cylinder_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Cylinder_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::vicon_env::Cylinder_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Cylinder_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::vicon_env::Cylinder_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::vicon_env::Cylinder_<ContainerAllocator> >
{
  static const char* value()
  {
    return "a61dd680941510aadadfbb73b1ffec95";
  }

  static const char* value(const ::vicon_env::Cylinder_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xa61dd680941510aaULL;
  static const uint64_t static_value2 = 0xdadfbb73b1ffec95ULL;
};

template<class ContainerAllocator>
struct DataType< ::vicon_env::Cylinder_<ContainerAllocator> >
{
  static const char* value()
  {
    return "vicon_env/Cylinder";
  }

  static const char* value(const ::vicon_env::Cylinder_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::vicon_env::Cylinder_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 id\n"
"Point2d pos\n"
"Point3d noise\n"
"float64 r\n"
"float64 h\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point2d\n"
"float64 x\n"
"float64 y\n"
"\n"
"================================================================================\n"
"MSG: vicon_env/Point3d\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::vicon_env::Cylinder_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::vicon_env::Cylinder_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.id);
      stream.next(m.pos);
      stream.next(m.noise);
      stream.next(m.r);
      stream.next(m.h);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Cylinder_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::vicon_env::Cylinder_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::vicon_env::Cylinder_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pos: ";
    Printer< ::vicon_env::Point2d_<ContainerAllocator> >::stream(s, indent + "  ", v.pos);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "noise: ";
    Printer< ::vicon_env::Point3d_<ContainerAllocator> >::stream(s, indent + "  ", v.noise);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "r: ";
    Printer<double>::stream(s, indent + "  ", v.r);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "h: ";
    Printer<double>::stream(s, indent + "  ", v.h);
  }
};

} // namespace message_operations
} // namespace ros

#endif // VICON_ENV_MESSAGE_CYLINDER_H
