// Generated by gencpp from file map_manager/RayCastResponse.msg
// DO NOT EDIT!


#ifndef MAP_MANAGER_MESSAGE_RAYCASTRESPONSE_H
#define MAP_MANAGER_MESSAGE_RAYCASTRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace map_manager
{
template <class ContainerAllocator>
struct RayCastResponse_
{
  typedef RayCastResponse_<ContainerAllocator> Type;

  RayCastResponse_()
    : points()  {
    }
  RayCastResponse_(const ContainerAllocator& _alloc)
    : points(_alloc)  {
  (void)_alloc;
    }



   typedef std::vector<double, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<double>> _points_type;
  _points_type points;





  typedef boost::shared_ptr< ::map_manager::RayCastResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::map_manager::RayCastResponse_<ContainerAllocator> const> ConstPtr;

}; // struct RayCastResponse_

typedef ::map_manager::RayCastResponse_<std::allocator<void> > RayCastResponse;

typedef boost::shared_ptr< ::map_manager::RayCastResponse > RayCastResponsePtr;
typedef boost::shared_ptr< ::map_manager::RayCastResponse const> RayCastResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::map_manager::RayCastResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::map_manager::RayCastResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::map_manager::RayCastResponse_<ContainerAllocator1> & lhs, const ::map_manager::RayCastResponse_<ContainerAllocator2> & rhs)
{
  return lhs.points == rhs.points;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::map_manager::RayCastResponse_<ContainerAllocator1> & lhs, const ::map_manager::RayCastResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace map_manager

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::map_manager::RayCastResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::map_manager::RayCastResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::RayCastResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::RayCastResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::RayCastResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::RayCastResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::map_manager::RayCastResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "76ce76db94343b4ddd64b9177a425ea3";
  }

  static const char* value(const ::map_manager::RayCastResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x76ce76db94343b4dULL;
  static const uint64_t static_value2 = 0xdd64b9177a425ea3ULL;
};

template<class ContainerAllocator>
struct DataType< ::map_manager::RayCastResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "map_manager/RayCastResponse";
  }

  static const char* value(const ::map_manager::RayCastResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::map_manager::RayCastResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "float64[] points\n"
;
  }

  static const char* value(const ::map_manager::RayCastResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::map_manager::RayCastResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.points);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct RayCastResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::map_manager::RayCastResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::map_manager::RayCastResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "points: ";
    if (v.points.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.points.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<double>::stream(s, true ? std::string() : indent + "    ", v.points[i]);
    }
    if (v.points.empty() || true)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // MAP_MANAGER_MESSAGE_RAYCASTRESPONSE_H
