// Generated by gencpp from file map_manager/RayCast.msg
// DO NOT EDIT!


#ifndef MAP_MANAGER_MESSAGE_RAYCAST_H
#define MAP_MANAGER_MESSAGE_RAYCAST_H

#include <ros/service_traits.h>


#include <map_manager/RayCastRequest.h>
#include <map_manager/RayCastResponse.h>


namespace map_manager
{

struct RayCast
{

typedef RayCastRequest Request;
typedef RayCastResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct RayCast
} // namespace map_manager


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::map_manager::RayCast > {
  static const char* value()
  {
    return "0c76c296d09a6c11167d8e932a6ebc50";
  }

  static const char* value(const ::map_manager::RayCast&) { return value(); }
};

template<>
struct DataType< ::map_manager::RayCast > {
  static const char* value()
  {
    return "map_manager/RayCast";
  }

  static const char* value(const ::map_manager::RayCast&) { return value(); }
};


// service_traits::MD5Sum< ::map_manager::RayCastRequest> should match
// service_traits::MD5Sum< ::map_manager::RayCast >
template<>
struct MD5Sum< ::map_manager::RayCastRequest>
{
  static const char* value()
  {
    return MD5Sum< ::map_manager::RayCast >::value();
  }
  static const char* value(const ::map_manager::RayCastRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::map_manager::RayCastRequest> should match
// service_traits::DataType< ::map_manager::RayCast >
template<>
struct DataType< ::map_manager::RayCastRequest>
{
  static const char* value()
  {
    return DataType< ::map_manager::RayCast >::value();
  }
  static const char* value(const ::map_manager::RayCastRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::map_manager::RayCastResponse> should match
// service_traits::MD5Sum< ::map_manager::RayCast >
template<>
struct MD5Sum< ::map_manager::RayCastResponse>
{
  static const char* value()
  {
    return MD5Sum< ::map_manager::RayCast >::value();
  }
  static const char* value(const ::map_manager::RayCastResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::map_manager::RayCastResponse> should match
// service_traits::DataType< ::map_manager::RayCast >
template<>
struct DataType< ::map_manager::RayCastResponse>
{
  static const char* value()
  {
    return DataType< ::map_manager::RayCast >::value();
  }
  static const char* value(const ::map_manager::RayCastResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // MAP_MANAGER_MESSAGE_RAYCAST_H
