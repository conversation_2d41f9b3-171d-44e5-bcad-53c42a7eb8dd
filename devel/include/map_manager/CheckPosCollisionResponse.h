// Generated by gencpp from file map_manager/CheckPosCollisionResponse.msg
// DO NOT EDIT!


#ifndef MAP_MANAGER_MESSAGE_CHECKPOSCOLLISIONRESPONSE_H
#define MAP_MANAGER_MESSAGE_CHECKPOSCOLLISIONRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace map_manager
{
template <class ContainerAllocator>
struct CheckPosCollisionResponse_
{
  typedef CheckPosCollisionResponse_<ContainerAllocator> Type;

  CheckPosCollisionResponse_()
    : occupied(false)  {
    }
  CheckPosCollisionResponse_(const ContainerAllocator& _alloc)
    : occupied(false)  {
  (void)_alloc;
    }



   typedef uint8_t _occupied_type;
  _occupied_type occupied;





  typedef boost::shared_ptr< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> const> ConstPtr;

}; // struct CheckPosCollisionResponse_

typedef ::map_manager::CheckPosCollisionResponse_<std::allocator<void> > CheckPosCollisionResponse;

typedef boost::shared_ptr< ::map_manager::CheckPosCollisionResponse > CheckPosCollisionResponsePtr;
typedef boost::shared_ptr< ::map_manager::CheckPosCollisionResponse const> CheckPosCollisionResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator1> & lhs, const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator2> & rhs)
{
  return lhs.occupied == rhs.occupied;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator1> & lhs, const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace map_manager

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "44d7026c7dcc8daf286cc35a49a8f442";
  }

  static const char* value(const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x44d7026c7dcc8dafULL;
  static const uint64_t static_value2 = 0x286cc35a49a8f442ULL;
};

template<class ContainerAllocator>
struct DataType< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "map_manager/CheckPosCollisionResponse";
  }

  static const char* value(const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool occupied\n"
;
  }

  static const char* value(const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.occupied);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct CheckPosCollisionResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::map_manager::CheckPosCollisionResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::map_manager::CheckPosCollisionResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "occupied: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.occupied);
  }
};

} // namespace message_operations
} // namespace ros

#endif // MAP_MANAGER_MESSAGE_CHECKPOSCOLLISIONRESPONSE_H
