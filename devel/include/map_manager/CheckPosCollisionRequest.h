// Generated by gencpp from file map_manager/CheckPosCollisionRequest.msg
// DO NOT EDIT!


#ifndef MAP_MANAGER_MESSAGE_CHECKPOSCOLLISIONREQUEST_H
#define MAP_MANAGER_MESSAGE_CHECKPOSCOLLISIONREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace map_manager
{
template <class ContainerAllocator>
struct CheckPosCollisionRequest_
{
  typedef CheckPosCollisionRequest_<ContainerAllocator> Type;

  CheckPosCollisionRequest_()
    : x(0.0)
    , y(0.0)
    , z(0.0)
    , inflated(false)  {
    }
  CheckPosCollisionRequest_(const ContainerAllocator& _alloc)
    : x(0.0)
    , y(0.0)
    , z(0.0)
    , inflated(false)  {
  (void)_alloc;
    }



   typedef double _x_type;
  _x_type x;

   typedef double _y_type;
  _y_type y;

   typedef double _z_type;
  _z_type z;

   typedef uint8_t _inflated_type;
  _inflated_type inflated;





  typedef boost::shared_ptr< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> const> ConstPtr;

}; // struct CheckPosCollisionRequest_

typedef ::map_manager::CheckPosCollisionRequest_<std::allocator<void> > CheckPosCollisionRequest;

typedef boost::shared_ptr< ::map_manager::CheckPosCollisionRequest > CheckPosCollisionRequestPtr;
typedef boost::shared_ptr< ::map_manager::CheckPosCollisionRequest const> CheckPosCollisionRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator1> & lhs, const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator2> & rhs)
{
  return lhs.x == rhs.x &&
    lhs.y == rhs.y &&
    lhs.z == rhs.z &&
    lhs.inflated == rhs.inflated;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator1> & lhs, const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace map_manager

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "4fcaedfaf867530b4bba38a583aa9a92";
  }

  static const char* value(const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x4fcaedfaf867530bULL;
  static const uint64_t static_value2 = 0x4bba38a583aa9a92ULL;
};

template<class ContainerAllocator>
struct DataType< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "map_manager/CheckPosCollisionRequest";
  }

  static const char* value(const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "float64 x\n"
"float64 y\n"
"float64 z\n"
"bool inflated\n"
;
  }

  static const char* value(const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.x);
      stream.next(m.y);
      stream.next(m.z);
      stream.next(m.inflated);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct CheckPosCollisionRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::map_manager::CheckPosCollisionRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::map_manager::CheckPosCollisionRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "x: ";
    Printer<double>::stream(s, indent + "  ", v.x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "y: ";
    Printer<double>::stream(s, indent + "  ", v.y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "z: ";
    Printer<double>::stream(s, indent + "  ", v.z);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "inflated: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.inflated);
  }
};

} // namespace message_operations
} // namespace ros

#endif // MAP_MANAGER_MESSAGE_CHECKPOSCOLLISIONREQUEST_H
