// Generated by gencpp from file map_manager/RayCastRequest.msg
// DO NOT EDIT!


#ifndef MAP_MANAGER_MESSAGE_RAYCASTREQUEST_H
#define MAP_MANAGER_MESSAGE_RAYCASTREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>

namespace map_manager
{
template <class ContainerAllocator>
struct RayCastRequest_
{
  typedef RayCastRequest_<ContainerAllocator> Type;

  RayCastRequest_()
    : position()
    , startAngle(0.0)
    , range(0.0)
    , vfov_min(0.0)
    , vfov_max(0.0)
    , vbeams(0)
    , hres(0.0)  {
    }
  RayCastRequest_(const ContainerAllocator& _alloc)
    : position(_alloc)
    , startAngle(0.0)
    , range(0.0)
    , vfov_min(0.0)
    , vfov_max(0.0)
    , vbeams(0)
    , hres(0.0)  {
  (void)_alloc;
    }



   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _position_type;
  _position_type position;

   typedef double _startAngle_type;
  _startAngle_type startAngle;

   typedef double _range_type;
  _range_type range;

   typedef double _vfov_min_type;
  _vfov_min_type vfov_min;

   typedef double _vfov_max_type;
  _vfov_max_type vfov_max;

   typedef int32_t _vbeams_type;
  _vbeams_type vbeams;

   typedef double _hres_type;
  _hres_type hres;





  typedef boost::shared_ptr< ::map_manager::RayCastRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::map_manager::RayCastRequest_<ContainerAllocator> const> ConstPtr;

}; // struct RayCastRequest_

typedef ::map_manager::RayCastRequest_<std::allocator<void> > RayCastRequest;

typedef boost::shared_ptr< ::map_manager::RayCastRequest > RayCastRequestPtr;
typedef boost::shared_ptr< ::map_manager::RayCastRequest const> RayCastRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::map_manager::RayCastRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::map_manager::RayCastRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::map_manager::RayCastRequest_<ContainerAllocator1> & lhs, const ::map_manager::RayCastRequest_<ContainerAllocator2> & rhs)
{
  return lhs.position == rhs.position &&
    lhs.startAngle == rhs.startAngle &&
    lhs.range == rhs.range &&
    lhs.vfov_min == rhs.vfov_min &&
    lhs.vfov_max == rhs.vfov_max &&
    lhs.vbeams == rhs.vbeams &&
    lhs.hres == rhs.hres;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::map_manager::RayCastRequest_<ContainerAllocator1> & lhs, const ::map_manager::RayCastRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace map_manager

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::map_manager::RayCastRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::map_manager::RayCastRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::RayCastRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::map_manager::RayCastRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::RayCastRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::map_manager::RayCastRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::map_manager::RayCastRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "14618c047ed1a962ef2052a624965300";
  }

  static const char* value(const ::map_manager::RayCastRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x14618c047ed1a962ULL;
  static const uint64_t static_value2 = 0xef2052a624965300ULL;
};

template<class ContainerAllocator>
struct DataType< ::map_manager::RayCastRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "map_manager/RayCastRequest";
  }

  static const char* value(const ::map_manager::RayCastRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::map_manager::RayCastRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "geometry_msgs/Point position\n"
"float64 startAngle\n"
"float64 range\n"
"float64 vfov_min\n"
"float64 vfov_max\n"
"int32 vbeams\n"
"float64 hres\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::map_manager::RayCastRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::map_manager::RayCastRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.position);
      stream.next(m.startAngle);
      stream.next(m.range);
      stream.next(m.vfov_min);
      stream.next(m.vfov_max);
      stream.next(m.vbeams);
      stream.next(m.hres);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct RayCastRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::map_manager::RayCastRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::map_manager::RayCastRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "position: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.position);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "startAngle: ";
    Printer<double>::stream(s, indent + "  ", v.startAngle);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "range: ";
    Printer<double>::stream(s, indent + "  ", v.range);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "vfov_min: ";
    Printer<double>::stream(s, indent + "  ", v.vfov_min);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "vfov_max: ";
    Printer<double>::stream(s, indent + "  ", v.vfov_max);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "vbeams: ";
    Printer<int32_t>::stream(s, indent + "  ", v.vbeams);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "hres: ";
    Printer<double>::stream(s, indent + "  ", v.hres);
  }
};

} // namespace message_operations
} // namespace ros

#endif // MAP_MANAGER_MESSAGE_RAYCASTREQUEST_H
