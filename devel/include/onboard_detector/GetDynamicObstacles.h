// Generated by gencpp from file onboard_detector/GetDynamicObstacles.msg
// DO NOT EDIT!


#ifndef ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLES_H
#define ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLES_H

#include <ros/service_traits.h>


#include <onboard_detector/GetDynamicObstaclesRequest.h>
#include <onboard_detector/GetDynamicObstaclesResponse.h>


namespace onboard_detector
{

struct GetDynamicObstacles
{

typedef GetDynamicObstaclesRequest Request;
typedef GetDynamicObstaclesResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetDynamicObstacles
} // namespace onboard_detector


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::onboard_detector::GetDynamicObstacles > {
  static const char* value()
  {
    return "a508c5cda827e9832476a1de6a1e2f79";
  }

  static const char* value(const ::onboard_detector::GetDynamicObstacles&) { return value(); }
};

template<>
struct DataType< ::onboard_detector::GetDynamicObstacles > {
  static const char* value()
  {
    return "onboard_detector/GetDynamicObstacles";
  }

  static const char* value(const ::onboard_detector::GetDynamicObstacles&) { return value(); }
};


// service_traits::MD5Sum< ::onboard_detector::GetDynamicObstaclesRequest> should match
// service_traits::MD5Sum< ::onboard_detector::GetDynamicObstacles >
template<>
struct MD5Sum< ::onboard_detector::GetDynamicObstaclesRequest>
{
  static const char* value()
  {
    return MD5Sum< ::onboard_detector::GetDynamicObstacles >::value();
  }
  static const char* value(const ::onboard_detector::GetDynamicObstaclesRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::onboard_detector::GetDynamicObstaclesRequest> should match
// service_traits::DataType< ::onboard_detector::GetDynamicObstacles >
template<>
struct DataType< ::onboard_detector::GetDynamicObstaclesRequest>
{
  static const char* value()
  {
    return DataType< ::onboard_detector::GetDynamicObstacles >::value();
  }
  static const char* value(const ::onboard_detector::GetDynamicObstaclesRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::onboard_detector::GetDynamicObstaclesResponse> should match
// service_traits::MD5Sum< ::onboard_detector::GetDynamicObstacles >
template<>
struct MD5Sum< ::onboard_detector::GetDynamicObstaclesResponse>
{
  static const char* value()
  {
    return MD5Sum< ::onboard_detector::GetDynamicObstacles >::value();
  }
  static const char* value(const ::onboard_detector::GetDynamicObstaclesResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::onboard_detector::GetDynamicObstaclesResponse> should match
// service_traits::DataType< ::onboard_detector::GetDynamicObstacles >
template<>
struct DataType< ::onboard_detector::GetDynamicObstaclesResponse>
{
  static const char* value()
  {
    return DataType< ::onboard_detector::GetDynamicObstacles >::value();
  }
  static const char* value(const ::onboard_detector::GetDynamicObstaclesResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLES_H
