// Generated by gencpp from file onboard_detector/GetDynamicObstaclesResponse.msg
// DO NOT EDIT!


#ifndef ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLESRESPONSE_H
#define ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLESRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Vector3.h>

namespace onboard_detector
{
template <class ContainerAllocator>
struct GetDynamicObstaclesResponse_
{
  typedef GetDynamicObstaclesResponse_<ContainerAllocator> Type;

  GetDynamicObstaclesResponse_()
    : position()
    , velocity()
    , size()  {
    }
  GetDynamicObstaclesResponse_(const ContainerAllocator& _alloc)
    : position(_alloc)
    , velocity(_alloc)
    , size(_alloc)  {
  (void)_alloc;
    }



   typedef std::vector< ::geometry_msgs::Vector3_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Vector3_<ContainerAllocator> >> _position_type;
  _position_type position;

   typedef std::vector< ::geometry_msgs::Vector3_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Vector3_<ContainerAllocator> >> _velocity_type;
  _velocity_type velocity;

   typedef std::vector< ::geometry_msgs::Vector3_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Vector3_<ContainerAllocator> >> _size_type;
  _size_type size;





  typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetDynamicObstaclesResponse_

typedef ::onboard_detector::GetDynamicObstaclesResponse_<std::allocator<void> > GetDynamicObstaclesResponse;

typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesResponse > GetDynamicObstaclesResponsePtr;
typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesResponse const> GetDynamicObstaclesResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator1> & lhs, const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator2> & rhs)
{
  return lhs.position == rhs.position &&
    lhs.velocity == rhs.velocity &&
    lhs.size == rhs.size;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator1> & lhs, const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace onboard_detector

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "5751cdcce82bba0007c77d0aa5f1b7cc";
  }

  static const char* value(const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x5751cdcce82bba00ULL;
  static const uint64_t static_value2 = 0x07c77d0aa5f1b7ccULL;
};

template<class ContainerAllocator>
struct DataType< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "onboard_detector/GetDynamicObstaclesResponse";
  }

  static const char* value(const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "geometry_msgs/Vector3[] position\n"
"geometry_msgs/Vector3[] velocity\n"
"geometry_msgs/Vector3[] size\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.position);
      stream.next(m.velocity);
      stream.next(m.size);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetDynamicObstaclesResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::onboard_detector::GetDynamicObstaclesResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "position: ";
    if (v.position.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.position.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.position[i]);
    }
    if (v.position.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "velocity: ";
    if (v.velocity.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.velocity.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.velocity[i]);
    }
    if (v.velocity.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "size: ";
    if (v.size.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.size.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Vector3_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.size[i]);
    }
    if (v.size.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLESRESPONSE_H
