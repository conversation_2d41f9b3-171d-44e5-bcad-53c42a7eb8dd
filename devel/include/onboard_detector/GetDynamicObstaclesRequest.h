// Generated by gencpp from file onboard_detector/GetDynamicObstaclesRequest.msg
// DO NOT EDIT!


#ifndef ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLESREQUEST_H
#define ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLESREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>

namespace onboard_detector
{
template <class ContainerAllocator>
struct GetDynamicObstaclesRequest_
{
  typedef GetDynamicObstaclesRequest_<ContainerAllocator> Type;

  GetDynamicObstaclesRequest_()
    : current_position()
    , range(0.0)  {
    }
  GetDynamicObstaclesRequest_(const ContainerAllocator& _alloc)
    : current_position(_alloc)
    , range(0.0)  {
  (void)_alloc;
    }



   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _current_position_type;
  _current_position_type current_position;

   typedef double _range_type;
  _range_type range;





  typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetDynamicObstaclesRequest_

typedef ::onboard_detector::GetDynamicObstaclesRequest_<std::allocator<void> > GetDynamicObstaclesRequest;

typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesRequest > GetDynamicObstaclesRequestPtr;
typedef boost::shared_ptr< ::onboard_detector::GetDynamicObstaclesRequest const> GetDynamicObstaclesRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator1> & lhs, const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator2> & rhs)
{
  return lhs.current_position == rhs.current_position &&
    lhs.range == rhs.range;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator1> & lhs, const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace onboard_detector

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "2d85907f898a3f1571d2bcd6d1effd72";
  }

  static const char* value(const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x2d85907f898a3f15ULL;
  static const uint64_t static_value2 = 0x71d2bcd6d1effd72ULL;
};

template<class ContainerAllocator>
struct DataType< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "onboard_detector/GetDynamicObstaclesRequest";
  }

  static const char* value(const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "geometry_msgs/Point current_position\n"
"float64 range\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.current_position);
      stream.next(m.range);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetDynamicObstaclesRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::onboard_detector::GetDynamicObstaclesRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "current_position: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.current_position);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "range: ";
    Printer<double>::stream(s, indent + "  ", v.range);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ONBOARD_DETECTOR_MESSAGE_GETDYNAMICOBSTACLESREQUEST_H
